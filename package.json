{"name": "my-invoice", "version": "5.0.2", "bin": {"next-forge": "dist/index.js"}, "files": ["dist/index.js"], "scripts": {"build": "turbo build", "lint": "ultracite lint", "dev": "turbo dev", "portal": "turbo dev --filter=portal", "fumadocs": "turbo dev --filter=docs", "format": "ultracite format", "test": "turbo test", "analyze": "turbo analyze", "translate": "turbo translate", "boundaries": "turbo boundaries", "bump-deps": "npx npm-check-updates --deep -u -x react-day-picker", "bump-ui": "npx shadcn@latest add --all --overwrite -c packages/design-system", "better-auth": "npx @better-auth/cli generate --output ./packages/database/prisma/schema.prisma --config ./packages/auth/server.ts", "prisma": "cd packages/database && npx prisma format && npx prisma generate", "prisma-reset": "cd packages/database && npx prisma migrate reset", "migrate": "cd packages/database && npx prisma format && npx prisma generate && npx prisma migrate dev", "migrate-reset": "cd packages/database && npx prisma db push --force-reset", "migrate-prod": "cd packages/database && npx prisma format && npx prisma generate && npx prisma db push", "seed": "cd packages/database && npx prisma db seed", "clean": "git clean -xdf node_modules", "postinstall": "cd packages/database && npx prisma generate"}, "devDependencies": {"@auto-it/first-time-contributor": "^11.3.0", "@biomejs/biome": "1.9.4", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^2.5.3", "@types/node": "^22.15.21", "tsup": "^8.5.0", "turbo": "^2.5.3", "typescript": "^5.8.3", "ultracite": "^4.2.5", "vitest": "^3.1.4"}, "engines": {"node": ">=18"}, "packageManager": "pnpm@10.11.0", "dependencies": {"@clack/prompts": "^0.11.0", "commander": "^14.0.0"}, "type": "module"}