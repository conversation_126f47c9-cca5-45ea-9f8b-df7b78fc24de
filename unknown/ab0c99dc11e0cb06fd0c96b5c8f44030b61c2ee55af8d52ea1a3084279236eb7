'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Form,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { toast } from '@repo/design-system/components/ui/sonner';
import { PlusCircle } from 'lucide-react';
import { useEffect } from 'react';
import type { z } from 'zod';

import { lineItemsSchema } from '../../schemas/order-form-schema';
import { useOrderFormStore } from '../../store/order-form-store';
import { InvoiceLevelAllowanceCharge } from './invoice-level-allowance-charge';
import { LineItemForm } from './line-item';

type LineItemsData = z.infer<typeof lineItemsSchema>;

interface LineItemsStepProps {
  onNext: () => void;
  onBack: () => void;
}

export function LineItemsStep({ onNext, onBack }: LineItemsStepProps) {
  const { formData, updateFormData, setLineItemsComplete } =
    useOrderFormStore();

  // Initialize form with values from store
  const form = useForm<LineItemsData>({
    resolver: zodResolver(lineItemsSchema),
    defaultValues: {
      lineItems: formData.lineItems,
    },
  });

  // Handle form submission
  const onSubmit = (data: LineItemsData) => {
    updateFormData(data);
    setLineItemsComplete(true);
    onNext();
  };

  // Update completion status when form becomes valid
  useEffect(() => {
    const subscription = form.watch(() => {
      setLineItemsComplete(form.formState.isValid);
    });
    return () => subscription.unsubscribe();
  }, [form, setLineItemsComplete]);

  // Add a new line item
  const addLineItem = () => {
    const currentLineItems = form.getValues('lineItems');
    form.setValue('lineItems', [
      ...currentLineItems,
      {
        id: '',
        classifications: [],
        description: '',
        unit: {
          price: 0,
          count: 1,
          code: undefined,
        },
        taxDetails: [
          {
            taxType: '06', // Default to Not Applicable
            taxRate: {
              percentage: 0,
            },
          },
        ],
        allowanceCharges: [],
        tarriffCode: '',
        originCountry: 'MYS', // Default to Malaysia
      },
    ]);
  };

  // Remove a line item
  const removeLineItem = (index: number) => {
    const currentLineItems = form.getValues('lineItems');
    if (currentLineItems.length > 1) {
      form.setValue(
        'lineItems',
        currentLineItems.filter((_, i) => i !== index)
      );
    } else {
      toast.error('At least one line item is required');
    }
  };

  // Add a tax detail to a line item
  const addTaxDetail = (lineItemIndex: number) => {
    const currentLineItems = form.getValues('lineItems');
    const currentTaxDetails = currentLineItems[lineItemIndex].taxDetails;

    const updatedLineItems = [...currentLineItems];
    updatedLineItems[lineItemIndex] = {
      ...updatedLineItems[lineItemIndex],
      taxDetails: [
        ...currentTaxDetails,
        {
          taxType: '06', // Default to Not Applicable
          taxRate: {
            percentage: 0,
          },
        },
      ],
    };

    form.setValue('lineItems', updatedLineItems);
  };

  // Remove a tax detail from a line item
  const removeTaxDetail = (lineItemIndex: number, taxDetailIndex: number) => {
    const currentLineItems = form.getValues('lineItems');
    const currentTaxDetails = currentLineItems[lineItemIndex].taxDetails;

    if (currentTaxDetails.length > 1) {
      const updatedLineItems = [...currentLineItems];
      updatedLineItems[lineItemIndex] = {
        ...updatedLineItems[lineItemIndex],
        taxDetails: currentTaxDetails.filter((_, i) => i !== taxDetailIndex),
      };

      form.setValue('lineItems', updatedLineItems);
    } else {
      toast.error('At least one tax detail is required');
    }
  };

  // Add an allowance/charge to a line item
  const addAllowanceCharge = (lineItemIndex: number) => {
    const currentLineItems = form.getValues('lineItems');
    const currentAllowanceCharges =
      currentLineItems[lineItemIndex].allowanceCharges || [];

    const updatedLineItems = [...currentLineItems];
    updatedLineItems[lineItemIndex] = {
      ...updatedLineItems[lineItemIndex],
      allowanceCharges: [
        ...currentAllowanceCharges,
        {
          rate: 0,
          reason: '',
          isCharge: false,
        },
      ],
    };

    form.setValue('lineItems', updatedLineItems);
  };

  // Remove an allowance/charge from a line item
  const removeAllowanceCharge = (
    lineItemIndex: number,
    allowanceChargeIndex: number
  ) => {
    const currentLineItems = form.getValues('lineItems');
    const currentAllowanceCharges =
      currentLineItems[lineItemIndex].allowanceCharges || [];

    const updatedLineItems = [...currentLineItems];
    updatedLineItems[lineItemIndex] = {
      ...updatedLineItems[lineItemIndex],
      allowanceCharges: currentAllowanceCharges.filter(
        (_, i) => i !== allowanceChargeIndex
      ),
    };

    form.setValue('lineItems', updatedLineItems);
  };

  // TODO: figure out how to prevent nested card in UI
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Line Items</CardTitle>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addLineItem}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Line Item
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {form.watch('lineItems').map((_, lineItemIndex) => (
                <LineItemForm
                  key={lineItemIndex}
                  index={lineItemIndex}
                  onRemove={removeLineItem}
                  addTaxDetail={addTaxDetail}
                  removeTaxDetail={removeTaxDetail}
                  addAllowanceCharge={addAllowanceCharge}
                  removeAllowanceCharge={removeAllowanceCharge}
                />
              ))}
            </div>
            <div>
              <InvoiceLevelAllowanceCharge />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button type="submit">Next Step</Button>
        </div>
      </form>
    </Form>
  );
}
