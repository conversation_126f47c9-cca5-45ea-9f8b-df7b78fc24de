import { fetchWithAuthForProxy, withApiKeyAuth } from '@/app/lib/auth';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { type NextRequest, NextResponse } from 'next/server';

const getHandler = async (request: NextRequest) => {
  const searchParams = Object.fromEntries(
    request.nextUrl.searchParams.entries()
  );

  const targetUrl =
    Object.keys(searchParams).length > 0
      ? urlSerialize('/api/v1/companies', searchParams)
      : '/api/v1/companies';

  const { data, status } = await fetchWithAuthForProxy(
    targetUrl,
    {},
    request.headers
  );

  return NextResponse.json(data, {
    status,
  });
};

const putHandler = async (request: NextRequest) => {
  let body: unknown;
  try {
    body = await request.json();
  } catch (_) {
    return NextResponse.json(
      { error: 'Failed to parse request body' },
      { status: 400 }
    );
  }

  const { data, status } = await fetchWithAuthForProxy(
    '/api/v1/companies',
    {
      method: 'PUT',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    },
    request.headers
  );

  return NextResponse.json(data, {
    status,
  });
};

export const OPTIONS = withApiKeyAuth(getHandler);
export const GET = withApiKeyAuth(getHandler);
export const PUT = withApiKeyAuth(putHandler);
