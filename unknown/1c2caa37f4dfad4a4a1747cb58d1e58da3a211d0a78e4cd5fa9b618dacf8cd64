'use server';

import { ApiEndpoint, fetchWithAuth } from '@/app/lib/api';
import type { Company } from '@/app/types';
import { getJwtFromSession } from '@/app/utils/auth-helpers';
import { log } from '@repo/observability/log';

/**
 * Checks if the current user has completed onboarding by fetching their companies from the core backend
 * @returns {Promise<boolean>} True if the user has at least one company registered, false otherwise
 */
export const isOnboardingComplete = async (): Promise<boolean> => {
  try {
    try {
      const company = await fetchWithAuth<{
        data: Company;
      }>(ApiEndpoint.ME_COMPANIES, await getJwtFromSession(), {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      log.info('Company', { company });

      // User has completed onboarding if they have at least one company
      return company != null;
    } catch (error) {
      log.warn('Failed to fetch companies from core backend', { error });
      return false;
    }
  } catch (error) {
    log.warn('Error checking onboarding completion', { error });
    return false;
  }
};
