---
title: Data Models
description: Understand the data structures used in the BizCare MyInvois API
---

# Data Models

This section describes all the data models and schemas used in the BizCare MyInvois API. Understanding these structures is essential for proper integration.

## Core Models

### Order

Represents an invoice order that can be submitted to MyInvois.

```json
{
  "id": 551,
  "company_id": 173,
  "user_id": 253,
  "invoice_code": "INV-2024-001",
  "is_submitted_to_lhdn": false,
  "is_ready": true,
  "is_consolidate": false,
  "status": "Draft",
  "external_id": "EXT-001",
  "billing_reference_number": "BR-2024-001",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "buyer": { /* Buyer object */ },
  "supplier": { /* Supplier object */ },
  "delivery_details": { /* DeliveryDetails object */ },
  "invoice_date_time": { /* InvoiceDateTime object */ },
  "foreign_currency": { /* ForeignCurrency object */ },
  "billing_period": { /* BillingPeriod object */ },
  "line_items": [ /* Array of LineItem objects */ ],
  "payment": { /* Payment object */ },
  "pre_payment": { /* Prepayment object */ },
  "legal_monetary_total": { /* LegalMonetaryTotal object */ },
  "invoice_level_line_item_taxes": { /* InvoiceLevelLineItemTaxes object */ },
  "invoice_level_allowance_charge": { /* InvoiceLevelAllowanceCharge object */ },
  "additional_document_reference": [ /* Array of AdditionalDocumentReference objects */ ],
  "submitted_documents": [ /* Array of SubmittedDocument objects */ ]
}
```

#### Order Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | integer | - | Unique order identifier |
| `company_id` | integer | Yes | Company that owns this order |
| `user_id` | integer | Yes | User who created this order |
| `invoice_code` | string | Yes | Unique invoice code (cannot be repeated) |
| `is_submitted_to_lhdn` | boolean | - | Whether submitted to MyInvois |
| `is_ready` | boolean | - | Whether order is ready for submission |
| `is_consolidate` | boolean | - | Whether this is a consolidated invoice |
| `status` | string | - | Current order status |
| `external_id` | string | No | External system reference |
| `billing_reference_number` | string | No | Billing reference number |

---

### Company

Represents a company registered in the system.

```json
{
  "id": 173,
  "user_id": 253,
  "name": "Your Company Sdn Bhd",
  "tin_code": "C25845632020",
  "registration_number": "************",
  "registration_type": "BRN",
  "sst_registration_number": "A12-3456-********",
  "tourism_tax_registration_number": "TTX-*********",
  "business_activity_description": "Software Development",
  "msic_code": "62010",
  "country": "Malaysia",
  "state": "Selangor",
  "city": "Cyberjaya",
  "zip_code": "63000",
  "address": "123 Technology Park",
  "phone": "+60*********",
  "email": "<EMAIL>",
  "bank_account": "*********0",
  "exporter_certified_number": null,
  "applicable_tax_types": ["01", "02"],
  "sales_tax_rates": ["5%", "10%"],
  "service_tax_rates": ["6%", "8%"],
  "client_id": "myinvois-client-id",
  "client_secret": "myinvois-client-secret",
  "scope": "InvoicingAPI",
  "access_token": "current-token",
  "token_expires_in": 3600,
  "token_expires_at": "2024-01-15T18:00:00Z",
  "is_company_ready": true,
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-15T14:00:00Z"
}
```

#### Company Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | Yes | Company name or individual name |
| `tin_code` | string | No | Tax Identification Number (e.g., C25845632020) |
| `registration_number` | string | No | Business registration number |
| `registration_type` | string | No | Type of registration |
| `sst_registration_number` | string | No | SST registration number |
| `tourism_tax_registration_number` | string | No | Tourism tax registration |
| `business_activity_description` | string | No | Business activity description |
| `msic_code` | string | No | Malaysian Standard Industrial Classification |
| `applicable_tax_types` | array | No | Array of applicable tax type codes |
| `sales_tax_rates` | array | No | Array of sales tax rates |
| `service_tax_rates` | array | No | Array of service tax rates |
| `client_id` | string | No | MyInvois API client ID |
| `client_secret` | string | No | MyInvois API client secret |
| `is_company_ready` | boolean | - | Whether company setup is complete |

---

### User

Represents a user in the system.

```json
{
  "id": 916,
  "full_name": "John Doe",
  "email": "<EMAIL>",
  "created_at": "2024-01-01T10:00:00Z",
  "updated_at": "2024-01-15T14:00:00Z"
}
```

#### User Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `full_name` | string | Yes | User's full name |
| `email` | string | Yes | User's email address |

---

### SubmittedDocument

Represents a document that has been submitted to MyInvois.

```json
{
  "id": 153,
  "document_submission_id": 147,
  "order_id": 397,
  "company_id": 670,
  "user_id": 382,
  "code": "INV-2024-001",
  "uuid": "550e8400-e29b-41d4-a716-************",
  "status": "Submitted",
  "type": "Invoice",
  "fail_reason": null,
  "fail_details": [],
  "cancel_reason": null,
  "long_id": "LHDN-generated-long-id",
  "document_details": { /* UBL format snapshot */ },
  "created_at": "2024-01-15T13:00:00Z",
  "updated_at": "2024-01-15T13:00:00Z"
}
```

#### SubmittedDocument Fields

| Field | Type | Description |
|-------|------|-------------|
| `code` | string | Document code (invoice code, credit note code, etc.) |
| `uuid` | string | MyInvois-generated UUID |
| `status` | string | Document status (Submitted, Valid, Invalid, etc.) |
| `type` | string | Document type (Invoice, Credit Note, etc.) |
| `fail_reason` | string | Reason for failure if status is Invalid |
| `fail_details` | array | Detailed failure information |
| `cancel_reason` | string | Reason for cancellation if applicable |
| `long_id` | string | MyInvois-generated long ID |
| `document_details` | object | UBL format snapshot of the document |

#### Document Status Values

| Status | Description |
|--------|-------------|
| `Submitted` | Document submitted to MyInvois |
| `Valid` | Document validated by MyInvois |
| `Invalid` | Document rejected by MyInvois |
| `Cancelled` | Document cancelled |
| `Pending` | Document pending validation |
| `InvalidOrder` | Order data is invalid |

---

### DocumentSubmission

Represents a batch submission of documents.

```json
{
  "id": 940,
  "user_id": 866,
  "company_id": 590,
  "submission_uid": "batch-submission-uuid",
  "total_documents": 5,
  "created_at": "2024-01-15T13:00:00Z",
  "updated_at": "2024-01-15T13:00:00Z",
  "submitted_documents": [ /* Array of SubmittedDocument objects */ ]
}
```

---

## Invoice Components

### Buyer

Represents the buyer/customer information.

```json
{
  "name": "Customer Company Sdn Bhd",
  "tin": "C*********01",
  "registration_number": "************",
  "address": "123 Customer Street",
  "city": "Kuala Lumpur",
  "state": "Selangor",
  "zip_code": "50000",
  "country": "Malaysia",
  "phone": "+60*********",
  "email": "<EMAIL>"
}
```

### Supplier

Represents the supplier/seller information (usually your company).

```json
{
  "name": "Your Company Sdn Bhd",
  "tin": "C98765432109",
  "registration_number": "************",
  "address": "456 Business Street",
  "city": "Cyberjaya",
  "state": "Selangor",
  "zip_code": "63000",
  "country": "Malaysia",
  "phone": "+***********",
  "email": "<EMAIL>"
}
```

### LineItem

Represents an individual line item in an invoice.

```json
{
  "description": "Professional Consulting Services",
  "quantity": 10,
  "unit_price": 150.00,
  "unit_code": "HUR",
  "classification_code": "003",
  "tax_type": "02",
  "tax_rate": 8.00,
  "tax_amount": 120.00,
  "discount_amount": 0.00,
  "total_amount": 1620.00,
  "line_extension_amount": 1500.00
}
```

#### LineItem Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `description` | string | Yes | Item description |
| `quantity` | number | Yes | Quantity of items |
| `unit_price` | number | Yes | Price per unit |
| `unit_code` | string | Yes | Unit of measure code |
| `classification_code` | string | Yes | Product/service classification |
| `tax_type` | string | Yes | Tax type code |
| `tax_rate` | number | Yes | Tax rate percentage |
| `tax_amount` | number | Yes | Total tax amount |
| `total_amount` | number | Yes | Total line amount including tax |

### LegalMonetaryTotal

Represents the monetary totals for the invoice.

```json
{
  "line_extension_amount": 1500.00,
  "tax_exclusive_amount": 1500.00,
  "tax_inclusive_amount": 1620.00,
  "allowance_total_amount": 0.00,
  "charge_total_amount": 0.00,
  "payable_rounding_amount": 0.00,
  "payable_amount": 1620.00
}
```

#### LegalMonetaryTotal Fields

| Field | Type | Description |
|-------|------|-------------|
| `line_extension_amount` | number | Total of all line items before tax |
| `tax_exclusive_amount` | number | Total amount excluding tax |
| `tax_inclusive_amount` | number | Total amount including tax |
| `allowance_total_amount` | number | Total allowances/discounts |
| `charge_total_amount` | number | Total additional charges |
| `payable_rounding_amount` | number | Rounding adjustment |
| `payable_amount` | number | Final payable amount |

### InvoiceDateTime

Represents date and time information for the invoice.

```json
{
  "invoice_date": "2024-01-15",
  "invoice_time": "10:30:00",
  "invoice_date_time": "2024-01-15T10:30:00Z",
  "due_date": "2024-02-14"
}
```

### Payment

Represents payment information and terms.

```json
{
  "payment_mode": "01",
  "payment_terms": "Net 30 days",
  "payment_amount": 1620.00,
  "payment_date": "2024-02-14",
  "payment_reference": "PAY-2024-001",
  "bank_account_number": "*********0"
}
```

#### Payment Mode Codes

| Code | Description |
|------|-------------|
| `01` | Cash |
| `02` | Cheque |
| `03` | Bank Transfer |
| `04` | Credit Card |
| `05` | Debit Card |
| `06` | E-Wallet |
| `07` | Others |

### DeliveryDetails

Represents delivery information for goods.

```json
{
  "delivery_party_name": "Delivery Company Sdn Bhd",
  "delivery_date": "2024-01-20",
  "delivery_address": "789 Delivery Street",
  "delivery_city": "Petaling Jaya",
  "delivery_state": "Selangor",
  "delivery_zip_code": "47800",
  "delivery_country": "Malaysia"
}
```

### ForeignCurrency

Represents foreign currency information when applicable.

```json
{
  "currency_code": "USD",
  "currency_exchange_rate": 4.65,
  "frequency_of_billing": "Monthly",
  "billing_period_start": "2024-01-01",
  "billing_period_end": "2024-01-31"
}
```

### BillingPeriod

Represents billing period for recurring services.

```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "description": "Monthly billing period for January 2024"
}
```

### AdditionalDocumentReference

Represents references to additional documents.

```json
{
  "document_type": "Contract",
  "document_reference": "CONTRACT-2024-001",
  "document_description": "Service Agreement",
  "attachment_url": "https://example.com/documents/contract.pdf"
}
```

---

## Validation Schemas

### Tax Type Codes

| Code | Description | Rate Options |
|------|-------------|--------------|
| `01` | Sales Tax | 5%, 10% |
| `02` | Service Tax | 6%, 8% |
| `03` | Tourism Tax | Fixed RM 10 |
| `04` | High-Value Goods Tax | Variable |
| `05` | Sales Tax on Low Value Goods | 10% |
| `06` | Not Applicable | 0% |
| `E` | Tax Exemption | 0% |

### Unit Codes (Common)

| Code | Description |
|------|-------------|
| `C62` | Piece/Unit |
| `HUR` | Hour |
| `DAY` | Day |
| `MON` | Month |
| `KGM` | Kilogram |
| `LTR` | Liter |
| `MTR` | Meter |
| `SET` | Set |
| `LOT` | Lot |

### Classification Codes (Common)

| Code | Description |
|------|-------------|
| `001` | Goods |
| `002` | Services |
| `003` | Professional Services |
| `004` | Consulting |
| `005` | Software |
| `006` | Hardware |
| `007` | Maintenance |
| `008` | Training |
| `009` | Support |

### Registration Types

| Code | Description |
|------|-------------|
| `BRN` | Business Registration Number |
| `NRIC` | National Registration Identity Card |
| `PASSPORT` | Passport Number |
| `ARMY` | Army ID |
| `TIN` | Tax Identification Number |

---

## Pagination Meta

Used in list responses for pagination information.

```json
{
  "total": 100,
  "page": 2,
  "perPage": 10,
  "currentPage": 2,
  "lastPage": 10,
  "firstPage": 1,
  "lastPageUrl": "/?page=10",
  "firstPageUrl": "/?page=1",
  "nextPageUrl": "/?page=3",
  "previousPageUrl": "/?page=1"
}
```

## Important Notes

<Callout type="info">
  **Required Fields**: Fields marked as "Required" must be included when creating or updating records.
</Callout>

<Callout type="warning">
  **Data Validation**: All monetary amounts should be provided with 2 decimal places. Tax calculations must be accurate.
</Callout>

<Callout type="tip">
  **Classification Codes**: Use appropriate classification codes for your products/services to ensure proper tax treatment.
</Callout>
