'use client';

import { api<PERSON><PERSON>, organization } from '@repo/auth/client';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@repo/design-system/components/ui/alert-dialog';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';
import { formatDateTime } from '@repo/design-system/lib/format';
import { Copy, Loader2, Plus, Trash } from 'lucide-react';
import { useEffect, useState } from 'react';
import type React from 'react';
import { z } from 'zod';

// Schema for API key creation form
const apiKeySchema = z.object({
  name: z.string().min(1, 'Name is required'),
});

type ApiKeyFormValues = z.infer<typeof apiKeySchema>;
type ApiKey = {
  id: string;
  name: string | null;
  start: string | null;
  prefix: string | null;
  userId: string;
  refillInterval: number | null;
  refillAmount: number | null;
  lastRefillAt: Date | null;
  lastRequest: Date | null;
  createdAt: Date;
  expiresAt: Date | null;
};

export function ApiKeysForm() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [deleteApiKeyId, setDeleteApiKeyId] = useState<string | null>(null);

  // Form for creating a new API key
  const form = useForm<ApiKeyFormValues>({
    resolver: zodResolver(apiKeySchema),
    defaultValues: {
      name: '',
    },
  });

  // Load API keys on component mount
  useEffect(() => {
    const fetchApiKeys = async () => {
      try {
        setIsLoading(true);
        const { data, error } = await apiKey.list();

        if (error) {
          throw new Error(error.message);
        }

        setApiKeys(data || []);
      } catch (error) {
        toast.error('Failed to load API keys');
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchApiKeys();
  }, []);

  // Handle API key creation
  const onSubmit = async (values: ApiKeyFormValues) => {
    try {
      setIsCreating(true);

      const findOrganization = await organization.getFullOrganization();

      const { data, error } = await apiKey.create({
        name: values.name,
        metadata: {
          organizationId: findOrganization.data?.id,
          organizationName: findOrganization.data?.name,
          organizationSlug: findOrganization.data?.slug,
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      setApiKeys([data, ...apiKeys]);
      setNewApiKey(data.key);
      form.reset();
      setCreateDialogOpen(false);
    } catch (error) {
      toast.error('Failed to create API key');
      console.error(error);
    } finally {
      setIsCreating(false);
    }
  };

  // Handle API key deletion
  const handleDeleteApiKey = async (id: string) => {
    try {
      setIsDeleting(true);
      const { error } = await apiKey.delete({
        keyId: id,
      });

      if (error) {
        throw new Error(error.message);
      }

      setApiKeys(apiKeys.filter((key: { id: string }) => key.id !== id));
      toast.success('API key deleted');
    } catch (error) {
      toast.error('Failed to delete API key');
      console.error(error);
    } finally {
      setIsDeleting(false);
      setDeleteApiKeyId(null);
    }
  };

  // Copy API key to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('API key copied to clipboard');
  };

  let content: React.ReactNode;
  if (isLoading) {
    content = (
      <div className="flex h-20 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  } else if (apiKeys.length === 0) {
    content = (
      <div className="flex h-20 items-center justify-center text-center text-muted-foreground">
        No API keys found. Create one to get started.
      </div>
    );
  } else {
    content = (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Key</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Last Used</TableHead>
              <TableHead>Expires</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {apiKeys.map((apiKey) => (
              <TableRow key={apiKey.id}>
                <TableCell className="font-medium">{apiKey.name}</TableCell>
                <TableCell>{apiKey.start}...</TableCell>
                <TableCell>
                  {formatDateTime(new Date(apiKey.createdAt), 'MMM d, yyyy')}
                </TableCell>
                <TableCell>
                  {apiKey.lastRequest
                    ? formatDateTime(
                        new Date(apiKey.lastRequest),
                        'MMM d, yyyy'
                      )
                    : 'Never'}
                </TableCell>
                <TableCell>
                  {apiKey.expiresAt ? (
                    <Badge variant="outline">
                      {formatDateTime(
                        new Date(apiKey.expiresAt),
                        'MMM d, yyyy'
                      )}
                    </Badge>
                  ) : (
                    <Badge variant="outline">Never</Badge>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setDeleteApiKeyId(apiKey.id)}
                      >
                        <Trash className="h-4 w-4 text-destructive" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete API Key</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this API key? This
                          action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          variant="destructive"
                          onClick={() => handleDeleteApiKey(apiKey.id)}
                          disabled={isDeleting}
                        >
                          {isDeleting && deleteApiKeyId === apiKey.id && (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          )}
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-lg">API Keys</h3>
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create API Key
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create API Key</DialogTitle>
              <DialogDescription>
                Create a new API key for programmatic access to the API.
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="My API Key" {...field} />
                      </FormControl>
                      <FormDescription>
                        A descriptive name for your API key.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit" disabled={isCreating}>
                    {isCreating && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Create
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {newApiKey && (
        <Card className="border-yellow-500">
          <CardHeader>
            <CardTitle className="text-yellow-500">
              New API Key Created
            </CardTitle>
            <CardDescription>
              This is the only time you'll be able to see this API key. Make
              sure to copy it now.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Input value={newApiKey} readOnly className="font-mono" />
              <Button
                variant="outline"
                size="icon"
                onClick={() => copyToClipboard(newApiKey)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" onClick={() => setNewApiKey(null)}>
              I've copied the API key
            </Button>
          </CardFooter>
        </Card>
      )}

      {content}
    </div>
  );
}
