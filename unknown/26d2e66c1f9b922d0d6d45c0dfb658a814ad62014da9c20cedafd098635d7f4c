'use client';

import { ApiEndpoint } from '@/app/lib/api';
import { clientFetchWithAuth } from '@/app/lib/api.client';
import type { Order } from '@/app/types';
import { useJwtToken } from '@/app/utils/auth-client-helpers';
import { log } from '@repo/observability/log';
import { useState } from 'react';
import { mutate } from 'swr';
import type {
  UpdateBuyerData,
  UpdateLineItemData,
  UpdateOrderInformationData,
} from '../schemas/order-form-schema';

/**
 * Custom hook for creating or updating an order
 * This uses direct client -> core API communication
 */
export function useUpdateOrder() {
  const { jwt } = useJwtToken();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const updateOrderInformation = async (
    orderData: UpdateOrderInformationData,
    orderId: number
  ): Promise<void> => {
    if (!jwt) {
      setError(new Error('Authentication required'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const order = await clientFetchWithAuth<{ data: Order }>(
        ApiEndpoint.ORDERS + '/' + orderId,
        jwt,
        {
          method: 'PUT',
          body: JSON.stringify({
            ...orderData,
            invoiceDateTime: {
              date: orderData.invoiceDateTime,
              time: orderData.invoiceDateTime,
            },
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      log.info('Order updated successfully', { order });

      // TODO: only mutate the single order
      mutate(
        (url) =>
          typeof url === 'string' &&
          url.startsWith(ApiEndpoint.ORDERS + '/' + orderId)
      );
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to updated order');
      log.warn('Failed to updated order in core backend', { error });
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateBuyer = async (
    orderData: UpdateBuyerData,
    orderId: number
  ): Promise<void> => {
    if (!jwt) {
      setError(new Error('Authentication required'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const order = await clientFetchWithAuth<{ data: Order }>(
        ApiEndpoint.ORDERS + '/' + orderId,
        jwt,
        {
          method: 'PUT',
          body: JSON.stringify({
            buyer: {
              name: orderData.buyerName,
              tin: orderData.buyerTin,
              registrationType: orderData.registrationType,
              registrationNumber: orderData.registrationNumber,
              sstRegistrationNumber: orderData.sstRegistrationNumber,
              email: orderData.email,
              address: orderData.address,
              contactNumber: orderData.contactNumber,
            },
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      log.info('Order updated successfully', { order });

      mutate(
        (url) =>
          typeof url === 'string' &&
          url.startsWith(ApiEndpoint.ORDERS + '/' + orderId)
      );
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to updated order');
      log.warn('Failed to updated order in core backend', { error });
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateLineItem = async (
    orderData: UpdateLineItemData,
    orderId: number
  ): Promise<void> => {
    if (!jwt) {
      setError(new Error('Authentication required'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const order = await clientFetchWithAuth<{ data: Order }>(
        ApiEndpoint.ORDERS + '/' + orderId,
        jwt,
        {
          method: 'PUT',
          body: JSON.stringify({
            ...orderData,
          }),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      log.info('Order updated successfully', { order });

      mutate(
        (url) =>
          typeof url === 'string' &&
          url.startsWith(ApiEndpoint.ORDERS + '/' + orderId)
      );
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to updated order');
      log.warn('Failed to updated order in core backend', { error });
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    updateOrderInformation,
    updateBuyer,
    updateLineItem,
    isLoading,
    error,
  };
}
