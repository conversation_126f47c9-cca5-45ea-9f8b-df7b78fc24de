'use server';

import { ApiEndpoint, fetchWithAuth } from '@/app/lib/api';
import type { Company } from '@/app/types';
import { getJwtFromSession } from '@/app/utils/auth-helpers';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { log } from '@repo/observability/log';

export async function getCompany(): Promise<Company> {
  try {
    const response = await fetchWithAuth<{ data: Company }>(
      urlSerialize(ApiEndpoint.ME_COMPANIES),
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getCompany', { company: response.data });
    return response.data;
  } catch (error) {
    log.warn('Failed to fetch company from core backend', { error });
    throw new Error('Failed to fetch company from core backend');
  }
}

export async function updateCompany(values: Partial<Company>) {
  try {
    const response = await fetchWithAuth<{ data: Company }>(
      ApiEndpoint.COMPANIES,
      await getJwtFromSession(),
      {
        method: 'PUT',
        body: JSON.stringify(values),
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('updateCompany', { company: response.data });
    return response.data;
  } catch (error) {
    log.warn('Failed to update company in core backend', { error });
    throw new Error('Failed to update company in core backend');
  }
}
