'use client';

import { useSession } from '@repo/auth/client';
import {
  AdvanceSelect,
  type SelectableItem,
} from '@repo/design-system/components/ui/advance-select';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/design-system/components/ui/form';
import { useEffect, useState } from 'react';
import {
  getClassificationsByCategory,
  searchClassifications,
} from '../../actions/classifications';
import {
  classificationCategories,
  getClassificationsByBusinessType,
  getFrequentlyUsedClassifications,
} from '../../schemas/classification-categories';
import { classificationCodesService } from '../../schemas/classifications';
import { useClassificationsStore } from '../../stores/classifications-store';

// Define the classification item type
interface ClassificationItem extends SelectableItem {
  code: string;
  description: string;
}

// Define the props for the classification selector
interface ClassificationSelectorProps {
  value: string[];
  onChange: (value: string[]) => void;
  isConsolidate: boolean;
}

export function ClassificationSelector({
  value,
  onChange,
  isConsolidate,
}: ClassificationSelectorProps) {
  // Get store values and actions
  const {
    recentlyUsed,
    favorites,
    addToRecentlyUsed,
    addToFavorites,
    removeFromFavorites,
  } = useClassificationsStore();

  // State for all classifications (loaded once)
  const [allClassifications, setAllClassifications] = useState<
    ClassificationItem[]
  >([]);
  const [suggestedClassifications, setSuggestedClassifications] = useState<
    string[]
  >([]);

  // Get session for business type
  const { data: session } = useSession();

  // Load all classifications once (for local operations)
  useEffect(() => {
    const classifications = classificationCodesService
      .getAllCodes()
      .map((classification) => ({
        id: classification.Code,
        label: `${classification.Code} - ${classification.Description}`,
        value: classification.Code,
        code: classification.Code,
        description: classification.Description,
      }));
    setAllClassifications(classifications);
  }, []);

  // Get suggested classifications based on business type
  useEffect(() => {
    const fetchBusinessType = () => {
      try {
        if (session?.user?.id) {
          // TODO: In a real implementation, you would fetch the business type from the API
          // For now, we'll use a mock business type
          const businessType = 'retail'; // This would come from the API
          const suggested = getClassificationsByBusinessType(businessType);
          setSuggestedClassifications(suggested);
        }
      } catch (error) {
        console.error('Error fetching business type:', error);
      }
    };

    fetchBusinessType();
  }, [session]);

  // Get suggested classifications
  const getSuggestedItems = () => {
    return suggestedClassifications
      .map((code) => {
        const classification = classificationCodesService.getByCode(code);
        if (!classification) {
          return null;
        }
        return {
          id: classification.Code,
          label: `${classification.Code} - ${classification.Description}`,
          value: classification.Code,
          code: classification.Code,
          description: classification.Description,
        };
      })
      .filter(Boolean) as ClassificationItem[];
  };

  // Get frequent classifications
  const getFrequentItems = () => {
    const frequentClassifications = getFrequentlyUsedClassifications();
    return frequentClassifications
      .map((classification) => {
        if (!classification) {
          return null;
        }
        return {
          id: classification.Code,
          label: `${classification.Code} - ${classification.Description}`,
          value: classification.Code,
          code: classification.Code,
          description: classification.Description,
        };
      })
      .filter(Boolean) as ClassificationItem[];
  };

  // Search function for classifications
  const searchItems = async (term: string) => {
    if (!term || term.length < 2) {
      return [];
    }

    try {
      const results = await searchClassifications(term);
      return results.map((classification) => ({
        id: classification.Code,
        label: `${classification.Code} - ${classification.Description}`,
        value: classification.Code,
        code: classification.Code,
        description: classification.Description,
      }));
    } catch (error) {
      console.error('Error searching classifications:', error);
      return [];
    }
  };

  // Get items by category
  const getItemsByCategory = async (categoryId: string) => {
    try {
      const results = await getClassificationsByCategory(categoryId);
      const mappedResults = results.map((classification) => {
        if (!classification) {
          return null;
        }

        return {
          id: classification.Code,
          label: `${classification.Code} - ${classification.Description}`,
          value: classification.Code,
          code: classification.Code,
          description: classification.Description,
        };
      });

      return mappedResults.filter(Boolean) as ClassificationItem[];
    } catch (error) {
      console.error('Error getting classifications by category:', error);
      return [];
    }
  };

  // Custom trigger renderer for multi-select
  const renderTrigger = ({
    isOpen,
  }: { value: string | string[]; isOpen: boolean; placeholder: string }) => {
    return (
      <Button
        variant="outline"
        aria-expanded={isOpen}
        className="h-auto min-h-10 w-full justify-between py-2"
      >
        <div className="flex flex-wrap items-center gap-1">
          {value.length === 0 ? (
            <span className="text-muted-foreground">
              Select classifications...
            </span>
          ) : (
            <>
              {value.map((code) => {
                const classification =
                  classificationCodesService.getByCode(code);
                return (
                  <Badge
                    key={code}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {code} - {classification?.Description}
                  </Badge>
                );
              })}
            </>
          )}
        </div>
      </Button>
    );
  };

  // Format categories for the selector
  const formattedCategories = classificationCategories.map((category) => ({
    id: category.id,
    name: category.name,
  }));

  return (
    <FormItem className="col-span-2">
      <FormLabel>Classifications</FormLabel>
      <FormControl>
        {/* TODO: search result not showing up */}
        <AdvanceSelect<ClassificationItem>
          value={value}
          onChange={onChange as (value: string | string[]) => void}
          isMulti={true}
          items={allClassifications}
          searchFunction={searchItems}
          getItemsByCategory={getItemsByCategory}
          getSuggestedItems={getSuggestedItems}
          getFrequentItems={getFrequentItems}
          placeholder="Select classifications"
          searchPlaceholder="Search classifications..."
          noResultsMessage="No classifications found"
          loadingMessage="Loading classifications..."
          categories={formattedCategories}
          favorites={favorites}
          recentlyUsed={recentlyUsed}
          onAddToFavorites={addToFavorites}
          onRemoveFromFavorites={removeFromFavorites}
          onAddToRecentlyUsed={addToRecentlyUsed}
          renderTrigger={renderTrigger}
          isConsolidate={isConsolidate}
        />
      </FormControl>
      <FormMessage />
    </FormItem>
  );
}
