'use client';

import { <PERSON><PERSON> } from '@repo/design-system/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@repo/design-system/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/design-system/components/ui/popover';
import { cn } from '@repo/design-system/lib/utils';
import { Check, ChevronsUpDown, Search, Star, StarOff } from 'lucide-react';
import { type ReactNode, useEffect, useState, useTransition } from 'react';

// Generic type for items that can be selected
export type SelectableItem = {
  id: string;
  label: string;
  value: string;
  description?: string;
  category?: string;
};

// Props for the base selector component
export interface AdvanceSelectProps<T extends SelectableItem> {
  // Core functionality
  value: string | string[]; // Can be single value or array for multi-select
  onChange: (value: string | string[]) => void;
  isMulti?: boolean;
  isConsolidate?: boolean; // Consolidate means classification can only be '004'.

  // Data and loading
  items?: T[];
  searchFunction?: (term: string) => Promise<T[]>;
  getItemsByCategory?: (categoryId: string) => Promise<T[]>;
  getSuggestedItems?: () => T[];
  getFrequentItems?: () => T[];

  // UI customization
  placeholder?: string;
  searchPlaceholder?: string;
  noResultsMessage?: string;
  loadingMessage?: string;
  width?: string;

  // Categories
  categories?: Array<{ id: string; name: string }>;

  // Favorites and recently used
  favorites?: string[];
  recentlyUsed?: string[];
  onAddToFavorites?: (id: string) => void;
  onRemoveFromFavorites?: (id: string) => void;
  onAddToRecentlyUsed?: (id: string) => void;

  // Rendering
  renderTrigger?: (props: {
    value: string | string[];
    isOpen: boolean;
    placeholder: string;
  }) => ReactNode;
  renderItem?: (props: {
    item: T;
    isSelected: boolean;
    isFavorite: boolean;
    onToggleFavorite: () => void;
  }) => ReactNode;
}

export function AdvanceSelect<T extends SelectableItem>({
  // Core functionality
  value,
  onChange,
  isMulti = false,
  isConsolidate = false,

  // Data and loading
  items = [],
  searchFunction,
  getItemsByCategory,
  getSuggestedItems,
  getFrequentItems,

  // UI customization
  placeholder = 'Select...',
  searchPlaceholder = 'Search...',
  noResultsMessage = 'No results found',
  loadingMessage = 'Loading...',
  width = '350px',

  // Categories
  categories = [],

  // Favorites and recently used
  favorites = [],
  recentlyUsed = [],
  onAddToFavorites,
  onRemoveFromFavorites,
  onAddToRecentlyUsed,

  // Rendering
  renderTrigger,
  renderItem,
}: AdvanceSelectProps<T>) {
  // State
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<T[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categoryResults, setCategoryResults] = useState<T[]>([]);
  const [isCategoryLoading, setIsCategoryLoading] = useState(false);
  const [, startTransition] = useTransition();

  // Get suggested and frequent items
  const suggestedItems = getSuggestedItems ? getSuggestedItems() : [];
  const frequentItems = getFrequentItems ? getFrequentItems() : [];

  // Handle search with debounce
  useEffect(() => {
    if (!searchTerm || !searchFunction) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);

    const timer = setTimeout(() => {
      startTransition(async () => {
        try {
          if (searchTerm.length >= 2) {
            const results = await searchFunction(searchTerm);
            setSearchResults(results);
          } else {
            setSearchResults([]);
          }
        } catch (error) {
          console.error('Error searching items:', error);
          setSearchResults([]);
        } finally {
          setIsSearching(false);
        }
      });
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, searchFunction]);

  // Load items for a category when selected
  useEffect(() => {
    if (!selectedCategory || !getItemsByCategory) {
      setCategoryResults([]);
      return;
    }

    setIsCategoryLoading(true);

    startTransition(async () => {
      try {
        const results = await getItemsByCategory(selectedCategory);
        setCategoryResults(results);
      } catch (error) {
        console.error('Error loading category items:', error);
        setCategoryResults([]);
      } finally {
        setIsCategoryLoading(false);
      }
    });
  }, [selectedCategory, getItemsByCategory]);

  // Check if an item is selected
  const isSelected = (itemValue: string) => {
    if (isMulti) {
      return Array.isArray(value) && value.includes(itemValue);
    }
    return value === itemValue;
  };

  // Check if an item is a favorite
  const isFavorite = (itemId: string) => favorites.includes(itemId);

  // Handle selection of an item
  const handleSelect = (itemValue: string) => {
    if (isMulti) {
      // For multi-select, toggle the selection
      const currentValues = Array.isArray(value) ? value : [];
      const newValues = currentValues.includes(itemValue)
        ? currentValues.filter((v) => v !== itemValue)
        : [...currentValues, itemValue];
      onChange(newValues);
    } else {
      // For single-select, just set the value and close the popover
      onChange(itemValue);
      setOpen(false);
    }

    // Add to recently used
    if (onAddToRecentlyUsed) {
      onAddToRecentlyUsed(itemValue);
    }
  };

  // Toggle favorite status
  const toggleFavorite = (itemId: string) => {
    if (isFavorite(itemId)) {
      onRemoveFromFavorites?.(itemId);
    } else {
      onAddToFavorites?.(itemId);
    }
  };

  // Default trigger renderer
  const defaultTriggerRenderer = ({
    value,
    isOpen,
    placeholder,
  }: {
    value: string | string[];
    isOpen: boolean;
    placeholder: string;
  }) => {
    let displayValue = placeholder;

    if (isMulti) {
      const count = Array.isArray(value) ? value.length : 0;
      displayValue = count > 0 ? `${count} selected` : placeholder;
    } else if (value) {
      const selectedItem = items.find((item) => item.value === value);
      displayValue = selectedItem ? selectedItem.label : String(value);
    }

    return (
      <Button
        variant="outline"
        aria-expanded={isOpen}
        className="w-full justify-between"
      >
        {displayValue}
        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </Button>
    );
  };

  // Default item renderer
  const defaultItemRenderer = ({
    item,
    isSelected,
    isFavorite,
    onToggleFavorite,
  }: {
    item: T;
    isSelected: boolean;
    isFavorite: boolean;
    onToggleFavorite: () => void;
  }) => {
    return (
      <div className="flex w-full items-center justify-between">
        <div className="flex items-center">
          <Check
            className={cn(
              'mr-2 h-4 w-4 flex-shrink-0',
              isSelected ? 'opacity-100' : 'opacity-0'
            )}
          />
          <span className="truncate">{item.label}</span>
        </div>
        {(onAddToFavorites || onRemoveFromFavorites) && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              onToggleFavorite();
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.stopPropagation();
                onToggleFavorite();
              }
            }}
            className="ml-2 flex-shrink-0 rounded-sm p-1 hover:bg-muted"
            aria-label={
              isFavorite ? 'Remove from favorites' : 'Add to favorites'
            }
          >
            {isFavorite ? (
              <StarOff className="h-4 w-4 text-muted-foreground" />
            ) : (
              <Star
                className={`h-4 w-4 ${isFavorite ? 'fill-yellow-500 text-yellow-500' : 'text-muted-foreground'}`}
              />
            )}
          </button>
        )}
      </div>
    );
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal={true}>
      <PopoverTrigger asChild disabled={isConsolidate}>
        {renderTrigger
          ? renderTrigger({ value, isOpen: open, placeholder })
          : defaultTriggerRenderer({ value, isOpen: open, placeholder })}
      </PopoverTrigger>
      <PopoverContent className={`w-[${width}] p-0`} align="start">
        <Command>
          <CommandInput
            placeholder={searchPlaceholder}
            onValueChange={setSearchTerm}
          />
          <CommandList
            className="max-h-[300px]"
            style={{ scrollbarWidth: 'thin' }}
          >
            <CommandEmpty>
              {isSearching ? loadingMessage : noResultsMessage}
            </CommandEmpty>

            {/* Loading indicator */}
            {(isSearching || isCategoryLoading) && (
              <CommandItem disabled>
                <Search className="mr-2 h-4 w-4 animate-spin" />
                {loadingMessage}
              </CommandItem>
            )}

            {/* Suggested Items */}
            {suggestedItems.length > 0 && !selectedCategory && (
              <CommandGroup heading="Suggested">
                {suggestedItems.map((item) => (
                  <CommandItem
                    key={`suggested-${item.id}`}
                    value={`suggested-${item.id}`}
                    onSelect={() => handleSelect(item.value)}
                  >
                    {renderItem
                      ? renderItem({
                          item,
                          isSelected: isSelected(item.value),
                          isFavorite: isFavorite(item.id),
                          onToggleFavorite: () => toggleFavorite(item.id),
                        })
                      : defaultItemRenderer({
                          item,
                          isSelected: isSelected(item.value),
                          isFavorite: isFavorite(item.id),
                          onToggleFavorite: () => toggleFavorite(item.id),
                        })}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {/* Recently Used Items */}
            {recentlyUsed.length > 0 && !selectedCategory && (
              <CommandGroup heading="Recently Used">
                {recentlyUsed
                  .map((id) => items.find((item) => item.id === id))
                  .filter((item): item is T => item !== undefined)
                  .map((item) => (
                    <CommandItem
                      key={`recent-${item.id}`}
                      value={`recent-${item.id}`}
                      onSelect={() => handleSelect(item.value)}
                    >
                      {renderItem
                        ? renderItem({
                            item,
                            isSelected: isSelected(item.value),
                            isFavorite: isFavorite(item.id),
                            onToggleFavorite: () => toggleFavorite(item.id),
                          })
                        : defaultItemRenderer({
                            item,
                            isSelected: isSelected(item.value),
                            isFavorite: isFavorite(item.id),
                            onToggleFavorite: () => toggleFavorite(item.id),
                          })}
                    </CommandItem>
                  ))}
              </CommandGroup>
            )}

            {/* Frequent Items */}
            {frequentItems.length > 0 && !selectedCategory && (
              <CommandGroup heading="Frequent">
                {frequentItems.map((item) => (
                  <CommandItem
                    key={`frequent-${item.id}`}
                    value={`frequent-${item.id}`}
                    onSelect={() => handleSelect(item.value)}
                  >
                    {renderItem
                      ? renderItem({
                          item,
                          isSelected: isSelected(item.value),
                          isFavorite: isFavorite(item.id),
                          onToggleFavorite: () => toggleFavorite(item.id),
                        })
                      : defaultItemRenderer({
                          item,
                          isSelected: isSelected(item.value),
                          isFavorite: isFavorite(item.id),
                          onToggleFavorite: () => toggleFavorite(item.id),
                        })}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {/* Favorites */}
            {favorites.length > 0 && !selectedCategory && (
              <CommandGroup heading="Favorites">
                {favorites
                  .map((id) => items.find((item) => item.id === id))
                  .filter((item): item is T => item !== undefined)
                  .map((item) => (
                    <CommandItem
                      key={`favorite-${item.id}`}
                      value={`favorite-${item.id}`}
                      onSelect={() => handleSelect(item.value)}
                    >
                      {renderItem
                        ? renderItem({
                            item,
                            isSelected: isSelected(item.value),
                            isFavorite: true,
                            onToggleFavorite: () => toggleFavorite(item.id),
                          })
                        : defaultItemRenderer({
                            item,
                            isSelected: isSelected(item.value),
                            isFavorite: true,
                            onToggleFavorite: () => toggleFavorite(item.id),
                          })}
                    </CommandItem>
                  ))}
              </CommandGroup>
            )}

            {/* Search Results */}
            {searchResults.length > 0 && (
              <CommandGroup heading="Search Results">
                {searchResults.map((item) => (
                  <CommandItem
                    key={`search-${item.id}`}
                    value={`search-${item.id}`}
                    onSelect={() => handleSelect(item.value)}
                  >
                    {renderItem
                      ? renderItem({
                          item,
                          isSelected: isSelected(item.value),
                          isFavorite: isFavorite(item.id),
                          onToggleFavorite: () => toggleFavorite(item.id),
                        })
                      : defaultItemRenderer({
                          item,
                          isSelected: isSelected(item.value),
                          isFavorite: isFavorite(item.id),
                          onToggleFavorite: () => toggleFavorite(item.id),
                        })}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {/* Category Results */}
            {selectedCategory && categoryResults.length > 0 && (
              <CommandGroup
                heading={
                  <div className="flex items-center justify-between space-x-4">
                    <span>
                      {categories.find((c) => c.id === selectedCategory)
                        ?.name || 'Category'}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 text-xs"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedCategory(null);
                      }}
                    >
                      Back to categories
                    </Button>
                  </div>
                }
              >
                {categoryResults.map((item) => (
                  <CommandItem
                    key={`category-${item.id}`}
                    value={`category-${item.id}`}
                    onSelect={() => handleSelect(item.value)}
                  >
                    {renderItem
                      ? renderItem({
                          item,
                          isSelected: isSelected(item.value),
                          isFavorite: isFavorite(item.id),
                          onToggleFavorite: () => toggleFavorite(item.id),
                        })
                      : defaultItemRenderer({
                          item,
                          isSelected: isSelected(item.value),
                          isFavorite: isFavorite(item.id),
                          onToggleFavorite: () => toggleFavorite(item.id),
                        })}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {/* Categories List */}
            {categories.length > 0 && !selectedCategory && !searchTerm && (
              <CommandGroup heading="Categories">
                {categories.map((category) => (
                  <CommandItem
                    key={`category-${category.id}`}
                    value={`category-${category.id}`}
                    onSelect={() => setSelectedCategory(category.id)}
                  >
                    {category.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
