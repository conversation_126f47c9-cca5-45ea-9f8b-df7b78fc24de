{"name": "@repo/ai", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@t3-oss/env-nextjs": "^0.13.4", "ai": "^4.3.16", "react": "^19.1.0", "react-markdown": "^10.1.0", "tailwind-merge": "^3.3.0", "zod": "^3.25.28"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "^19.1.5"}}