{"name": "@repo/auth", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@better-auth/stripe": "^1.2.8", "@t3-oss/env-nextjs": "^0.12.0", "better-auth": "^1.2.8", "next": "15.3.0", "next-themes": "^0.4.6", "react": "^19.1.0", "server-only": "^0.0.1", "usehooks-ts": "^3.1.1", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@repo/database": "workspace:*", "@repo/payments": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "^19.1.2", "typescript": "^5.8.3"}}