export interface TaxType {
  code: string;
  description: string;
  registrationField?: string;
  details?: string;
  rates?: Array<{
    rate: string;
    description: string;
  }>;
}

export const TAX_TYPES: TaxType[] = [
  {
    code: '01',
    description: 'Sales Tax',
    registrationField: 'sst_registration_number',
    details:
      'Sales tax is a single-stage tax imposed on taxable goods manufactured in Malaysia by a registered manufacturer or on taxable goods imported into Malaysia.',
    rates: [
      {
        rate: '5%',
        description:
          'Applied to petroleum oils, construction materials, timepieces, and certain foodstuffs.',
      },
      {
        rate: '10%',
        description:
          'Standard sales tax rate in Malaysia, levied on taxable goods and imported taxable goods. Generally applied to luxury items or goods that are not considered essential.',
      },
    ],
  },
  {
    code: '02',
    description: 'Service Tax',
    registrationField: 'sst_registration_number',
    details:
      'Service tax is a form of consumption tax imposed on taxable services provided in Malaysia by a registered person in carrying on their business.',
    rates: [
      {
        rate: '6%',
        description:
          'Applied to food & beverage services, telecommunication services, parking services and logistics services.',
      },
      {
        rate: '8%',
        description:
          'Effective from 1 March 2024, applied to most services except food & beverage, telecommunication, parking, and logistics services.',
      },
    ],
  },
  {
    code: '03',
    description: 'Tourism Tax',
    registrationField: 'tourism_tax_registration_number',
    details:
      'Tourism tax is levied on accommodation premises provided by an operator at a rate fixed by the government.',
    rates: [
      {
        rate: 'RM10',
        description: 'Per room per night for foreign tourists.',
      },
    ],
  },
  {
    code: '04',
    description: 'High-Value Goods Tax',
    details: 'Tax imposed on luxury or high-value goods.',
  },
  {
    code: '05',
    description: 'Sales Tax on Low Value Goods',
    details: 'Tax imposed on imported low-value goods sold online.',
  },
  {
    code: '06',
    description: 'Not Applicable',
    details: 'Select this if none of the tax types apply to your business.',
  },
  {
    code: 'E',
    description: 'Tax exemption (where applicable)',
    details: 'Select this if your business has a tax exemption status.',
  },
];
