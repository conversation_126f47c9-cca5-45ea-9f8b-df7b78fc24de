'use client';

import { LoaderCircle } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Separator } from '@repo/design-system/components/ui/separator';
import { log } from '@repo/observability/log';
import { msicOptions } from '../../constants/msic';
import { TAX_TYPES } from '../../constants/tax-types';
import type { BusinessFormData } from '../../schemas/business-form-schema';
import { useOnboardingStore } from '../../store/onboarding-store';
import { BUSINESS_TYPES } from './business-type-selector';

interface SummaryStepProps {
  isSubmitting: boolean;
  onBack: () => void;
  onComplete?: (formData: Partial<BusinessFormData>) => Promise<void>;
  onError?: (error: unknown) => void;
}

export function SummaryStep({
  isSubmitting,
  onBack,
  onComplete,
  onError,
}: SummaryStepProps) {
  const { formData, setComplete } = useOnboardingStore();

  async function handleSubmit() {
    try {
      if (!onComplete) {
        throw new Error('No onComplete function provided');
      }

      // Call the complete callback if provided
      await onComplete(formData);

      // Mark onboarding as complete
      setComplete(true);
    } catch (error) {
      log.warn('Failed to submit onboarding data', { error });

      // Call the error callback if provided
      if (onError) {
        onError(error);
      }
    }
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>API Credentials</CardTitle>
            <CardDescription>Your API access information</CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2 md:grid-cols-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Client ID
                </dt>
                <dd className="text-sm">{formData.client_id}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Client Secret
                </dt>
                <dd className="text-sm">••••••••••••••••</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Company Information</CardTitle>
            <CardDescription>
              Your business registration details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2 md:grid-cols-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Business Name
                </dt>
                <dd className="text-sm">{formData.name}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  TIN Code
                </dt>
                <dd className="text-sm">{formData.tin_code}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Registration Type
                </dt>
                <dd className="text-sm">{formData.registration_type}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Registration Number
                </dt>
                <dd className="text-sm">{formData.registration_number}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Business Details</CardTitle>
            <CardDescription>
              Your business activity information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2 md:grid-cols-2">
              <div className="col-span-2">
                <dt className="font-medium text-muted-foreground text-sm">
                  Business Activity Description
                </dt>
                <dd className="text-sm">
                  {formData.business_activity_description}
                </dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  MSIC Code
                </dt>
                <dd className="text-sm">
                  {formData.msic_code}{' '}
                  {
                    msicOptions.find(
                      (option) => option.Code === formData.msic_code
                    )?.Description
                  }
                </dd>
              </div>

              {formData.business_type && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Business Type
                  </dt>
                  <dd className="text-sm">
                    {BUSINESS_TYPES.find(
                      (type) => type.value === formData.business_type
                    )?.label || formData.business_type}
                  </dd>
                </div>
              )}

              <div className="col-span-2">
                <Separator />
              </div>

              {/* Tax Types */}
              <div className="col-span-2 space-y-2">
                <dt className="font-medium text-muted-foreground text-sm">
                  Applicable Tax Types
                </dt>
                <dd className="text-sm">
                  {formData.applicable_tax_types?.map((code) => {
                    const taxType = TAX_TYPES.find((tax) => tax.code === code);
                    return taxType ? (
                      <div key={code} className="mb-2">
                        <div className="flex items-center gap-1 font-medium text-sm">
                          <span>{taxType.description}</span>
                          {code === 'E' && <span>(Exemption)</span>}
                        </div>
                        {taxType.details && (
                          <div className="mt-1 text-muted-foreground text-sm">
                            {taxType.details}
                          </div>
                        )}
                        {taxType.rates && taxType.rates.length > 0 && (
                          <div className="mt-1">
                            <p className="font-medium text-muted-foreground text-sm">
                              Applicable Rates:
                            </p>
                            <ul className="list-disc pl-5 text-muted-foreground text-sm">
                              {taxType.rates.map((rate, index) => (
                                <li key={index}>
                                  <span className="font-medium">
                                    {rate.rate}
                                  </span>
                                  : {rate.description}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ) : null;
                  })}
                </dd>
              </div>

              {formData.sst_registration_number && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    SST Registration Number
                  </dt>
                  <dd className="text-sm">
                    {formData.sst_registration_number}
                  </dd>
                </div>
              )}

              {formData.tourism_tax_registration_number && (
                <div>
                  <dt className="font-medium text-muted-foreground text-sm">
                    Tourism Tax Registration Number
                  </dt>
                  <dd className="text-sm">
                    {formData.tourism_tax_registration_number}
                  </dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>Your business contact details</CardDescription>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-2 md:grid-cols-2">
              <div className="col-span-2">
                <dt className="font-medium text-muted-foreground text-sm">
                  Address
                </dt>
                <dd className="text-sm">{formData.address}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  City
                </dt>
                <dd className="text-sm">{formData.city}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  State
                </dt>
                <dd className="text-sm">{formData.state}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  ZIP Code
                </dt>
                <dd className="text-sm">{formData.zip_code}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Country
                </dt>
                <dd className="text-sm">{formData.country}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Phone
                </dt>
                <dd className="text-sm">{formData.phone}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Email
                </dt>
                <dd className="text-sm">{formData.email}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={handleSubmit} disabled={isSubmitting}>
          {isSubmitting ? (
            <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
          ) : null}
          Complete Onboarding
        </Button>
      </div>
    </div>
  );
}
