'use client';

import { LoaderCircle } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import LocationSelector from '@repo/design-system/components/ui/location-input';
import countries from '@repo/design-system/data/countries.json';
import { useEffect } from 'react';
import type { ContactInfoData } from '../../schemas/business-form-schema';
import { contactInfoSchema } from '../../schemas/business-form-schema';
import { useOnboardingStore } from '../../store/onboarding-store';

interface ContactInfoStepProps {
  onNext: () => void;
  onBack: () => void;
}

export function ContactInfoStep({ onNext, onBack }: ContactInfoStepProps) {
  const { formData, updateFormData } = useOnboardingStore();

  const form = useForm<ContactInfoData>({
    resolver: zodResolver(contactInfoSchema),
    defaultValues: {
      country: formData.country || '',
      state: formData.state || '',
      zip_code: formData.zip_code || '',
      city: formData.city || '',
      address: formData.address || '',
      phone: formData.phone || '',
      email: formData.email || '',
    },
  });

  const { isSubmitting } = form.formState;

  // Update form when store data changes
  useEffect(() => {
    form.reset({
      country: formData.country || '',
      state: formData.state || '',
      zip_code: formData.zip_code || '',
      city: formData.city || '',
      address: formData.address || '',
      phone: formData.phone || '',
      email: formData.email || '',
    });
  }, [form, formData]);

  function onSubmit(values: ContactInfoData) {
    updateFormData(values);
    onNext();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Address</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter your address"
                    disabled={isSubmitting}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your city"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="zip_code"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>ZIP Code</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your ZIP code"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <GridFormItem>
              <FormLabel>Location</FormLabel>
              <FormControl>
                <LocationSelector
                  defaultCountry={countries.find(
                    (country) => country.iso2 === 'MY'
                  )}
                  onCountryChange={(country) => {
                    if (country) {
                      form.setValue('country', country.name);
                    } else {
                      form.setValue('country', '');
                    }
                  }}
                  onStateChange={(state) => {
                    if (state) {
                      form.setValue('state', state.name);
                    } else {
                      form.setValue('state', '');
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </GridFormItem>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Phone</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your phone number"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </div>
        </div>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Complete
          </Button>
        </div>
      </form>
    </Form>
  );
}
