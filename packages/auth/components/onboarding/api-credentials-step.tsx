'use client';

import { InfoIcon, LoaderCircle } from '@repo/design-system/components/icons';
import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { useEffect } from 'react';
import type { ApiCredentialsData } from '../../schemas/business-form-schema';
import { apiCredentialsSchema } from '../../schemas/business-form-schema';
import { useOnboardingStore } from '../../store/onboarding-store';

interface ApiCredentialsStepProps {
  onNext: () => void;
}

export function ApiCredentialsStep({ onNext }: ApiCredentialsStepProps) {
  const { formData, updateFormData } = useOnboardingStore();

  const form = useForm<ApiCredentialsData>({
    resolver: zodResolver(apiCredentialsSchema),
    defaultValues: {
      client_id: formData.client_id || '',
      client_secret: formData.client_secret || '',
    },
  });

  const { isSubmitting } = form.formState;

  // Update form when store data changes
  useEffect(() => {
    form.reset({
      client_id: formData.client_id || '',
      client_secret: formData.client_secret || '',
    });
  }, [form, formData.client_id, formData.client_secret]);

  function onSubmit(values: ApiCredentialsData) {
    updateFormData(values);
    onNext();
  }

  return (
    <Form {...form}>
      <Alert className="mt-4 mb-6">
        <InfoIcon className="h-4 w-4" />
        <AlertDescription>
          You can find your client ID & client secret in the LHDN portal.
        </AlertDescription>
        <AlertDescription>
          TODO: Add link to LHDN portal with instructions
        </AlertDescription>
      </Alert>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="client_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Client ID</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter your client ID"
                    disabled={isSubmitting}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="client_secret"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Client Secret</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="Enter your client secret"
                    disabled={isSubmitting}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* TODO: verify credentials with LHDN before proceed */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Next
          </Button>
        </div>
      </form>
    </Form>
  );
}
