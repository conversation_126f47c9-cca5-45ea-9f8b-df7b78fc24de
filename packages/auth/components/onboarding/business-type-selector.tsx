'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/design-system/components/ui/form';
import type { UseFormReturn } from '@repo/design-system/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import type { CompanyInfoData } from '../../schemas/business-form-schema';

const BUSINESS_TYPES = [
  {
    value: 'retail',
    label: 'Retail / E-Commerce',
    description: 'Selling physical or digital products to consumers',
  },
  {
    value: 'healthcare',
    label: 'Healthcare / Medical',
    description: 'Medical services, healthcare products, or wellness services',
  },
  {
    value: 'education',
    label: 'Education / Training',
    description:
      'Educational institutions, training services, or learning resources',
  },
  {
    value: 'technology',
    label: 'Technology / IT',
    description: 'Software, hardware, IT services, or digital solutions',
  },
  {
    value: 'finance',
    label: 'Finance / Insurance',
    description: 'Financial services, banking, insurance, or investment',
  },
  {
    value: 'manufacturing',
    label: 'Manufacturing / Production',
    description: 'Production of goods or manufacturing services',
  },
  {
    value: 'services',
    label: 'Professional Services',
    description:
      'Consulting, legal, accounting, or other professional services',
  },
  {
    value: 'other',
    label: 'Other',
    description: 'Other business types not listed above',
  },
];

interface BusinessTypeSelectorProps {
  form: UseFormReturn<CompanyInfoData>;
  isSubmitting?: boolean;
}

export function BusinessTypeSelector({
  form,
  isSubmitting = false,
}: BusinessTypeSelectorProps) {
  return (
    <FormField
      control={form.control}
      name="business_type"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Business Type</FormLabel>
          <FormControl>
            <Select
              disabled={isSubmitting}
              onValueChange={field.onChange}
              value={field.value}
            >
              <SelectTrigger>
                <SelectValue
                  aria-label={field.value}
                  placeholder="Select your business type"
                >
                  {field.value
                    ? BUSINESS_TYPES.find((type) => type.value === field.value)
                        ?.label
                    : ''}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {BUSINESS_TYPES.map((type) => (
                  <SelectItem
                    key={type.value}
                    value={type.value}
                    textValue={type.label}
                  >
                    <div className="flex flex-col items-start justify-center">
                      <span>{type.label}</span>
                      <span className="text-muted-foreground text-xs">
                        {type.description}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export { BUSINESS_TYPES };
