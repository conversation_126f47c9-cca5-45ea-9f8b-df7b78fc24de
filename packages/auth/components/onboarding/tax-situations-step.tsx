'use client';

import { LoaderCircle } from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { useEffect } from 'react';
import type { TaxSituationsData } from '../../schemas/business-form-schema';
import { taxSituationsSchema } from '../../schemas/business-form-schema';
import { useOnboardingStore } from '../../store/onboarding-store';
import { TaxTypesSelector } from './tax-types-selector';

interface TaxSituationsStepProps {
  onNext: () => void;
  onBack: () => void;
}

export function TaxSituationsStep({ onNext, onBack }: TaxSituationsStepProps) {
  const { formData, updateFormData } = useOnboardingStore();

  const form = useForm<TaxSituationsData>({
    resolver: zodResolver(taxSituationsSchema),
    defaultValues: {
      applicable_tax_types: formData.applicable_tax_types || ['06'], // Default to 'Not Applicable'
      sales_tax_rates: formData.sales_tax_rates || [],
      service_tax_rates: formData.service_tax_rates || [],
      sst_registration_number: formData.sst_registration_number || '',
      tourism_tax_registration_number:
        formData.tourism_tax_registration_number || '',
    },
  });

  const { isSubmitting } = form.formState;

  // Update form when store data changes
  useEffect(() => {
    form.reset({
      applicable_tax_types: formData.applicable_tax_types || ['06'], // Default to 'Not Applicable'
      sales_tax_rates: formData.sales_tax_rates || [],
      service_tax_rates: formData.service_tax_rates || [],
      sst_registration_number: formData.sst_registration_number || '',
      tourism_tax_registration_number:
        formData.tourism_tax_registration_number || '',
    });
  }, [form, formData]);

  function onSubmit(values: TaxSituationsData) {
    updateFormData(values);
    onNext();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          {/* Tax Types Selector */}
          <TaxTypesSelector form={form} isSubmitting={isSubmitting} />

          {/* Show registration fields based on selected tax types */}
          {form
            .watch('applicable_tax_types')
            ?.some((code: string) => ['01', '02'].includes(code)) && (
            <FormField
              control={form.control}
              name="sst_registration_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SST Registration Number</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter SST registration number"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {form.watch('applicable_tax_types')?.includes('03') && (
            <FormField
              control={form.control}
              name="tourism_tax_registration_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tourism Tax Registration Number</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter tourism tax registration number"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? (
              <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Next
          </Button>
        </div>
      </form>
    </Form>
  );
}
