'use client';

import {} from '@repo/design-system/components/ui/sidebar';

import type { BusinessFormData } from '../../schemas/business-form-schema';
import { useOnboardingStore } from '../../store/onboarding-store';
import { ApiCredentialsStep } from './api-credentials-step';
import { CompanyInfoStep } from './company-info-step';
import { ContactInfoStep } from './contact-info-step';
import { OnboardingStepper, onboardingSteps } from './onboarding-stepper';
import { SummaryStep } from './summary-step';
import { TaxSituationsStep } from './tax-situations-step';

interface OnboardingFormProps {
  isSubmitting: boolean;
  onComplete?: (formData: Partial<BusinessFormData>) => Promise<void>;
  onError?: (error: unknown) => void;
}

export function OnboardingForm({
  isSubmitting,
  onComplete,
  onError,
}: OnboardingFormProps) {
  const { currentStep, setCurrentStep } = useOnboardingStore();

  const handleNext = () => {
    setCurrentStep(currentStep + 1);
  };

  const handleBack = () => {
    setCurrentStep(currentStep - 1);
  };

  // Render the current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <ApiCredentialsStep onNext={handleNext} />;
      case 1:
        return <CompanyInfoStep onNext={handleNext} onBack={handleBack} />;
      case 2:
        return <TaxSituationsStep onNext={handleNext} onBack={handleBack} />;
      case 3:
        return <ContactInfoStep onNext={handleNext} onBack={handleBack} />;
      case 4:
        return (
          <SummaryStep
            isSubmitting={isSubmitting}
            onBack={handleBack}
            onComplete={onComplete}
            onError={onError}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <OnboardingStepper />

      <div className="flex flex-col text-left">
        <h1 className="font-semibold text-lg tracking-tight">
          {onboardingSteps[currentStep].title}
        </h1>
        <p className="text-muted-foreground text-sm">
          {onboardingSteps[currentStep].description}
        </p>
      </div>

      <div>{renderStepContent()}</div>
    </>
  );
}
