'use client';

import type { TaxSituationsData } from '@/schemas/business-form-schema';
import { InfoIcon } from '@repo/design-system/components/icons';
import { Checkbox } from '@repo/design-system/components/ui/checkbox';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/design-system/components/ui/form';
import type { UseFormReturn } from '@repo/design-system/components/ui/form';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { useEffect } from 'react';
import { TAX_TYPES } from '../../constants/tax-types';

interface TaxTypesSelectorProps {
  form: UseFormReturn<TaxSituationsData>;
  isSubmitting: boolean;
}

export function TaxTypesSelector({
  form,
  isSubmitting,
}: TaxTypesSelectorProps) {
  // Update tax rates when tax types change
  const selectedTaxTypes = form.watch('applicable_tax_types') || [];

  useEffect(() => {
    // If Sales Tax is not selected, clear sales tax rates
    if (!selectedTaxTypes.includes('01')) {
      form.setValue('sales_tax_rates', []);
    }

    // If Service Tax is not selected, clear service tax rates
    if (!selectedTaxTypes.includes('02')) {
      form.setValue('service_tax_rates', []);
    }
  }, [form, selectedTaxTypes]);

  // Helper function to render tax rate selectors
  const renderTaxRateSelectors = (
    taxType: (typeof TAX_TYPES)[0],
    fieldName: 'sales_tax_rates' | 'service_tax_rates'
  ) => {
    if (!taxType.rates || taxType.rates.length === 0) {
      return null;
    }

    return (
      <FormField
        control={form.control}
        name={fieldName}
        render={({ field }) => (
          <div className="mt-2 ml-8 space-y-2 border-gray-200 border-l-2 pl-4 text-foreground">
            <p className="font-medium text-sm">Select applicable rates:</p>
            <div className="space-y-2">
              {taxType.rates?.map((rate, index) => (
                <FormItem
                  key={index}
                  className="flex flex-row items-start space-x-3 space-y-0"
                >
                  <FormControl className="mt-1">
                    <Checkbox
                      disabled={isSubmitting}
                      checked={field.value?.includes(rate.rate)}
                      onCheckedChange={(checked) => {
                        const currentValue = field.value || [];
                        return checked
                          ? field.onChange([...currentValue, rate.rate])
                          : field.onChange(
                              currentValue.filter(
                                (value) => value !== rate.rate
                              )
                            );
                      }}
                    />
                  </FormControl>
                  <div className="space-y-1">
                    <div className="flex flex-col justify-center space-x-2">
                      <FormLabel className="font-medium text-sm">
                        {rate.rate}
                      </FormLabel>
                      <FormLabel className="text-muted-foreground text-xs">
                        {rate.description}
                      </FormLabel>
                    </div>
                  </div>
                </FormItem>
              ))}
            </div>
            <p className="text-muted-foreground text-xs italic">
              Select all rates that apply to your business. You may need to
              apply different rates to different products or services.
            </p>
          </div>
        )}
      />
    );
  };

  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="applicable_tax_types"
        render={() => (
          <FormItem>
            <div className="space-y-4">
              {TAX_TYPES.map((taxType) => (
                <FormField
                  key={taxType.code}
                  control={form.control}
                  name="applicable_tax_types"
                  render={({ field }) => {
                    const isSelected = field.value?.includes(taxType.code);

                    return (
                      <div key={taxType.code} className="space-y-2">
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              disabled={isSubmitting}
                              checked={isSelected}
                              onCheckedChange={(checked) => {
                                const currentValue = field.value || [];
                                return checked
                                  ? field.onChange([
                                      ...currentValue,
                                      taxType.code,
                                    ])
                                  : field.onChange(
                                      currentValue.filter(
                                        (value) => value !== taxType.code
                                      )
                                    );
                              }}
                            />
                          </FormControl>
                          <div className="flex items-center space-x-2">
                            <FormLabel className="font-medium">
                              {taxType.description}{' '}
                              {taxType.code === 'E' && '(E)'}
                            </FormLabel>
                            {taxType.details && (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <InfoIcon className="h-4 w-4 text-muted-foreground" />
                                  </TooltipTrigger>
                                  <TooltipContent className="max-w-sm">
                                    <p>{taxType.details}</p>
                                    {taxType.rates &&
                                      taxType.rates.length > 0 && (
                                        <div className="mt-2">
                                          <p className="font-medium">
                                            Applicable Rates:
                                          </p>
                                          <ul className="list-disc pl-5 text-sm">
                                            {taxType.rates.map(
                                              (rate, index) => (
                                                <li key={index}>
                                                  <span className="font-medium">
                                                    {rate.rate}
                                                  </span>
                                                  : {rate.description}
                                                </li>
                                              )
                                            )}
                                          </ul>
                                        </div>
                                      )}
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            )}
                          </div>
                        </FormItem>

                        {isSelected && (
                          <div className="ml-8 text-muted-foreground text-sm">
                            <p className="ml-1">{taxType.details}</p>

                            {/* Render tax rate selectors for Sales Tax */}
                            {taxType.code === '01' &&
                              renderTaxRateSelectors(
                                taxType,
                                'sales_tax_rates'
                              )}

                            {/* Render tax rate selectors for Service Tax */}
                            {taxType.code === '02' &&
                              renderTaxRateSelectors(
                                taxType,
                                'service_tax_rates'
                              )}

                            {/* For other tax types, just show the rates info */}
                            {taxType.code !== '01' &&
                              taxType.code !== '02' &&
                              taxType.rates &&
                              taxType.rates.length > 0 && (
                                <div className="mt-2">
                                  <p className="ml-1 font-medium">
                                    Applicable Rates:
                                  </p>
                                  <ul className="list-disc pl-5">
                                    {taxType.rates.map((rate, index) => (
                                      <li key={index}>
                                        <span className="font-medium">
                                          {rate.rate}
                                        </span>
                                        : {rate.description}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                          </div>
                        )}
                      </div>
                    );
                  }}
                />
              ))}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
