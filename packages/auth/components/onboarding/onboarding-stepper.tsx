'use client';

import {
  Stepper,
  StepperIndicator,
  StepperI<PERSON>,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
} from '@repo/design-system/components/ui/stepper';
import { useOnboardingStore } from '../../store/onboarding-store';

export const onboardingSteps = [
  {
    step: 1,
    title: 'API Credentials',
    mobileTitle: 'API',
    description:
      'Enter your API credentials to securely connect to LHDN MyInvois',
  },
  {
    step: 2,
    title: 'Business Info',
    mobileTitle: 'Business',
    description: 'Information about your business and registration numbers',
  },
  {
    step: 3,
    title: 'Business Tax Situations',
    mobileTitle: 'Tax',
    description: 'Select applicable tax types for your business',
  },
  {
    step: 4,
    title: 'Contact Info',
    mobileTitle: 'Contact',
    description:
      'Contact information that will be displayed on invoices and used for communication',
  },
  {
    step: 5,
    title: 'Summary',
    mobileTitle: 'Summary',
    description: 'Review and complete your onboarding',
  },
];

export function OnboardingStepper() {
  const { currentStep, setCurrentStep } = useOnboardingStore();

  // Handle step change from the stepper
  const handleStepChange = (step: number) => {
    // Convert 1-based step number back to 0-based index
    const targetStep = step - 1;

    // Only allow navigation to previous steps or the current step
    if (targetStep <= currentStep) {
      setCurrentStep(targetStep);
    }
  };

  return (
    <Stepper
      value={currentStep + 1}
      onValueChange={handleStepChange}
      className="my-8 flex md:hidden"
    >
      {onboardingSteps.map(({ step, mobileTitle: title }) => (
        <StepperItem
          key={step}
          step={step}
          className="not-last:flex-1 max-md:items-start"
          // Disable steps that are ahead of the current step
          disabled={step - 1 > currentStep}
        >
          <StepperTrigger className="flex-1 flex-col rounded">
            <StepperIndicator />
            <div className="text-center md:text-left">
              <StepperTitle className="text-xs">{title}</StepperTitle>
            </div>
          </StepperTrigger>
          {step < onboardingSteps.length && (
            <StepperSeparator className="max-md:mt-3.5 md:mx-4" />
          )}
        </StepperItem>
      ))}
    </Stepper>
  );
}
