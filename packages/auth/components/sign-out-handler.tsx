'use client';

import { useRouter } from 'next/navigation';
import { signOut as authSignOut } from '../client';
import { useOnboardingStore } from '../store/onboarding-store';

interface SignOutOptions {
  redirectTo?: string;
  fetchOptions?: {
    onSuccess?: () => void;
    onError?: (error: unknown) => void;
  };
}

/**
 * Custom sign-out handler that resets the onboarding store before signing out
 * @param options Sign-out options including redirect URL and callbacks
 */
export const signOut = (options?: SignOutOptions) => {
  // Get the reset function from the onboarding store
  const reset = useOnboardingStore.getState().reset;

  // Reset the onboarding store
  reset();

  // Call the original signOut function with the provided options
  return authSignOut(options);
};

/**
 * Hook that provides a sign-out function that resets the onboarding store
 * @param redirectTo Optional URL to redirect to after sign-out
 * @returns A function that handles sign-out with store reset
 */
export const useSignOut = (redirectTo = '/sign-in') => {
  const router = useRouter();
  const reset = useOnboardingStore.getState().reset;

  return () => {
    // Reset the onboarding store
    reset();

    // Sign out and redirect
    authSignOut({
      fetchOptions: {
        onSuccess: () => {
          router.push(redirectTo);
        },
      },
    });
  };
};
