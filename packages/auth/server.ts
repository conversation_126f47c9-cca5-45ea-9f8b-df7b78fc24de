// import { stripe as stripePlugin } from '@better-auth/stripe';
import { database } from '@repo/database';
import type { User } from '@repo/database/types';
import { log } from '@repo/observability/log';
// import { stripe } from '@repo/payments';
import { type BetterAuthOptions, betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { nextCookies } from 'better-auth/next-js';
import {
  admin,
  bearer,
  jwt,
  magicLink,
  organization,
} from 'better-auth/plugins';
import { apiKey } from 'better-auth/plugins';
import { keys } from './keys';
import { ac, ownerRole, superAdminRole } from './permissions';

const options = {
  user: {
    additionalFields: {
      firstName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      lastName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      phone: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      dob: {
        type: 'date',
        required: false,
        defaultValue: null,
        input: true,
      },
    },
  },
  emailAndPassword: {
    enabled: true,
    autoSignIn: true,
  },
  database: prismaAdapter(database, {
    provider: 'postgresql',
  }),
  session: {
    cookieCache: {
      enabled: true,
      // TOOD: maxAge: in seconds
    },
    additionalFields: {
      activeOrganizationName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: false,
      },
    },
  },
  plugins: [
    magicLink({
      // biome-ignore lint/suspicious/useAwait: <explanation>
      sendMagicLink: async ({ email, token, url }, _request) => {
        log.info('sending magic link to', { email, token, url });
        // send email to user
        // use resend to send email
      },
    }),
    // TODO: use local jwks in core backend
    jwt({
      jwt: {
        issuer: keys().AUTH_ISSUER,
        audience: keys().AUTH_ISSUER,
        expirationTime: '1h',
      },
    }),
    bearer(),
    apiKey({
      defaultPrefix: 'inv_',
      enableMetadata: true,
      rateLimit: {
        enabled: false, // true,
      },
    }),
    // stripePlugin({
    //   stripeClient: stripe,
    //   stripeWebhookSecret: keys().STRIPE_WEBHOOK_SECRET ?? '',
    //   createCustomerOnSignUp: false,
    //   // TODO: test auto create customer
    //   // onCustomerCreate: async ({ customer, stripeCustomer, user }, request) => {
    //   //   console.log(
    //   //     `Customer ${customer.id}, Stripe Customer ${stripeCustomer.id} created for user ${user.id}`
    //   //   );

    //   // TODO: create ticket, etc
    //   // },
    //   // getCustomerCreateParams: async ({ user, session }, request) => {
    //   //   // Customize the Stripe customer creation parameters
    //   //   return {
    //   //     metadata: {
    //   //       referralSource: user.metadata?.referralSource,
    //   //     },
    //   //   };
    //   // },
    // }),
    admin({
      defaultRole: 'customer',
      ac,
      roles: {
        superAdmin: superAdminRole,
      },
      adminRoles: ['super-admin', 'admin'],
    }),
    organization({
      ac,
      roles: { owner: ownerRole },
    }),
    nextCookies(),
  ],
  databaseHooks: {
    user: {
      create: {
        after: async (context) => {
          const user = context as User;

          try {
            const response = await fetch(
              `${keys().NEXT_PUBLIC_CORE_BACKEND_URL}/webhooks/v1/license`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'X-Webhook-Secret': 'your-webhook-secret-here',
                },
                body: JSON.stringify({
                  event: 'user.created',
                  data: {
                    userId: user.id,
                    email: user.email,
                    name: user.name,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    phone: user.phone,
                    dob: user.dob,
                  },
                }),
              }
            );

            const data = await response.json();

            log.info('User synced with core:', { data });
          } catch (error) {
            log.warn('Failed to sync user with core:', { error });
          }
        },
      },
    },
    session: {
      create: {
        before: async (context) => {
          try {
            const organizations = await database.organization.findMany({
              where: {
                members: {
                  some: {
                    userId: context.userId,
                  },
                },
              },
            });

            return {
              data: {
                ...context,
                activeOrganizationId: organizations?.[0]?.id,
                activeOrganizationName: organizations?.[0]?.name,
              },
            };
          } catch (error) {
            log.warn('Error listing organizations:', { error });

            return {
              data: context,
            };
          }
        },
      },
    },
  },
  onAPIError: {
    throw: true,
    onError: (error) => {
      log.warn('Auth error:', { error });
    },
    errorURL: '/auth/error',
  },
} satisfies BetterAuthOptions;

export const auth: ReturnType<typeof betterAuth<typeof options>> =
  betterAuth(options);
