import { createAccessControl } from 'better-auth/plugins/access';
import {
  adminAc,
  defaultStatements as adminDefaultStatements,
} from 'better-auth/plugins/admin/access';
import { defaultStatements as orgDefaultStatements } from 'better-auth/plugins/organization/access';

const statement = {
  ...adminDefaultStatements, // user, session
  ...orgDefaultStatements, // organization
} as const;

export const ac = createAccessControl(statement);

// Admin role - has full access to everything
export const superAdminRole = ac.newRole({
  ...adminAc.statements,
  organization: ['update', 'delete'],
  member: ['create', 'update', 'delete'], // Can manage all members
  invitation: ['create', 'cancel'], // Can invite to any org
  team: ['create', 'update', 'delete'], // Full team control
});

export const adminRole = ac.newRole({});

// Customer role -
export const customerRole = ac.newRole({});

// Organization-specific roles (OrganizationMember.role)
export const ownerRole = ac.newRole({
  organization: ['update', 'delete'], // Manage their org
  member: ['create', 'update', 'delete'], // Manage org members
  invitation: ['create', 'cancel'], // Invite to their org
});
