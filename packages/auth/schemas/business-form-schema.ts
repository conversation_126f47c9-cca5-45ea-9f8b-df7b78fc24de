import { z } from 'zod';

// Define the schema with snake_case keys for backend compatibility
export const businessFormSchema = z.object({
  // API Credentials
  client_id: z.string().min(1, 'Client ID is required'),
  client_secret: z.string().min(1, 'Client Secret is required'),

  // Company Information
  name: z.string().min(1, 'Business name is required'),

  // Registration Information
  tin_code: z.string().min(1, 'TIN code is required'),
  registration_number: z.string().min(1, 'Registration number is required'),
  registration_type: z.enum(['BRN', 'NRIC', 'PASSPORT', 'ARMY'], {
    errorMap: () => ({ message: 'Please select a valid registration type' }),
  }),
  applicable_tax_types: z
    .array(z.string())
    .min(1, 'At least one tax type must be selected'),
  sales_tax_rates: z.array(z.string()).optional(),
  service_tax_rates: z.array(z.string()).optional(),
  sst_registration_number: z.string().optional(),
  tourism_tax_registration_number: z.string().optional(),

  // Business Details
  business_activity_description: z
    .string()
    .min(1, 'Business activity description is required'),
  msic_code: z.string().min(1, 'MSIC code is required'),
  business_type: z
    .enum(
      [
        'retail',
        'healthcare',
        'education',
        'technology',
        'finance',
        'manufacturing',
        'services',
        'other',
      ],
      {
        errorMap: () => ({ message: 'Please select a valid business type' }),
      }
    )
    .optional(),

  // Contact Information
  country: z.string().min(1, 'Country is required'),
  state: z.string().min(1, 'State is required'),
  zip_code: z.string().min(1, 'ZIP code is required'),
  city: z.string().min(1, 'City is required'),
  address: z.string().min(1, 'Address is required'),
  phone: z.string().min(1, 'Phone number is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
});

// Step 1: API Credentials
export const apiCredentialsSchema = z.object({
  client_id: z.string().min(1, 'Client ID is required'),
  client_secret: z.string().min(1, 'Client Secret is required'),
});

// Step 2: Company and Business Information (Merged)
export const companyInfoSchema = z.object({
  name: z.string().min(1, 'Business name is required'),
  tin_code: z.string().min(1, 'TIN code is required'),
  registration_number: z.string().min(1, 'Registration number is required'),
  registration_type: z.enum(['BRN', 'NRIC', 'PASSPORT', 'ARMY'], {
    errorMap: () => ({ message: 'Please select a valid registration type' }),
  }),
  business_activity_description: z
    .string()
    .min(1, 'Business activity description is required'),
  msic_code: z.string().min(1, 'MSIC code is required'),
  business_type: z
    .enum(
      [
        'retail',
        'healthcare',
        'education',
        'technology',
        'finance',
        'manufacturing',
        'services',
        'other',
      ],
      {
        errorMap: () => ({ message: 'Please select a valid business type' }),
      }
    )
    .optional(),
});

// Step 3: Business Tax Situations
export const taxSituationsSchema = z.object({
  applicable_tax_types: z
    .array(z.string())
    .min(1, 'At least one tax type must be selected'),
  // Add fields for specific tax rates
  sales_tax_rates: z.array(z.string()).optional(),
  service_tax_rates: z.array(z.string()).optional(),
  sst_registration_number: z.string().optional(),
  tourism_tax_registration_number: z.string().optional(),
});

// Step 4: Contact Information
export const contactInfoSchema = z.object({
  country: z.string().min(1, 'Country is required'),
  state: z.string().min(1, 'State is required'),
  zip_code: z.string().min(1, 'ZIP code is required'),
  city: z.string().min(1, 'City is required'),
  address: z.string().min(1, 'Address is required'),
  phone: z.string().min(1, 'Phone number is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
});

// Define types for use in the application
export type BusinessFormData = z.infer<typeof businessFormSchema>;
export type ApiCredentialsData = z.infer<typeof apiCredentialsSchema>;
export type CompanyInfoData = z.infer<typeof companyInfoSchema>;
export type TaxSituationsData = z.infer<typeof taxSituationsSchema>;
export type ContactInfoData = z.infer<typeof contactInfoSchema>;
