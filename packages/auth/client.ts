import {
  adminClient,
  apiKeyClient,
  inferAdditionalFields,
  magicLinkClient,
  organizationClient,
} from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/react';
import { ownerRole, superAdminRole } from './permissions';
import { ac } from './permissions';
import type { auth } from './server';

// type AuthClient = ReturnType<
//   typeof createAuthClient<{
//     plugins: [
//       ReturnType<typeof adminClient>,
//       // ReturnType<typeof stripeClient>,
//       ReturnType<typeof magicLinkClient>,
//       // ReturnType<typeof organizationClient>,
//     ];
//   }>
// >;

const authClient = createAuthClient({
  plugins: [
    // stripeClient(),
    // customSessionClient<typeof auth>(),
    adminClient({
      ac,
      roles: {
        superAdmin: superAdminRole,
      },
    }),
    organizationClient({
      ac,
      roles: { owner: ownerRole },
    }),
    magicLinkClient(),
    apiKeyClient(),
    inferAdditionalFields<typeof auth>(),
  ],
});

export const {
  signIn,
  signOut,
  signUp,
  useSession,
  admin,
  organization,
  useActiveOrganization,
  apiKey,
} = authClient;

export default authClient;
export type User = typeof authClient.$Infer.Session.user;
export type Session = typeof authClient.$Infer.Session.session;
