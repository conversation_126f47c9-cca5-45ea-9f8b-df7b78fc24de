'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { BusinessFormData } from '../schemas/business-form-schema';

interface OnboardingStore {
  // Form data
  formData: Partial<BusinessFormData>;

  // Step management
  currentStep: number;
  totalSteps: number;

  // Actions
  updateFormData: (data: Partial<BusinessFormData>) => void;
  setCurrentStep: (step: number) => void;
  reset: () => void;

  // Status
  isComplete: boolean;
  setComplete: (complete: boolean) => void;
}

// Initial form data with empty values
const initialFormData: Partial<BusinessFormData> = {
  client_id: '',
  client_secret: '',
  name: '',
  tin_code: '',
  registration_number: '',
  registration_type: 'BRN',
  applicable_tax_types: [],
  sales_tax_rates: [],
  service_tax_rates: [],
  sst_registration_number: '',
  tourism_tax_registration_number: '',
  business_activity_description: '',
  msic_code: '',
  country: '',
  state: '',
  zip_code: '',
  city: '',
  address: '',
  phone: '',
  email: '',
};

// Create the store with persistence
export const useOnboardingStore = create<OnboardingStore>()(
  persist(
    (set) => ({
      // Initial state
      formData: initialFormData,
      currentStep: 0,
      totalSteps: 4,
      isComplete: false,

      // Actions
      updateFormData: (data) =>
        set((state) => ({
          formData: { ...state.formData, ...data },
        })),

      setCurrentStep: (step) => set({ currentStep: step }),

      setComplete: (complete) => set({ isComplete: complete }),

      reset: () =>
        set({
          formData: initialFormData,
          currentStep: 0,
          isComplete: false,
        }),
    }),
    {
      name: 'onboarding-store',
    }
  )
);
