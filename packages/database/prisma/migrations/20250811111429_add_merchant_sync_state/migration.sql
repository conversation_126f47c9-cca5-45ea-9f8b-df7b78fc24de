-- CreateTable
CREATE TABLE "merchant_sync_state" (
    "id" TEXT NOT NULL,
    "shop_id" TEXT NOT NULL,
    "access_token" TEXT NOT NULL,
    "backend_user_id" TEXT,
    "backend_organization_id" TEXT,
    "sync_status" TEXT NOT NULL DEFAULT 'PENDING',
    "sync_errors" TEXT,
    "last_sync_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "merchant_sync_state_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "merchant_sync_state_shop_id_key" ON "merchant_sync_state"("shop_id");

-- CreateIndex
CREATE INDEX "merchant_sync_state_shop_id_idx" ON "merchant_sync_state"("shop_id");

-- CreateIndex
CREATE INDEX "merchant_sync_state_backend_user_id_idx" ON "merchant_sync_state"("backend_user_id");

-- CreateIndex
CREATE INDEX "merchant_sync_state_backend_organization_id_idx" ON "merchant_sync_state"("backend_organization_id");

-- CreateIndex
CREATE INDEX "merchant_sync_state_sync_status_idx" ON "merchant_sync_state"("sync_status");

-- AddForeignKey
ALTER TABLE "merchant_sync_state" ADD CONSTRAINT "merchant_sync_state_backend_user_id_fkey" FOREIGN KEY ("backend_user_id") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "merchant_sync_state" ADD CONSTRAINT "merchant_sync_state_backend_organization_id_fkey" FOREIGN KEY ("backend_organization_id") REFERENCES "organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;
