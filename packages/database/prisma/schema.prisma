// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/client"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

//// User model (core entity for all roles)

model User {
  id            String    @id @default(uuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @map("email_verified")
  image         String?
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  sessions      Session[]
  accounts      Account[]
  role          String    @default("customer") // super-admin, admin, customer
  banned        Boolean?  @default(false)
  banReason     String?   @map("ban_reason")
  banExpires    DateTime? @map("ban_expires")

  firstName String?   @map("first_name")
  lastName  String?   @map("last_name")
  phone     String?
  dob       DateTime?

  // organization fields
  members     Member[]
  invitations Invitation[]
  apiKeys     ApiKey[]     @relation("CreatedByUser")

  apikeys Apikey[]

  // Shopify integration fields
  merchantSyncStates MerchantSyncState[]

  @@map("user")
}

//// Organization models

model Organization {
  id          String       @id @default(uuid())
  name        String
  slug        String?
  logo        String?
  createdAt   DateTime     @default(now()) @map("created_at")
  metadata    String?
  members     Member[]
  invitations Invitation[]
  apiKeys     ApiKey[]

  premiumTierId   String?          @map("premium_tier_id") // Reference to the premium tier
  premiumTier     PremiumTier?     @relation(fields: [premiumTierId], references: [id])
  premiumUpgrades PremiumUpgrade[]

  // Shopify integration fields
  merchantSyncStates MerchantSyncState[]

  @@unique([slug])
  @@map("organization")
}

model Member {
  id             String       @id @default(uuid())
  organizationId String       @map("organization_id")
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String       @map("user_id")
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           String // owner, organizer
  createdAt      DateTime     @default(now()) @map("created_at")

  @@map("member")
}

model Invitation {
  id             String       @id @default(uuid())
  organizationId String       @map("organization_id")
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           String?
  status         String
  expiresAt      DateTime     @map("expires_at")
  inviterId      String       @map("inviter_id")
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

  @@map("invitation")
}

//// Auth models

model Session {
  id             String   @id @default(uuid())
  expiresAt      DateTime @map("expires_at")
  token          String   @unique
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")
  ipAddress      String?  @map("ip_address")
  userAgent      String?  @map("user_agent")
  userId         String   @map("user_id")
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  impersonatedBy String?  @map("impersonated_by")

  activeOrganizationId   String? @map("active_organization_id")
  activeOrganizationName String? @map("active_organization_name")
  organizerId            String? @map("organizer_id")

  @@map("session")
}

model Account {
  id                    String    @id @default(uuid())
  accountId             String?   @map("account_id") // oauth account id
  providerId            String    @map("provider_id") // account type: credential, google, apple, oauth...
  userId                String    @map("user_id")
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?   @map("access_token")
  refreshToken          String?   @map("refresh_token")
  idToken               String?   @map("id_token")
  accessTokenExpiresAt  DateTime? @map("access_token_expires_at")
  refreshTokenExpiresAt DateTime? @map("refresh_token_expires_at")
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  @@map("account")
}

model Verification {
  id         String    @id @default(uuid())
  identifier String
  value      String
  expiresAt  DateTime  @map("expires_at")
  createdAt  DateTime? @default(now()) @map("created_at")
  updatedAt  DateTime? @updatedAt @map("updated_at")

  @@map("verification")
}

model Jwks {
  id         String   @id
  publicKey  String
  privateKey String
  createdAt  DateTime

  @@map("jwks")
}

//// System

model PremiumTier {
  id                  String           @id @default(uuid())
  name                String // e.g., "Basic", "Standard", "Pro"
  description         String?
  maxInvoicesPerMonth Int // Maximum number of invoices allowed for this tier
  price               Decimal // Price for this tier
  isActive            Boolean          @default(true)
  createdAt           DateTime         @default(now()) @map("created_at")
  updatedAt           DateTime         @updatedAt @map("updated_at")
  premiumUpgrades     PremiumUpgrade[]
  organizations       Organization[]

  @@map("premium_tier")
}

model PremiumUpgrade {
  id             String               @id
  organizationId String               @map("organization_id")
  premiumTierId  String?              @map("premium_tier_id")
  amount         Decimal
  status         PremiumUpgradeStatus @default(pending)
  paymentStatus  PaymentStatus        @default(pending) @map("payment_status")
  transactionId  String               @map("transaction_id") // Payment gateway transaction ID
  metadata       Json? // For storing additional data like upgradeType, paymentMethod, etc.
  createdAt      DateTime             @default(now()) @map("created_at")
  updatedAt      DateTime             @updatedAt @map("updated_at")

  // Relations
  premiumTier  PremiumTier? @relation(fields: [premiumTierId], references: [id], onDelete: SetNull)
  organization Organization @relation(fields: [organizationId], references: [id])

  @@index([premiumTierId])
  @@index([transactionId])
  @@map("premium_upgrade")
}

//// API Keys

model ApiKey {
  id             String       @id @default(uuid())
  name           String
  key            String       @unique
  organizationId String       @map("organization_id")
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdAt      DateTime     @default(now()) @map("created_at")
  expiresAt      DateTime?    @map("expires_at")
  lastUsedAt     DateTime?    @map("last_used_at")
  createdById    String       @map("created_by_id")
  createdBy      User         @relation("CreatedByUser", fields: [createdById], references: [id], onDelete: Cascade)

  @@map("api_key")
}

//// Enums

enum VerificationStatus {
  pending
  verified
  rejected
}

enum PayoutFrequency {
  weekly
  biweekly
  monthly
}

enum PremiumUpgradeStatus {
  pending
  completed
  failed
  cancelled
}

enum PaymentStatus {
  pending
  paid
  cancelled
  refunded
  custom
}

model Apikey {
  id                  String    @id
  name                String?
  start               String?
  prefix              String?
  key                 String
  userId              String
  user                User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  refillInterval      Int?
  refillAmount        Int?
  lastRefillAt        DateTime?
  enabled             Boolean?
  rateLimitEnabled    Boolean?
  rateLimitTimeWindow Int?
  rateLimitMax        Int?
  requestCount        Int?
  remaining           Int?
  lastRequest         DateTime?
  expiresAt           DateTime?
  createdAt           DateTime
  updatedAt           DateTime
  permissions         String?
  metadata            String?

  @@map("apikey")
}

//// Shopify Integration Models

model MerchantSyncState {
  id                     String    @id @default(uuid())
  shopId                 String    @unique @map("shop_id") // Shopify shop domain (e.g., "store.myshopify.com")
  accessToken            String    @map("access_token") // Shopify access token for API communication
  backendUserId          String?   @map("backend_user_id") // Reference to created User
  backendOrganizationId  String?   @map("backend_organization_id") // Reference to created Organization
  syncStatus             String    @default("PENDING") @map("sync_status") // PENDING, UP_TO_DATE, FAILED, SYNCING
  syncErrors             String?   @map("sync_errors") // JSON string of sync errors
  lastSyncAt             DateTime? @map("last_sync_at") // Last successful sync timestamp
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")

  // Relations
  backendUser         User?         @relation(fields: [backendUserId], references: [id], onDelete: SetNull)
  backendOrganization Organization? @relation(fields: [backendOrganizationId], references: [id], onDelete: SetNull)

  @@index([shopId])
  @@index([backendUserId])
  @@index([backendOrganizationId])
  @@index([syncStatus])
  @@map("merchant_sync_state")
}
