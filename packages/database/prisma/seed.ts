import { Prisma, PrismaClient, type User } from '@prisma/client';
import { auth } from '@repo/auth/server';

const prisma = new PrismaClient();

const users = [
  {
    email: '<EMAIL>',
    name: 'Admin',
    emailVerified: true,
    role: 'super-admin',
    organization: 'my-invoice',
  },
  {
    email: '<EMAIL>',
    name: '<PERSON>',
    emailVerified: true,
    role: 'customer',
  },
  {
    email: '<EMAIL>',
    name: '<PERSON>',
    emailVerified: true,
    role: 'customer',
  },
];

const premiumTiers = [
  {
    name: 'Basic Premium',
    description: 'Entry-level premium tier with increased invoice capacity',
    maxInvoicesPerMonth: 50,
    price: 19.99,
    isActive: true,
  },
  {
    name: 'Standard Premium',
    description: 'Mid-level premium tier with higher invoice capacity',
    maxInvoicesPerMonth: 100,
    price: 39.99,
    isActive: true,
  },
  {
    name: 'Pro Premium',
    description: 'Top-level premium tier with maximum invoice capacity',
    maxInvoicesPerMonth: 200,
    price: 79.99,
    isActive: true,
  },
  {
    name: 'Enterprise',
    description: 'Enterprise-level tier for large events',
    maxInvoicesPerMonth: 500,
    price: 149.99,
    isActive: false,
  },
];

async function main() {
  // Log start message without using process.stdout directly
  process.stdout.write('Starting database seed...\n');

  // Clean up existing data
  await prisma.$transaction([
    prisma.organization.deleteMany(),
    prisma.premiumTier.deleteMany(),
    prisma.account.deleteMany(),
    prisma.session.deleteMany(),
    prisma.user.deleteMany(),
  ]);

  // Create organization
  const myInvoiceOrg = await prisma.organization.create({
    data: {
      name: 'My Invoice',
      slug: 'my-invoice',
    },
  });

  const usersCreated: User[] = [];

  // Create users
  for (const userData of users) {
    const user = await prisma.user.create({
      data: {
        email: userData.email,
        name: userData.name,
        emailVerified: userData.emailVerified,
        role: userData.role,
      },
    });

    // create account for password based login
    const ctx = await auth.$context;
    const hash = await ctx.password.hash('abcd1234');

    await prisma.account.create({
      data: {
        accountId: user.id,
        userId: user.id,
        providerId: 'credential',
        password: hash,
      },
    });

    // Assign users to organizations
    if (userData.organization) {
      await prisma.member.create({
        data: {
          organizationId: myInvoiceOrg.id,
          userId: user.id,
          role: 'owner',
          createdAt: new Date(),
        },
      });
    }

    usersCreated.push(user);
  }

  // Create Premium Tiers
  for (const tierData of premiumTiers) {
    await prisma.premiumTier.create({
      data: {
        name: tierData.name,
        description: tierData.description,
        maxInvoicesPerMonth: tierData.maxInvoicesPerMonth,
        price: new Prisma.Decimal(tierData.price),
        isActive: tierData.isActive,
      },
    });
  }

  // Log completion message without using process.stdout directly
  process.stdout.write('Seeding complete!\n');
}

main()
  .catch((e) => {
    // Log error without using process.stderr directly
    process.stderr.write(`Error during seeding: ${e}\n`);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
