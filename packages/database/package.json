{"name": "@repo/database", "version": "0.0.0", "main": "./index.ts", "types": "./index.ts", "scripts": {"analyze": "prisma generate --no-hints --schema=./prisma/schema.prisma", "build": "prisma generate --no-hints --schema=./prisma/schema.prisma", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "postinstall": "prisma generate --no-hints --schema=./prisma/schema.prisma"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "6.6.0", "@supabase/supabase-js": "^2.49.4", "@t3-oss/env-nextjs": "^0.12.0", "server-only": "^0.0.1", "undici": "^7.8.0", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "bufferutil": "^4.0.9", "prisma": "6.6.0", "supabase": "^2.20.12", "tsx": "^4.19.3", "typescript": "^5.8.3"}}