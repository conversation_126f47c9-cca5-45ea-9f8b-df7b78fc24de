import 'server-only';
import { auth } from '@repo/auth/server';
import { headers } from 'next/headers';
import { Svix } from 'svix';
import { keys } from '../keys';

const svixToken = keys().SVIX_TOKEN;

export const send = async (eventType: string, payload: object) => {
  if (!svixToken) {
    throw new Error('SVIX_TOKEN is not set');
  }

  const svix = new Svix(svixToken);

  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.session.activeOrganizationId) {
    return;
  }

  return svix.message.create(session.session.activeOrganizationId, {
    eventType,
    payload: {
      eventType,
      ...payload,
    },
    application: {
      name: session.session.activeOrganizationId,
      uid: session.session.activeOrganizationId,
    },
  });
};

export const getAppPortal = async () => {
  if (!svixToken) {
    throw new Error('SVIX_TOKEN is not set');
  }

  const svix = new Svix(svixToken);

  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.session.activeOrganizationId) {
    return;
  }

  return svix.authentication.appPortalAccess(
    session.session.activeOrganizationId,
    {
      application: {
        name: session.session.activeOrganizationId,
        uid: session.session.activeOrganizationId,
      },
    }
  );
};
