# JWT Auth Flow: Vercel Auth Package & AdonisJS Backend

This document describes the authentication flow between the Vercel-hosted `@repo/auth` package (using better-auth with JWT plugin) and a separate AdonisJS backend. It covers both perspectives and highlights best practices for secure, stateless authentication.

---

## 1. Overview

- **Auth Service**: Hosted as serverless functions on Vercel (`/packages/auth`), provides authentication, organization, and premium upgrade features. Uses [better-auth](https://better-auth.dev/) with the JWT plugin.
- **AdonisJS Backend**: Consumes JWTs issued by the auth service for user authentication and authorization.

---

## 2. Auth Service (Vercel) Flow

### 2.1. Setup
- Add the JWT plugin in `server.ts`:
  ```ts
  import { jwt } from 'better-auth/plugins';
  betterAuth({
    plugins: [
      jwt(),
      // ...other plugins
    ]
  });
  ```
- Run migration/generation to set up the JWKS table:
  ```bash
  npm run better-auth
  # or
  npx @better-auth/cli migrate
  ```

### 2.2. Endpoints Provided
- **`/api/auth/token`**: Returns a JWT for the authenticated user/session.
- **`/api/auth/jwks`**: Returns the JWKS (public keys) for verifying JWTs.

### 2.3. Retrieving a JWT
- Client sends a request to `/api/auth/token` with the session token in the `Authorization` header:
  ```js
  await fetch('/api/auth/token', {
    headers: { 'Authorization': `Bearer ${sessionToken}` }
  });
  // Response: { token: 'eyJ...' }
  ```
- Alternatively, after login, the JWT is available in the `set-auth-jwt` response header.

### 2.4. Customization
- You can customize the JWT payload, issuer, audience, and expiration in the plugin config.

---

## 3. AdonisJS Backend Flow

### 3.1. Fetching the JWKS
- On startup (or periodically), fetch and cache the JWKS from the auth service:
  ```ts
  import { createRemoteJWKSet, jwtVerify } from 'jose';

  const JWKS = createRemoteJWKSet(new URL('https://your-vercel-app.vercel.app/api/auth/jwks'));
  ```

### 3.2. Authenticating Requests
- For protected endpoints, expect the JWT in the `Authorization` header.
- Verify the JWT using the cached JWKS:
  ```ts
  async function validateToken(token) {
    const { payload } = await jwtVerify(token, JWKS, {
      issuer: 'https://your-vercel-app.vercel.app',
      audience: 'https://your-vercel-app.vercel.app',
    });
    return payload;
  }
  ```
- Attach the payload (user/org info) to the request context for downstream use.

### 3.3. Example Middleware
```ts
import { createRemoteJWKSet, jwtVerify } from 'jose';

const JWKS = createRemoteJWKSet(new URL('https://your-vercel-app.vercel.app/api/auth/jwks'));

async function authMiddleware(ctx, next) {
  const token = ctx.request.header('Authorization')?.replace('Bearer ', '');
  if (!token) return ctx.response.unauthorized();
  try {
    const { payload } = await jwtVerify(token, JWKS, {
      issuer: 'https://your-vercel-app.vercel.app',
      audience: 'https://your-vercel-app.vercel.app',
    });
    ctx.auth = { user: payload };
    await next();
  } catch (err) {
    return ctx.response.unauthorized();
  }
}
```

---

## 4. Security & Best Practices
- Always use HTTPS for all communication.
- Cache the JWKS and only refetch if you see a new `kid` in the JWT header.
- Rotate keys periodically as recommended by better-auth.
- Never expose your private key.

---

## 5. Summary
- The Vercel-hosted auth service issues JWTs and exposes public keys via JWKS.
- The AdonisJS backend verifies JWTs using the JWKS endpoint, enabling stateless, secure authentication across services.

---

**For more details, see the [better-auth JWT docs](https://better-auth.dev/docs/plugins/jwt).**
