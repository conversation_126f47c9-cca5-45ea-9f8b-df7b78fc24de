/**
 * Merchant Onboarding Service
 * 
 * Handles the complete onboarding flow for new Shopify app installations,
 * including automatic user creation and organization linking in the backend system.
 * 
 * Requirements: 1.1, 1.4, 11.4
 */

import { getAuthenticatedClient } from './backend-api-client';
import type { SyncQueueManager } from './sync-queue-manager';
import { getSyncQueueManager } from './sync-queue-factory';
import type { MonitoringService } from './monitoring-service';
import { getMonitoringService } from './monitoring-service';
import {
  extractShopifyMerchantData,
  extractShopifyStoreData,
  transformShopifyMerchantToUser,
  validateShopifyMerchantData,
  validateShopifyStoreData,
} from './shopify-data-transformers';
import type {
  SyncResult,
  ShopifyMerchantData,
  ShopifyStoreData,
} from './backend-api-client';
import type { BusinessFormData } from '../routes/components/business_form';
import prisma from '../db.server';

/**
 * Onboarding result interface
 */
export interface OnboardingResult {
  success: boolean;
  userId?: string;
  organizationId?: string;
  errors: string[];
  warnings: string[];
  correlationId: string;
  syncOperationIds: string[];
}

/**
 * Onboarding configuration
 */
export interface OnboardingConfig {
  enableAutoSync: boolean;
  skipValidation: boolean;
  retryOnFailure: boolean;
  maxRetries: number;
  timeoutMs: number;
}

/**
 * Onboarding context from Shopify
 */
export interface OnboardingContext {
  shop: string;
  accessToken: string;
  session: {
    shop: string;
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    userId?: bigint | null;
  };
  storeInfo?: {
    name?: string | null;
    phone?: string | null;
    email?: string | null;
    registrationNumber?: string | null;
    tinCode?: string | null;
  };
  shopData?: {
    name?: string;
    currency?: string;
    timezone?: string;
    planName?: string;
    country?: string;
  };
}

/**
 * Onboarding error types
 */
export enum OnboardingErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  BACKEND_API_ERROR = 'BACKEND_API_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  SYNC_QUEUE_ERROR = 'SYNC_QUEUE_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
}

/**
 * Onboarding error class
 */
export class OnboardingError extends Error {
  constructor(
    message: string,
    public type: OnboardingErrorType,
    public correlationId?: string,
    public cause?: Error
  ) {
    super(message);
    this.name = 'OnboardingError';
  }
}

/**
 * Default onboarding configuration
 */
const DEFAULT_ONBOARDING_CONFIG: OnboardingConfig = {
  enableAutoSync: true,
  skipValidation: false,
  retryOnFailure: true,
  maxRetries: 3,
  timeoutMs: 30000, // 30 seconds
};

/**
 * Merchant Onboarding Service
 */
export class MerchantOnboardingService {
  private shop: string;
  private syncQueueManager: SyncQueueManager;
  private monitoringService: MonitoringService;
  private config: OnboardingConfig;

  constructor(
    shop: string,
    config: Partial<OnboardingConfig> = {}
  ) {
    this.shop = shop;
    this.config = { ...DEFAULT_ONBOARDING_CONFIG, ...config };
    this.syncQueueManager = getSyncQueueManager();
    this.monitoringService = getMonitoringService();
  }

  /**
   * Execute complete merchant onboarding flow
   * 
   * @param request - Remix request object for authentication
   * @param context - Onboarding context from Shopify
   * @returns OnboardingResult
   */
  async onboardMerchant(request: Request, context: OnboardingContext): Promise<OnboardingResult> {
    const correlationId = this.monitoringService.generateCorrelationId();
    const startTime = Date.now();

    try {
      this.monitoringService.logBusinessEvent({
        type: 'MERCHANT_ONBOARDING_STARTED',
        shopId: context.shop,
        correlationId,
        timestamp: new Date(),
        data: {
          hasStoreInfo: !!context.storeInfo,
          hasShopData: !!context.shopData,
        },
      });

      // Step 1: Extract and validate Shopify data
      const { merchantData, storeData } = await this.extractAndValidateData(context, correlationId);

      // Step 2: Create or update user in backend
      const userResult = await this.createOrUpdateUser(request, merchantData, correlationId);

      // Step 3: Link store to organization (if user creation was successful)
      let organizationResult: SyncResult | undefined;
      if (userResult.success && userResult.userId) {
        organizationResult = await this.linkStoreToOrganization(
          request,
          userResult.userId,
          storeData,
          correlationId
        );
      }

      // Step 4: Update merchant sync state
      await this.updateMerchantSyncState(
        context.shop,
        userResult.userId,
        organizationResult?.organizationId,
        correlationId
      );

      // Step 5: Queue ongoing sync operations if enabled
      const syncOperationIds: string[] = [];
      if (this.config.enableAutoSync && userResult.success) {
        const queueResult = await this.queueOngoingSyncOperations(
          request,
          context.shop,
          merchantData,
          storeData,
          userResult.userId as string,
          correlationId
        );
        syncOperationIds.push(...queueResult);
      }

      const duration = Date.now() - startTime;
      const result: OnboardingResult = {
        success: userResult.success && (!organizationResult || organizationResult.success),
        userId: userResult.userId,
        organizationId: organizationResult?.organizationId,
        errors: [
          ...(userResult.errors || []),
          ...(organizationResult?.errors || []),
        ],
        warnings: [
          ...(userResult.warnings || []),
          ...(organizationResult?.warnings || []),
        ],
        correlationId,
        syncOperationIds,
      };

      this.monitoringService.logBusinessEvent({
        type: 'MERCHANT_ONBOARDING_COMPLETED',
        shopId: context.shop,
        correlationId,
        timestamp: new Date(),
        data: {
          success: result.success,
          userId: result.userId,
          organizationId: result.organizationId,
          duration,
          errorCount: result.errors.length,
          warningCount: result.warnings.length,
        },
      });

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;

      this.monitoringService.logError(error as Error, {
        correlationId,
        shopId: context.shop,
        operation: 'merchant_onboarding',
        duration,
      });

      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown onboarding error'],
        warnings: [],
        correlationId,
        syncOperationIds: [],
      };
    }
  }

  /**
   * Handle onboarding failure with retry logic
   * 
   * @param request - Remix request object for authentication
   * @param context - Onboarding context
   * @param attempt - Current attempt number
   * @returns OnboardingResult
   */
  async handleOnboardingWithRetry(
    request: Request,
    context: OnboardingContext,
    attempt: number = 1
  ): Promise<OnboardingResult> {
    try {
      const result = await this.onboardMerchant(request, context);

      if (!result.success && this.config.retryOnFailure && attempt < this.config.maxRetries) {
        // Wait before retry with exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));

        this.monitoringService.logBusinessEvent({
          type: 'MERCHANT_ONBOARDING_RETRY',
          shopId: context.shop,
          correlationId: result.correlationId,
          timestamp: new Date(),
          data: {
            attempt,
            delay,
            errors: result.errors,
          },
        });

        return this.handleOnboardingWithRetry(request, context, attempt + 1);
      }

      return result;

    } catch (error) {
      if (this.config.retryOnFailure && attempt < this.config.maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.handleOnboardingWithRetry(request, context, attempt + 1);
      }

      throw error;
    }
  }

  /**
   * Check if merchant is already onboarded
   * 
   * @param shop - Shop domain
   * @returns boolean indicating if merchant is onboarded
   */
  async isMerchantOnboarded(shop: string): Promise<boolean> {
    try {
      const syncState = await prisma.merchantSyncState.findUnique({
        where: { shopId: shop },
      });

      return !!(syncState?.backendUserId);
    } catch (error) {
      this.monitoringService.logError(error as Error, {
        shopId: shop,
        operation: 'check_onboarding_status',
      });
      return false;
    }
  }

  /**
   * Get onboarding status for a merchant
   * 
   * @param shop - Shop domain
   * @returns Onboarding status information
   */
  async getOnboardingStatus(shop: string): Promise<{
    isOnboarded: boolean;
    userId?: string;
    organizationId?: string;
    lastSyncAt?: Date;
    syncStatus?: string;
  }> {
    try {
      const syncState = await prisma.merchantSyncState.findUnique({
        where: { shopId: shop },
      });

      return {
        isOnboarded: !!(syncState?.backendUserId),
        userId: syncState?.backendUserId || undefined,
        organizationId: syncState?.backendOrganizationId || undefined,
        lastSyncAt: syncState?.lastSyncAt,
        syncStatus: syncState?.syncStatus,
      };
    } catch (error) {
      this.monitoringService.logError(error as Error, {
        shopId: shop,
        operation: 'get_onboarding_status',
      });

      return { isOnboarded: false };
    }
  }

  // Private helper methods

  /**
   * Extract and validate Shopify data
   */
  private async extractAndValidateData(
    context: OnboardingContext,
    correlationId: string
  ): Promise<{
    merchantData: ShopifyMerchantData;
    storeData: ShopifyStoreData;
  }> {
    // Extract merchant data
    const merchantData = extractShopifyMerchantData(context.session, context.storeInfo);

    // Extract store data
    const storeData = extractShopifyStoreData({
      shop: context.shop,
      ...context.shopData,
    });

    // Validate data if not skipped
    if (!this.config.skipValidation) {
      const merchantValidation = validateShopifyMerchantData(merchantData);
      const storeValidation = validateShopifyStoreData(storeData);

      if (!merchantValidation.isValid) {
        throw new OnboardingError(
          `Merchant data validation failed: ${merchantValidation.errors[0].message}`,
          OnboardingErrorType.VALIDATION_ERROR,
          correlationId
        );
      }

      if (!storeValidation.isValid) {
        throw new OnboardingError(
          `Store data validation failed: ${storeValidation.errors[0].message}`,
          OnboardingErrorType.VALIDATION_ERROR,
          correlationId
        );
      }
    }

    return { merchantData, storeData };
  }

  /**
   * Create or update user in backend
   */
  private async createOrUpdateUser(
    request: Request,
    merchantData: ShopifyMerchantData,
    correlationId: string
  ): Promise<SyncResult> {
    try {
      // Check if user already exists by looking up sync state
      const existingSyncState = await prisma.merchantSyncState.findUnique({
        where: { shopId: merchantData.shopDomain },
      });

      if (existingSyncState?.backendUserId) {
        // Update existing user
        const userData = transformShopifyMerchantToUser(merchantData);
        const client = await getAuthenticatedClient(request, this.shop);
        const user = await client.updateUser(
          existingSyncState.backendUserId,
          userData,
          correlationId
        );

        return {
          success: true,
          userId: user.id,
          warnings: ['User updated successfully'],
        };
      } else {
        // Create new user
        const userData = transformShopifyMerchantToUser(merchantData);
        const client = await getAuthenticatedClient(request, this.shop);
        const user = await client.createUser(userData, correlationId);

        return {
          success: true,
          userId: user.id,
          warnings: ['User created successfully'],
        };
      }
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Failed to create/update user'],
      };
    }
  }

  /**
   * Link store to organization
   */
  private async linkStoreToOrganization(
    request: Request,
    userId: string,
    storeData: ShopifyStoreData,
    correlationId: string
  ): Promise<SyncResult> {
    try {
      // Use the client directly
      const client = await getAuthenticatedClient(request, this.shop);
      return await client.linkShopifyStore(userId, storeData, correlationId);
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Failed to link store to organization'],
      };
    }
  }

  /**
   * Update merchant sync state in database
   */
  private async updateMerchantSyncState(
    shop: string,
    userId?: string,
    organizationId?: string,
    correlationId?: string
  ): Promise<void> {
    try {
      await prisma.merchantSyncState.upsert({
        where: { shopId: shop },
        update: {
          backendUserId: userId,
          backendOrganizationId: organizationId,
          lastSyncAt: new Date(),
          syncStatus: userId ? 'UP_TO_DATE' : 'FAILED',
          syncErrors: userId ? null : JSON.stringify(['User creation failed']),
          updatedAt: new Date(),
        },
        create: {
          shopId: shop,
          backendUserId: userId,
          backendOrganizationId: organizationId,
          lastSyncAt: new Date(),
          syncStatus: userId ? 'UP_TO_DATE' : 'FAILED',
          syncErrors: userId ? null : JSON.stringify(['User creation failed']),
        },
      });
    } catch (error) {
      this.monitoringService.logError(error as Error, {
        correlationId,
        shopId: shop,
        operation: 'update_merchant_sync_state',
      });
      throw new OnboardingError(
        'Failed to update merchant sync state',
        OnboardingErrorType.DATABASE_ERROR,
        correlationId,
        error as Error
      );
    }
  }

  /**
   * Queue ongoing sync operations
   */
  private async queueOngoingSyncOperations(
    request: Request,
    shop: string,
    merchantData: ShopifyMerchantData,
    storeData: ShopifyStoreData,
    userId: string,
    correlationId: string
  ): Promise<string[]> {
    const operationIds: string[] = [];

    try {
      // Queue periodic user sync operation
      const userSyncId = await this.syncQueueManager.enqueueSync({
        shopId: shop,
        correlationId,
        timestamp: new Date().toISOString(),
        data: {
          type: 'USER_SYNC',
          shopifyMerchantData: merchantData,
          backendUserId: userId,
        },
      });
      operationIds.push(userSyncId);

      // Queue periodic organization sync operation
      const orgSyncId = await this.syncQueueManager.enqueueSync({
        shopId: shop,
        correlationId,
        timestamp: new Date().toISOString(),
        data: {
          type: 'ORGANIZATION_SYNC',
          shopifyStoreData: storeData,
          backendUserId: userId,
        },
      });
      operationIds.push(orgSyncId);

      return operationIds;
    } catch (error) {
      this.monitoringService.logError(error as Error, {
        correlationId,
        shopId: shop,
        operation: 'queue_ongoing_sync_operations',
      });
      throw new OnboardingError(
        'Failed to queue ongoing sync operations',
        OnboardingErrorType.SYNC_QUEUE_ERROR,
        correlationId,
        error as Error
      );
    }
  }
}

// Business Data Management Functions

/**
 * Error handling helper for business data operations
 */
// biome-ignore lint/suspicious/noExplicitAny: Error can be any
function handleBusinessDataError(error: any) {
  if (
    error.error === "Validation Error" ||
    error.error === "validation_error"
  ) {
    // Create a structured error object with field-specific messages
    const fieldErrors: Record<string, string> = {};

    if (Array.isArray(error.messages)) {
      for (const item of error.messages) {
        // Convert snake_case field names to camelCase for frontend compatibility
        const fieldName = item.field.replace(
          /_([a-z])/g,
          (_: string, letter: string) => letter.toUpperCase(),
        );
        fieldErrors[fieldName] = item.message;
      }
    }

    // Throw a response with validation errors that can be handled by the form
    throw new Response(
      JSON.stringify({
        error: "validation_error",
        fieldErrors,
      }),
      {
        status: 422,
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
  }

  // If it's not a validation error, re-throw the original error
  throw new Response(
    JSON.stringify({
      error: error.error || "unknown_error",
      message: error.message || "An unexpected error occurred",
    }),
    {
      status: 400,
      headers: {
        "Content-Type": "application/json",
      },
    },
  );
}

/**
 * Find company by shop - Enhanced with better error handling
 */
export async function findBusinessData({
  request,
  shop,
}: {
  request: Request;
  shop: string;
}) {
  try {
    const client = await getAuthenticatedClient(request, shop);
    return await client.makeRequest('GET', '/api/v1/shopify/companies');
  } catch (error: any) {
    console.error(`Failed to find business data for shop ${shop}:`, error);
    handleBusinessDataError(error);
  }
}

/**
 * Send business data to the API - Enhanced with better error handling and validation
 */
export async function sendBusinessData({
  request,
  shop,
  businessData,
}: {
  request: Request;
  shop: string;
  businessData: BusinessFormData;
}) {
  try {
    // Validate required fields
    if (!businessData.name || !businessData.tinCode || !businessData.registrationNumber) {
      throw new Error("Missing required business data fields");
    }

    const client = await getAuthenticatedClient(request, shop);

    const result = await client.makeRequest('POST', '/api/v1/shopify/companies', {
      // Map form fields to API expected format
      name: businessData.name,
      tin_code: businessData.tinCode,
      registration_number: businessData.registrationNumber,
      registration_type: businessData.registrationType,
      sst_registration_number: businessData.sstRegistrationNumber || "",
      tourism_tax_registration_number:
        businessData.tourismTaxRegistrationNumber || "",
      business_activity_description:
        businessData.businessActivityDescription || "",
      msic_code: businessData.msicCode,
      country: businessData.country,
      state: businessData.state,
      zip_code: businessData.zipCode,
      city: businessData.city,
      address: businessData.address,
      phone: businessData.phone,
      email: businessData.email,
      // API credentials
      client_id: businessData.clientId,
      client_secret: businessData.clientSecret,
    });

    console.log(`Successfully sent business data for shop ${shop}`);
    return result;
  } catch (error: any) {
    console.error(`Failed to send business data for shop ${shop}:`, error);
    handleBusinessDataError(error);
  }
}

/**
 * Factory function to create MerchantOnboardingService
 */
export function createMerchantOnboardingService(
  shop: string,
  config?: Partial<OnboardingConfig>
): MerchantOnboardingService {
  return new MerchantOnboardingService(shop, config);
}

/**
 * Utility function for quick onboarding
 */
export async function onboardMerchantQuick(
  request: Request,
  context: OnboardingContext,
  config?: Partial<OnboardingConfig>
): Promise<OnboardingResult> {
  const service = createMerchantOnboardingService(context.shop, config);
  return service.handleOnboardingWithRetry(request, context);
}

/**
 * Utility function to check if merchant needs onboarding
 */
export async function checkOnboardingRequired(shop: string): Promise<boolean> {
  const service = createMerchantOnboardingService(shop);
  const isOnboarded = await service.isMerchantOnboarded(shop);
  return !isOnboarded;
}