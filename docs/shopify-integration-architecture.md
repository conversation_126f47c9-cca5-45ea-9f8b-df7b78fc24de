# Shopify Integration Architecture

## Domain Separation Strategy

### Core SaaS Domain (Main Database)
- **Purpose**: Core invoice management system
- **Database**: Main PostgreSQL database
- **Models**: User, Organization, Member, Invoice, Subscription
- **Responsibility**: Core business logic

### Shopify Plugin Domain (Shopify Database)  
- **Purpose**: Shopify-specific functionality
- **Database**: Shopify PostgreSQL database
- **Models**: ShopifySession, StoreInfo, SyncOrders, SyncOperations
- **Responsibility**: Shopify integration logic

## Integration Pattern

### 1. Simplified MerchantSyncState (Main DB)
```prisma
model MerchantSyncState {
  id                    String              @id @default(uuid())
  shopId                String              @unique @map("shop_id")
  backendUserId         String?             @map("backend_user_id")
  backendOrganizationId String?             @map("backend_organization_id")
  syncStatus            MerchantSyncStatus  @default(PENDING)
  lastSyncAt            DateTime?           @map("last_sync_at")
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  // Relations to core domain
  backendUser         User?         @relation(fields: [backendUserId], references: [id])
  backendOrganization Organization? @relation(fields: [backendOrganizationId], references: [id])

  @@map("merchant_sync_state")
}
```

### 2. Integration Service Layer
```typescript
// apps/api/app/lib/shopify-integration-service.ts

interface ShopifyIntegrationService {
  // Called by Shopify plugin when merchant installs
  createMerchantAccount(shopifyData: ShopifyMerchantData): Promise<MerchantAccount>;
  
  // Called by Shopify plugin for ongoing sync
  syncMerchantData(shopId: string, updates: MerchantUpdates): Promise<void>;
  
  // Called by core system to get Shopify data
  getMerchantShopifyData(userId: string): Promise<ShopifyData | null>;
}
```

### 3. Event-Driven Communication
```typescript
// Shopify Plugin -> Core SaaS
interface ShopifyEvents {
  'merchant.installed': { shopId: string; merchantData: MerchantData };
  'merchant.updated': { shopId: string; updates: MerchantUpdates };
  'orders.synced': { shopId: string; orderCount: number };
  'sync.failed': { shopId: string; error: string };
}

// Core SaaS -> Shopify Plugin  
interface CoreEvents {
  'user.updated': { userId: string; userData: UserData };
  'organization.updated': { orgId: string; orgData: OrgData };
  'subscription.changed': { userId: string; plan: string };
}
```

## Implementation Strategy

### Phase 1: Clean Up Current Integration
1. **Simplify webhook** to just handle merchant account creation
2. **Remove complex sync logic** from webhook
3. **Create integration service** for cross-domain operations

### Phase 2: Event-Driven Architecture
1. **Add event bus** (Redis/RabbitMQ/AWS EventBridge)
2. **Implement event handlers** in both domains
3. **Replace direct database calls** with events

### Phase 3: API Gateway Pattern
1. **Create integration API** for cross-domain queries
2. **Implement rate limiting** and authentication
3. **Add monitoring** and observability

## Benefits of This Approach

### Domain Separation
- ✅ **Clear boundaries** between core SaaS and Shopify functionality
- ✅ **Independent deployment** of Shopify features
- ✅ **Team autonomy** - different teams can own different domains
- ✅ **Technology flexibility** - can use different tech stacks per domain

### Scalability
- ✅ **Independent scaling** - scale Shopify sync separately from core SaaS
- ✅ **Database optimization** - optimize each DB for its specific use case
- ✅ **Caching strategies** - different caching per domain

### Maintainability
- ✅ **Reduced complexity** in core SaaS system
- ✅ **Plugin-like architecture** - can add/remove integrations
- ✅ **Clear integration contracts** via events and APIs
- ✅ **Easier testing** - test domains independently

## Trade-offs

### Complexity
- ❌ **Network calls** between domains (vs single DB transactions)
- ❌ **Eventual consistency** instead of immediate consistency
- ❌ **More infrastructure** to manage (event bus, multiple DBs)

### Development
- ❌ **Cross-domain changes** require coordination
- ❌ **Integration testing** becomes more complex
- ❌ **Data consistency** requires careful design

## Recommended Next Steps

1. **Keep current separate databases** ✅
2. **Simplify the webhook** we just built to focus only on account creation
3. **Create integration service layer** for clean cross-domain communication
4. **Add event bus** for async communication between domains
5. **Implement monitoring** for cross-domain operations

This approach gives you the best of both worlds:
- **Clean domain separation** for long-term maintainability
- **Plugin-like architecture** for Shopify integration
- **Flexibility** to add more integrations (WooCommerce, BigCommerce, etc.)
- **Team autonomy** for different domains
