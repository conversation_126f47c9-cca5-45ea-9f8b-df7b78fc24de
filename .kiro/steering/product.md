# Product Overview

This is a production-grade Next.js application template called "next-forge" that serves as a comprehensive starting point for modern web applications. The current implementation appears to be customized for an invoice/business management system called "my-invoice".

## Key Features
- Multi-tenant organization management with user roles (super-admin, admin, customer)
- Authentication and authorization system with API key support
- Payment processing integration (Stripe)
- File upload and storage capabilities
- Internationalization support (multiple languages)
- Email notifications and webhooks
- Analytics and observability
- Feature flags system

## Applications
- **Main App**: Primary user-facing application
- **API**: Backend API services
- **Portal**: Organization/business portal
- **Web**: Marketing/landing pages
- **Docs**: Documentation site

The system is designed for B2B use cases with organizations, members, invitations, and role-based access control.