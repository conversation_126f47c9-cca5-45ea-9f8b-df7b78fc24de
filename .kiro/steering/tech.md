# Technology Stack

## Build System & Package Management
- **Monorepo**: Turborepo for build orchestration and caching
- **Package Manager**: pnpm with workspaces
- **Build Tool**: Next.js with Turbopack for development

## Core Technologies
- **Framework**: Next.js 15.3.2 with App Router
- **Runtime**: Node.js >=18
- **Language**: TypeScript 5.8+ with strict mode
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Better Auth
- **Payments**: Stripe integration
- **Storage**: Cloud storage integration
- **Styling**: Tailwind CSS 4.1+
- **UI Components**: Radix UI primitives with shadcn/ui
- **State Management**: Zustand
- **Data Fetching**: SWR
- **Animations**: Motion (Framer Motion)
- **Icons**: Lucide React

## Development Tools
- **Linting**: Biome (extends ultracite config)
- **Testing**: Vitest with React Testing Library
- **Type Checking**: TypeScript strict mode
- **Code Quality**: Ultracite for formatting and linting
- **Observability**: Sentry for error tracking
- **Analytics**: PostHog and Google Analytics

## Common Commands

### Development
```bash
# Start all apps in development
pnpm dev

# Start specific app
pnpm portal          # Portal app only
pnpm fumadocs       # Documentation site

# Build all packages
pnpm build

# Run tests
pnpm test
```

### Database Operations
```bash
# Generate Prisma client and run migrations
pnpm migrate

# Reset database
pnpm migrate-reset

# Production migration
pnpm migrate-prod

# Seed database
pnpm seed

# Generate auth schema
pnpm better-auth
```

### Code Quality
```bash
# Lint code
pnpm lint

# Format code
pnpm format

# Type check
pnpm typecheck

# Analyze bundle
pnpm analyze
```

### Maintenance
```bash
# Update dependencies
pnpm bump-deps

# Update UI components
pnpm bump-ui

# Clean all node_modules
pnpm clean

# Translate content
pnpm translate
```

## Environment Requirements
- Node.js 18+
- PostgreSQL database
- Environment variables for database, auth, payments, and external services