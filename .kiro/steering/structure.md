# Project Structure

## Monorepo Organization

This is a Turborepo monorepo with clear separation between applications and shared packages.

```
├── apps/                    # Applications
│   ├── api/                # Backend API (Next.js API routes)
│   ├── app/                # Main user application
│   ├── docs/               # Documentation site (Fumadocs)
│   ├── portal/             # Business/organization portal
│   └── web/                # Marketing/landing pages
├── packages/               # Shared packages
│   ├── auth/               # Authentication system
│   ├── database/           # Prisma schema and client
│   ├── design-system/      # UI components and styling
│   ├── analytics/          # Analytics integrations
│   ├── payments/           # Payment processing
│   ├── storage/            # File storage utilities
│   ├── internationalization/ # i18n support
│   └── [other packages]/   # Additional shared utilities
└── turbo/                  # Turborepo configuration
```

## Application Structure

Each Next.js app follows the App Router pattern:

```
apps/[app-name]/
├── app/                    # Next.js App Router
│   ├── (authenticated)/    # Route groups for auth states
│   ├── (unauthenticated)/
│   ├── api/               # API routes
│   ├── components/        # App-specific components
│   ├── lib/               # App-specific utilities
│   └── layout.tsx         # Root layout
├── components/            # Shared app components
├── lib/                   # App utilities
├── middleware.ts          # Next.js middleware
├── package.json
└── tsconfig.json
```

## Package Structure

Shared packages follow a consistent structure:

```
packages/[package-name]/
├── components/            # React components (if applicable)
├── lib/                   # Core utilities
├── hooks/                 # React hooks (if applicable)
├── types.ts              # TypeScript types
├── index.ts              # Main export
├── keys.ts               # Environment variables/config
├── package.json
└── tsconfig.json
```

## Key Conventions

### File Naming
- Use kebab-case for files and folders
- Component files use PascalCase (e.g., `UserButton.tsx`)
- Utility files use camelCase (e.g., `authHelpers.ts`)
- Route files follow Next.js conventions (`page.tsx`, `layout.tsx`, `route.ts`)

### Import Structure
- Workspace packages use `@repo/package-name` imports
- Relative imports for local files
- External dependencies imported normally

### Environment Variables
- Each package with env vars has a `keys.ts` file for validation
- Use `@t3-oss/env-nextjs` for type-safe environment variables
- `.env.example` files document required variables

### Database
- Single Prisma schema in `packages/database/prisma/schema.prisma`
- Generated client shared across all apps
- Migrations managed centrally

### Authentication
- Better Auth configuration in `packages/auth/server.ts`
- Auth components and utilities in `packages/auth/`
- Route protection via middleware

### Styling
- Tailwind CSS configuration in design-system package
- Global styles in `packages/design-system/styles/globals.css`
- Component-specific styles co-located with components

### Testing
- Vitest for unit/integration tests
- Test files alongside source files with `.test.ts` extension
- Shared testing utilities in `packages/testing/`