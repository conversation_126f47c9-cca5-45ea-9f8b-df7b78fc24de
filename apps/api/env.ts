import { keys as analytics } from '@repo/analytics/keys';
import { keys as auth } from '@repo/auth/keys';
import { keys as database } from '@repo/database/keys';
import { keys as email } from '@repo/email/keys';
import { keys as core } from '@repo/next-config/keys';
import { keys as observability } from '@repo/observability/keys';
import { keys as payments } from '@repo/payments/keys';
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  extends: [
    auth(),
    analytics(),
    core(),
    database(),
    email(),
    observability(),
    payments(),
  ],
  server: {
    CORE_BASE_URL: z.string().min(1).url(),
    CORE_INCOMING_API_KEY: z.string().min(1),
    CORE_OUTGOING_API_KEY: z.string().min(1),
    SHOPIFY_WEBHOOK_SECRET: z.string().min(1).optional(),
  },
  client: {},
  runtimeEnv: {
    CORE_BASE_URL: process.env.CORE_BASE_URL,
    CORE_INCOMING_API_KEY: process.env.CORE_INCOMING_API_KEY,
    CORE_OUTGOING_API_KEY: process.env.CORE_OUTGOING_API_KEY,
    SHOPIFY_WEBHOOK_SECRET: process.env.SHOPIFY_WEBHOOK_SECRET,
  },
});
