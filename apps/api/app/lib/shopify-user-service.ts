import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import { parseError } from '@repo/observability/error';

/**
 * Service for managing Shopify user creation and updates
 */

export interface ShopifyMerchantData {
  shop: string;
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  phone?: string | null;
  name?: string;
}

export interface UserCreationResult {
  success: boolean;
  userId?: string;
  user?: {
    id: string;
    email: string;
    name: string;
    firstName?: string | null;
    lastName?: string | null;
    phone?: string | null;
  };
  errors?: string[];
  warnings?: string[];
  isExisting?: boolean;
}

/**
 * Generate a unique email for users without email addresses
 * @param shop - Shop domain
 * @param name - User name or firstName
 * @returns Generated email address
 */
function generateEmailForShopifyUser(shop: string, name?: string): string {
  const cleanShop = shop.replace('.myshopify.com', '');
  const cleanName = name?.toLowerCase().replace(/[^a-z0-9]/g, '') || 'merchant';
  return `${cleanName}+${cleanShop}@shopify-merchant.local`;
}

/**
 * Prepare user data for creation from Shopify merchant data
 * @param merchantData - Shopify merchant data
 * @returns Prepared user data
 */
function prepareUserData(merchantData: ShopifyMerchantData) {
  // Determine the name to use
  let name = merchantData.name;
  if (!name && (merchantData.firstName || merchantData.lastName)) {
    name = [merchantData.firstName, merchantData.lastName]
      .filter(Boolean)
      .join(' ');
  }
  if (!name) {
    name = `Merchant (${merchantData.shop})`;
  }

  // Determine email to use
  let email = merchantData.email;
  if (!email) {
    email = generateEmailForShopifyUser(merchantData.shop, name);
  }

  return {
    name,
    email,
    emailVerified: false, // Shopify merchants need to verify their email
    firstName: merchantData.firstName,
    lastName: merchantData.lastName,
    phone: merchantData.phone,
    role: 'customer', // Default role for Shopify merchants
  };
}

/**
 * Check if a user already exists for the given shop
 * @param shop - Shop domain
 * @returns Existing user or null
 */
export async function findExistingUserForShop(shop: string) {
  try {
    // First, check if there's a merchant sync state for this shop
    const syncState = await database.merchantSyncState.findUnique({
      where: { shopId: shop },
      include: {
        backendUser: {
          select: {
            id: true,
            email: true,
            name: true,
            firstName: true,
            lastName: true,
            phone: true,
          },
        },
      },
    });

    if (syncState?.backendUser) {
      return syncState.backendUser;
    }

    return null;
  } catch (error) {
    log.warn('Error finding existing user for shop:', { shop, error });
    return null;
  }
}

/**
 * Check if a user exists by email
 * @param email - Email address
 * @returns Existing user or null
 */
export async function findUserByEmail(email: string) {
  try {
    return await database.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        firstName: true,
        lastName: true,
        phone: true,
      },
    });
  } catch (error) {
    log.warn('Error finding user by email:', { email, error });
    return null;
  }
}

/**
 * Create a new user from Shopify merchant data
 * @param merchantData - Shopify merchant data
 * @param correlationId - Optional correlation ID for logging
 * @returns User creation result
 */
export async function createUserFromShopifyData(
  merchantData: ShopifyMerchantData,
  correlationId?: string
): Promise<UserCreationResult> {
  try {
    log.info('Creating user from Shopify data', {
      shop: merchantData.shop,
      correlationId,
    });

    // Check if user already exists for this shop
    const existingUser = await findExistingUserForShop(merchantData.shop);
    if (existingUser) {
      log.info('User already exists for shop', {
        shop: merchantData.shop,
        userId: existingUser.id,
        correlationId,
      });

      return {
        success: true,
        userId: existingUser.id,
        user: existingUser,
        warnings: ['User already exists for this shop'],
        isExisting: true,
      };
    }

    // Prepare user data
    const userData = prepareUserData(merchantData);

    // Check if a user with this email already exists
    if (merchantData.email) {
      const existingEmailUser = await findUserByEmail(merchantData.email);
      if (existingEmailUser) {
        log.info('User with email already exists', {
          email: merchantData.email,
          userId: existingEmailUser.id,
          correlationId,
        });

        return {
          success: true,
          userId: existingEmailUser.id,
          user: existingEmailUser,
          warnings: ['User with this email already exists'],
          isExisting: true,
        };
      }
    }

    // Create the user
    const user = await database.user.create({
      data: userData,
      select: {
        id: true,
        email: true,
        name: true,
        firstName: true,
        lastName: true,
        phone: true,
      },
    });

    log.info('User created successfully', {
      userId: user.id,
      shop: merchantData.shop,
      correlationId,
    });

    return {
      success: true,
      userId: user.id,
      user,
      warnings: ['User created successfully'],
      isExisting: false,
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error creating user from Shopify data', {
      shop: merchantData.shop,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}

/**
 * Update an existing user with Shopify merchant data
 * @param userId - User ID to update
 * @param merchantData - Shopify merchant data
 * @param correlationId - Optional correlation ID for logging
 * @returns User update result
 */
export async function updateUserFromShopifyData(
  userId: string,
  merchantData: ShopifyMerchantData,
  correlationId?: string
): Promise<UserCreationResult> {
  try {
    log.info('Updating user from Shopify data', {
      userId,
      shop: merchantData.shop,
      correlationId,
    });

    // Prepare update data (only update non-null values)
    const updateData: any = {};
    
    if (merchantData.firstName !== undefined) {
      updateData.firstName = merchantData.firstName;
    }
    if (merchantData.lastName !== undefined) {
      updateData.lastName = merchantData.lastName;
    }
    if (merchantData.phone !== undefined) {
      updateData.phone = merchantData.phone;
    }
    
    // Update name if we have first/last name
    if (merchantData.firstName || merchantData.lastName) {
      const name = [merchantData.firstName, merchantData.lastName]
        .filter(Boolean)
        .join(' ');
      if (name) {
        updateData.name = name;
      }
    } else if (merchantData.name) {
      updateData.name = merchantData.name;
    }

    // Only update if we have data to update
    if (Object.keys(updateData).length === 0) {
      log.info('No data to update for user', {
        userId,
        shop: merchantData.shop,
        correlationId,
      });

      const user = await database.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          firstName: true,
          lastName: true,
          phone: true,
        },
      });

      return {
        success: true,
        userId,
        user: user || undefined,
        warnings: ['No updates needed'],
        isExisting: true,
      };
    }

    // Update the user
    const user = await database.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        email: true,
        name: true,
        firstName: true,
        lastName: true,
        phone: true,
      },
    });

    log.info('User updated successfully', {
      userId: user.id,
      shop: merchantData.shop,
      correlationId,
    });

    return {
      success: true,
      userId: user.id,
      user,
      warnings: ['User updated successfully'],
      isExisting: true,
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error updating user from Shopify data', {
      userId,
      shop: merchantData.shop,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}
