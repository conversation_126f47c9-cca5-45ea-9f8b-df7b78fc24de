import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import { parseError } from '@repo/observability/error';

/**
 * Shopify Integration Service
 * 
 * This service provides a clean integration layer between the Shopify plugin domain
 * and the core SaaS domain. It handles cross-domain operations while respecting
 * domain boundaries.
 */

export interface ShopifyMerchantData {
  shop: string;
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  phone?: string | null;
  name?: string;
}

export interface ShopifyStoreData {
  shop: string;
  name?: string;
  currency?: string;
  timezone?: string;
  planName?: string;
  country?: string;
  registrationNumber?: string | null;
  tinCode?: string | null;
}

export interface MerchantAccountResult {
  success: boolean;
  userId?: string;
  organizationId?: string;
  memberId?: string;
  syncStateId?: string;
  errors?: string[];
  warnings?: string[];
  isExisting?: boolean;
}

/**
 * Create merchant account in core SaaS system
 * This is the main integration point between Shopify plugin and core SaaS
 */
export async function createMerchantAccount(
  shopId: string,
  accessToken: string,
  merchantData: ShopifyMerchantData,
  storeData: ShopifyStoreData,
  correlationId?: string
): Promise<MerchantAccountResult> {
  try {
    log.info('Creating merchant account for Shopify integration', {
      shopId,
      correlationId,
    });

    // Check if merchant already exists
    const existingSyncState = await database.merchantSyncState.findUnique({
      where: { shopId },
      include: {
        backendUser: {
          select: { id: true, email: true, name: true },
        },
        backendOrganization: {
          select: { id: true, name: true, slug: true },
        },
      },
    });

    if (existingSyncState?.backendUser && existingSyncState?.backendOrganization) {
      log.info('Merchant account already exists', {
        shopId,
        userId: existingSyncState.backendUserId,
        organizationId: existingSyncState.backendOrganizationId,
        correlationId,
      });

      return {
        success: true,
        userId: existingSyncState.backendUserId!,
        organizationId: existingSyncState.backendOrganizationId!,
        syncStateId: existingSyncState.id,
        warnings: ['Merchant account already exists'],
        isExisting: true,
      };
    }

    // Create merchant account in a transaction
    const result = await database.$transaction(async (tx) => {
      // 1. Create or find user
      const userEmail = merchantData.email || `merchant+${shopId.replace('.myshopify.com', '')}@shopify-integration.local`;
      const userName = merchantData.name || [merchantData.firstName, merchantData.lastName].filter(Boolean).join(' ') || `Merchant (${shopId})`;

      let user = await tx.user.findUnique({
        where: { email: userEmail },
        select: { id: true, email: true, name: true },
      });

      if (!user) {
        user = await tx.user.create({
          data: {
            email: userEmail,
            name: userName,
            firstName: merchantData.firstName,
            lastName: merchantData.lastName,
            phone: merchantData.phone,
            role: 'customer',
            emailVerified: false,
          },
          select: { id: true, email: true, name: true },
        });
      }

      // 2. Create organization
      const orgName = storeData.name || shopId;
      const orgSlug = generateOrganizationSlug(orgName, shopId);

      // Ensure unique slug
      let uniqueSlug = orgSlug;
      let counter = 1;
      while (await tx.organization.findUnique({ where: { slug: uniqueSlug } })) {
        uniqueSlug = `${orgSlug}-${counter}`;
        counter++;
      }

      const organization = await tx.organization.create({
        data: {
          name: orgName,
          slug: uniqueSlug,
          metadata: JSON.stringify({
            shopifyShop: shopId,
            currency: storeData.currency,
            timezone: storeData.timezone,
            planName: storeData.planName,
            country: storeData.country,
            registrationNumber: storeData.registrationNumber,
            tinCode: storeData.tinCode,
            source: 'shopify',
            createdAt: new Date().toISOString(),
          }),
        },
        select: { id: true, name: true, slug: true },
      });

      // 3. Create member relationship
      const member = await tx.member.create({
        data: {
          userId: user.id,
          organizationId: organization.id,
          role: 'owner', // Shopify merchants are owners of their organizations
        },
        select: { id: true, userId: true, organizationId: true, role: true },
      });

      // 4. Create or update sync state
      const syncState = await tx.merchantSyncState.upsert({
        where: { shopId },
        update: {
          accessToken,
          backendUserId: user.id,
          backendOrganizationId: organization.id,
          syncStatus: 'UP_TO_DATE',
          syncErrors: null,
          lastSyncAt: new Date(),
        },
        create: {
          shopId,
          accessToken,
          backendUserId: user.id,
          backendOrganizationId: organization.id,
          syncStatus: 'UP_TO_DATE',
          lastSyncAt: new Date(),
        },
        select: { id: true, shopId: true, syncStatus: true },
      });

      return {
        user,
        organization,
        member,
        syncState,
      };
    });

    log.info('Merchant account created successfully', {
      shopId,
      userId: result.user.id,
      organizationId: result.organization.id,
      correlationId,
    });

    return {
      success: true,
      userId: result.user.id,
      organizationId: result.organization.id,
      memberId: result.member.id,
      syncStateId: result.syncState.id,
      warnings: ['Merchant account created successfully'],
      isExisting: false,
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error creating merchant account', {
      shopId,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}

/**
 * Get merchant account information
 */
export async function getMerchantAccount(shopId: string): Promise<{
  success: boolean;
  data?: {
    userId: string;
    organizationId: string;
    user: { id: string; email: string; name: string };
    organization: { id: string; name: string; slug: string };
    syncStatus: string;
    lastSyncAt: Date | null;
  };
  error?: string;
}> {
  try {
    const syncState = await database.merchantSyncState.findUnique({
      where: { shopId },
      include: {
        backendUser: {
          select: { id: true, email: true, name: true },
        },
        backendOrganization: {
          select: { id: true, name: true, slug: true },
        },
      },
    });

    if (!syncState?.backendUser || !syncState?.backendOrganization) {
      return {
        success: false,
        error: 'Merchant account not found',
      };
    }

    return {
      success: true,
      data: {
        userId: syncState.backendUserId!,
        organizationId: syncState.backendOrganizationId!,
        user: syncState.backendUser,
        organization: syncState.backendOrganization,
        syncStatus: syncState.syncStatus,
        lastSyncAt: syncState.lastSyncAt,
      },
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error getting merchant account', { shopId, error: message });
    
    return {
      success: false,
      error: message,
    };
  }
}

/**
 * Update merchant sync status
 */
export async function updateMerchantSyncStatus(
  shopId: string,
  status: 'UP_TO_DATE' | 'PENDING' | 'FAILED',
  errors?: string[]
): Promise<{ success: boolean; error?: string }> {
  try {
    await database.merchantSyncState.update({
      where: { shopId },
      data: {
        syncStatus: status,
        syncErrors: errors ? JSON.stringify(errors) : null,
        lastSyncAt: status === 'UP_TO_DATE' ? new Date() : undefined,
      },
    });

    return { success: true };
  } catch (error) {
    const message = parseError(error);
    log.error('Error updating merchant sync status', { shopId, error: message });
    
    return {
      success: false,
      error: message,
    };
  }
}

/**
 * Generate organization slug from name and shop
 */
function generateOrganizationSlug(name: string, shop: string): string {
  const cleanName = name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();

  const cleanShop = shop
    .replace('.myshopify.com', '')
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '');

  return `${cleanName}-${cleanShop}`.substring(0, 50);
}
