import { NextResponse } from 'next/server';
import { log } from '@repo/observability/log';

/**
 * Standardized response utilities for Shopify webhooks
 * Provides consistent response formats that the Shopify Remix app can handle
 */

export interface WebhookSuccessData {
  userId?: string;
  organizationId?: string;
  memberId?: string;
  syncStateId?: string;
  isExisting?: boolean;
}

export interface WebhookResponseMetadata {
  correlationId: string;
  timestamp: string;
  duration?: number;
  shop?: string;
  operation?: string;
}

export interface WebhookSuccessResponse {
  success: true;
  data: WebhookSuccessData;
  warnings?: string[];
  metadata: WebhookResponseMetadata;
}

export interface WebhookErrorResponse {
  success: false;
  error: string;
  details?: string[] | any;
  type?: string;
  retryable?: boolean;
  metadata: WebhookResponseMetadata;
}

/**
 * Create a success response for webhook processing
 */
export function createSuccessResponse(
  data: WebhookSuccessData,
  metadata: WebhookResponseMetadata,
  warnings?: string[]
): NextResponse<WebhookSuccessResponse> {
  const response: WebhookSuccessResponse = {
    success: true,
    data,
    metadata: {
      ...metadata,
      timestamp: new Date().toISOString(),
    },
  };

  if (warnings && warnings.length > 0) {
    response.warnings = warnings;
  }

  // Log successful processing
  log.info('Shopify webhook processed successfully', {
    correlationId: metadata.correlationId,
    shop: metadata.shop,
    operation: metadata.operation,
    duration: metadata.duration,
    userId: data.userId,
    organizationId: data.organizationId,
    isExisting: data.isExisting,
    warningCount: warnings?.length || 0,
  });

  return NextResponse.json(response, { status: 200 });
}

/**
 * Create an error response for webhook processing
 */
export function createErrorResponse(
  error: string,
  metadata: WebhookResponseMetadata,
  options?: {
    details?: string[] | any;
    type?: string;
    retryable?: boolean;
    status?: number;
  }
): NextResponse<WebhookErrorResponse> {
  const response: WebhookErrorResponse = {
    success: false,
    error,
    metadata: {
      ...metadata,
      timestamp: new Date().toISOString(),
    },
  };

  if (options?.details) {
    response.details = options.details;
  }

  if (options?.type) {
    response.type = options.type;
  }

  if (options?.retryable !== undefined) {
    response.retryable = options.retryable;
  }

  const status = options?.status || 500;

  // Log error response
  log.error('Shopify webhook processing failed', {
    correlationId: metadata.correlationId,
    shop: metadata.shop,
    operation: metadata.operation,
    duration: metadata.duration,
    error,
    type: options?.type,
    retryable: options?.retryable,
    status,
    details: options?.details,
  });

  return NextResponse.json(response, { status });
}

/**
 * Create a validation error response
 */
export function createValidationErrorResponse(
  validationErrors: string[],
  metadata: WebhookResponseMetadata
): NextResponse<WebhookErrorResponse> {
  return createErrorResponse('Payload validation failed', metadata, {
    details: validationErrors,
    type: 'VALIDATION_ERROR',
    retryable: false,
    status: 400,
  });
}

/**
 * Create an authentication error response
 */
export function createAuthenticationErrorResponse(
  message: string,
  metadata: WebhookResponseMetadata
): NextResponse<WebhookErrorResponse> {
  return createErrorResponse(message, metadata, {
    type: 'AUTHENTICATION_ERROR',
    retryable: false,
    status: 401,
  });
}

/**
 * Create a processing error response
 */
export function createProcessingErrorResponse(
  error: string,
  metadata: WebhookResponseMetadata,
  details?: any,
  retryable = true
): NextResponse<WebhookErrorResponse> {
  return createErrorResponse(error, metadata, {
    details,
    type: 'PROCESSING_ERROR',
    retryable,
    status: 500,
  });
}

/**
 * Create a timeout error response
 */
export function createTimeoutErrorResponse(
  operation: string,
  metadata: WebhookResponseMetadata
): NextResponse<WebhookErrorResponse> {
  return createErrorResponse(`Operation timed out: ${operation}`, metadata, {
    type: 'TIMEOUT_ERROR',
    retryable: true,
    status: 504,
  });
}

/**
 * Create a rate limit error response
 */
export function createRateLimitErrorResponse(
  metadata: WebhookResponseMetadata,
  retryAfter?: number
): NextResponse<WebhookErrorResponse> {
  const response = createErrorResponse('Rate limit exceeded', metadata, {
    type: 'RATE_LIMIT_ERROR',
    retryable: true,
    status: 429,
    details: retryAfter ? { retryAfter } : undefined,
  });

  // Add Retry-After header if provided
  if (retryAfter) {
    response.headers.set('Retry-After', retryAfter.toString());
  }

  return response;
}

/**
 * Create a maintenance mode response
 */
export function createMaintenanceResponse(
  metadata: WebhookResponseMetadata,
  estimatedDuration?: string
): NextResponse<WebhookErrorResponse> {
  return createErrorResponse(
    'Service temporarily unavailable for maintenance',
    metadata,
    {
      type: 'MAINTENANCE_ERROR',
      retryable: true,
      status: 503,
      details: estimatedDuration ? { estimatedDuration } : undefined,
    }
  );
}

/**
 * Create response metadata
 */
export function createResponseMetadata(
  correlationId: string,
  options?: {
    shop?: string;
    operation?: string;
    startTime?: number;
  }
): WebhookResponseMetadata {
  const metadata: WebhookResponseMetadata = {
    correlationId,
    timestamp: new Date().toISOString(),
  };

  if (options?.shop) {
    metadata.shop = options.shop;
  }

  if (options?.operation) {
    metadata.operation = options.operation;
  }

  if (options?.startTime) {
    metadata.duration = Date.now() - options.startTime;
  }

  return metadata;
}

/**
 * Response builder class for fluent API
 */
export class WebhookResponseBuilder {
  private correlationId: string;
  private shop?: string;
  private operation?: string;
  private startTime?: number;

  constructor(correlationId: string) {
    this.correlationId = correlationId;
  }

  withShop(shop: string): this {
    this.shop = shop;
    return this;
  }

  withOperation(operation: string): this {
    this.operation = operation;
    return this;
  }

  withStartTime(startTime: number): this {
    this.startTime = startTime;
    return this;
  }

  success(
    data: WebhookSuccessData,
    warnings?: string[]
  ): NextResponse<WebhookSuccessResponse> {
    const metadata = createResponseMetadata(this.correlationId, {
      shop: this.shop,
      operation: this.operation,
      startTime: this.startTime,
    });

    return createSuccessResponse(data, metadata, warnings);
  }

  error(
    error: string,
    options?: {
      details?: string[] | any;
      type?: string;
      retryable?: boolean;
      status?: number;
    }
  ): NextResponse<WebhookErrorResponse> {
    const metadata = createResponseMetadata(this.correlationId, {
      shop: this.shop,
      operation: this.operation,
      startTime: this.startTime,
    });

    return createErrorResponse(error, metadata, options);
  }

  validationError(
    validationErrors: string[]
  ): NextResponse<WebhookErrorResponse> {
    const metadata = createResponseMetadata(this.correlationId, {
      shop: this.shop,
      operation: this.operation,
      startTime: this.startTime,
    });

    return createValidationErrorResponse(validationErrors, metadata);
  }

  authenticationError(message: string): NextResponse<WebhookErrorResponse> {
    const metadata = createResponseMetadata(this.correlationId, {
      shop: this.shop,
      operation: this.operation,
      startTime: this.startTime,
    });

    return createAuthenticationErrorResponse(message, metadata);
  }

  processingError(
    error: string,
    details?: any,
    retryable = true
  ): NextResponse<WebhookErrorResponse> {
    const metadata = createResponseMetadata(this.correlationId, {
      shop: this.shop,
      operation: this.operation,
      startTime: this.startTime,
    });

    return createProcessingErrorResponse(error, metadata, details, retryable);
  }
}
