import { z } from 'zod';

/**
 * Validation schemas for Shopify webhook payloads
 * Based on the OnboardingContext interface from example-onboarding.ts
 */

/**
 * Schema for Shopify session data
 */
export const ShopifySessionSchema = z.object({
  shop: z.string().min(1, 'Shop domain is required'),
  firstName: z.string().nullable().optional(),
  lastName: z.string().nullable().optional(),
  email: z.string().email().nullable().optional(),
  userId: z.union([z.string(), z.number(), z.bigint()]).nullable().optional(),
});

/**
 * Schema for store information (business details)
 */
export const StoreInfoSchema = z.object({
  name: z.string().nullable().optional(),
  phone: z.string().nullable().optional(),
  email: z.string().email().nullable().optional(),
  registrationNumber: z.string().nullable().optional(),
  tinCode: z.string().nullable().optional(),
});

/**
 * Schema for shop data (Shopify store details)
 */
export const ShopDataSchema = z.object({
  name: z.string().optional(),
  currency: z.string().optional(),
  timezone: z.string().optional(),
  planName: z.string().optional(),
  country: z.string().optional(),
});

/**
 * Main schema for Shopify install webhook payload
 * This matches the OnboardingContext interface from the example
 */
export const ShopifyInstallWebhookSchema = z.object({
  shop: z.string().min(1, 'Shop domain is required'),
  accessToken: z.string().min(1, 'Access token is required'),
  session: ShopifySessionSchema,
  storeInfo: StoreInfoSchema.optional(),
  shopData: ShopDataSchema.optional(),
});

/**
 * Type definitions derived from schemas
 */
export type ShopifySession = z.infer<typeof ShopifySessionSchema>;
export type StoreInfo = z.infer<typeof StoreInfoSchema>;
export type ShopData = z.infer<typeof ShopDataSchema>;
export type ShopifyInstallWebhookPayload = z.infer<
  typeof ShopifyInstallWebhookSchema
>;

/**
 * Validation result interface
 */
export interface ValidationResult<T> {
  success: boolean;
  data?: T;
  errors?: string[];
}

/**
 * Validate Shopify install webhook payload
 * @param payload - Raw payload object
 * @returns Validation result with parsed data or errors
 */
export function validateShopifyInstallPayload(
  payload: unknown
): ValidationResult<ShopifyInstallWebhookPayload> {
  try {
    const result = ShopifyInstallWebhookSchema.safeParse(payload);

    if (result.success) {
      return {
        success: true,
        data: result.data,
      };
    }

    const errors = result.error.errors.map(
      (err) => `${err.path.join('.')}: ${err.message}`
    );

    return {
      success: false,
      errors,
    };
  } catch (_error) {
    return {
      success: false,
      errors: ['Invalid payload format'],
    };
  }
}

/**
 * Extract and validate merchant data for user creation
 * @param session - Shopify session data
 * @param storeInfo - Optional store information
 * @returns Validation result with merchant data
 */
export function extractMerchantData(
  session: ShopifySession,
  storeInfo?: StoreInfo
): ValidationResult<{
  shop: string;
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  phone?: string | null;
  name?: string;
}> {
  try {
    // Construct full name from first and last name
    const name =
      [session.firstName, session.lastName].filter(Boolean).join(' ') ||
      undefined;

    // Use session email first, fall back to store email
    const email = session.email || storeInfo?.email || null;

    // Use store phone if available
    const phone = storeInfo?.phone || null;

    const merchantData = {
      shop: session.shop,
      firstName: session.firstName,
      lastName: session.lastName,
      email,
      phone,
      name,
    };

    return {
      success: true,
      data: merchantData,
    };
  } catch (_error) {
    return {
      success: false,
      errors: ['Failed to extract merchant data'],
    };
  }
}

/**
 * Extract and validate store data for organization creation
 * @param shop - Shop domain
 * @param shopData - Optional shop data
 * @param storeInfo - Optional store information
 * @returns Validation result with store data
 */
export function extractStoreData(
  shop: string,
  shopData?: ShopData,
  storeInfo?: StoreInfo
): ValidationResult<{
  shop: string;
  name?: string;
  currency?: string;
  timezone?: string;
  planName?: string;
  country?: string;
  registrationNumber?: string | null;
  tinCode?: string | null;
}> {
  try {
    // Use shop data name first, fall back to store info name, then shop domain
    const name = shopData?.name || storeInfo?.name || shop;

    const storeData = {
      shop,
      name,
      currency: shopData?.currency,
      timezone: shopData?.timezone,
      planName: shopData?.planName,
      country: shopData?.country,
      registrationNumber: storeInfo?.registrationNumber,
      tinCode: storeInfo?.tinCode,
    };

    return {
      success: true,
      data: storeData,
    };
  } catch (_error) {
    return {
      success: false,
      errors: ['Failed to extract store data'],
    };
  }
}

/**
 * Validate required fields for user creation
 * @param merchantData - Extracted merchant data
 * @returns Validation result
 */
export function validateUserCreationData(merchantData: {
  shop: string;
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  phone?: string | null;
  name?: string;
}): ValidationResult<boolean> {
  const errors: string[] = [];

  // Check for required fields
  if (!merchantData.shop) {
    errors.push('Shop domain is required');
  }

  // At least one of email or name should be present for user creation
  if (!merchantData.email && !merchantData.name && !merchantData.firstName) {
    errors.push(
      'At least one of email, name, or firstName is required for user creation'
    );
  }

  if (errors.length > 0) {
    return {
      success: false,
      errors,
    };
  }

  return {
    success: true,
    data: true,
  };
}

/**
 * Validate required fields for organization creation
 * @param storeData - Extracted store data
 * @returns Validation result
 */
export function validateOrganizationCreationData(storeData: {
  shop: string;
  name?: string;
}): ValidationResult<boolean> {
  const errors: string[] = [];

  if (!storeData.shop) {
    errors.push('Shop domain is required');
  }

  if (!storeData.name) {
    errors.push('Organization name is required');
  }

  if (errors.length > 0) {
    return {
      success: false,
      errors,
    };
  }

  return {
    success: true,
    data: true,
  };
}
