import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import { parseError } from '@repo/observability/error';

/**
 * Service for managing Shopify MerchantSyncState records
 */

export interface MerchantSyncStateData {
  shopId: string;
  accessToken: string;
  backendUserId?: string;
  backendOrganizationId?: string;
  syncStatus?: string;
  syncErrors?: string[] | null;
}

export interface SyncStateResult {
  success: boolean;
  syncStateId?: string;
  syncState?: {
    id: string;
    shopId: string;
    syncStatus: string;
    backendUserId?: string | null;
    backendOrganizationId?: string | null;
    lastSyncAt?: Date | null;
  };
  errors?: string[];
  warnings?: string[];
  isExisting?: boolean;
}

/**
 * Sync status constants
 */
export const SYNC_STATUS = {
  PENDING: 'PENDING',
  SYNCING: 'SYNCING',
  UP_TO_DATE: 'UP_TO_DATE',
  FAILED: 'FAILED',
  PARTIAL: 'PARTIAL', // When user created but organization failed, or vice versa
} as const;

/**
 * Find existing sync state for a shop
 * @param shopId - Shop domain
 * @returns Existing sync state or null
 */
export async function findSyncStateByShop(shopId: string) {
  try {
    return await database.merchantSyncState.findUnique({
      where: { shopId },
      select: {
        id: true,
        shopId: true,
        accessToken: true,
        backendUserId: true,
        backendOrganizationId: true,
        syncStatus: true,
        syncErrors: true,
        lastSyncAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  } catch (error) {
    log.warn('Error finding sync state by shop:', { shopId, error });
    return null;
  }
}

/**
 * Create a new merchant sync state record
 * @param data - Sync state data
 * @param correlationId - Optional correlation ID for logging
 * @returns Sync state creation result
 */
export async function createMerchantSyncState(
  data: MerchantSyncStateData,
  correlationId?: string
): Promise<SyncStateResult> {
  try {
    log.info('Creating merchant sync state', {
      shopId: data.shopId,
      correlationId,
    });

    // Check if sync state already exists
    const existingState = await findSyncStateByShop(data.shopId);
    if (existingState) {
      log.info('Sync state already exists for shop', {
        shopId: data.shopId,
        syncStateId: existingState.id,
        correlationId,
      });

      return {
        success: true,
        syncStateId: existingState.id,
        syncState: existingState,
        warnings: ['Sync state already exists for this shop'],
        isExisting: true,
      };
    }

    // Prepare sync state data
    const syncStateData = {
      shopId: data.shopId,
      accessToken: data.accessToken,
      backendUserId: data.backendUserId || null,
      backendOrganizationId: data.backendOrganizationId || null,
      syncStatus: data.syncStatus || SYNC_STATUS.PENDING,
      syncErrors: data.syncErrors ? JSON.stringify(data.syncErrors) : null,
      lastSyncAt: null, // Will be set when sync completes
    };

    // Create the sync state
    const syncState = await database.merchantSyncState.create({
      data: syncStateData,
      select: {
        id: true,
        shopId: true,
        syncStatus: true,
        backendUserId: true,
        backendOrganizationId: true,
        lastSyncAt: true,
      },
    });

    log.info('Merchant sync state created successfully', {
      syncStateId: syncState.id,
      shopId: data.shopId,
      correlationId,
    });

    return {
      success: true,
      syncStateId: syncState.id,
      syncState,
      warnings: ['Sync state created successfully'],
      isExisting: false,
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error creating merchant sync state', {
      shopId: data.shopId,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}

/**
 * Update an existing merchant sync state record
 * @param shopId - Shop domain
 * @param updates - Updates to apply
 * @param correlationId - Optional correlation ID for logging
 * @returns Sync state update result
 */
export async function updateMerchantSyncState(
  shopId: string,
  updates: {
    accessToken?: string;
    backendUserId?: string | null;
    backendOrganizationId?: string | null;
    syncStatus?: string;
    syncErrors?: string[] | null;
    lastSyncAt?: Date | null;
  },
  correlationId?: string
): Promise<SyncStateResult> {
  try {
    log.info('Updating merchant sync state', {
      shopId,
      updates: Object.keys(updates),
      correlationId,
    });

    // Prepare update data
    const updateData: {
      accessToken?: string;
      backendUserId?: string | null;
      backendOrganizationId?: string | null;
      syncStatus?: string;
      syncErrors?: string | null;
      lastSyncAt?: Date | null;
    } = {};

    if (updates.accessToken !== undefined) {
      updateData.accessToken = updates.accessToken;
    }
    if (updates.backendUserId !== undefined) {
      updateData.backendUserId = updates.backendUserId;
    }
    if (updates.backendOrganizationId !== undefined) {
      updateData.backendOrganizationId = updates.backendOrganizationId;
    }
    if (updates.syncStatus !== undefined) {
      updateData.syncStatus = updates.syncStatus;
    }
    if (updates.syncErrors !== undefined) {
      updateData.syncErrors = updates.syncErrors
        ? JSON.stringify(updates.syncErrors)
        : null;
    }
    if (updates.lastSyncAt !== undefined) {
      updateData.lastSyncAt = updates.lastSyncAt;
    }

    // Update the sync state
    const syncState = await database.merchantSyncState.update({
      where: { shopId },
      data: updateData,
      select: {
        id: true,
        shopId: true,
        syncStatus: true,
        backendUserId: true,
        backendOrganizationId: true,
        lastSyncAt: true,
      },
    });

    log.info('Merchant sync state updated successfully', {
      syncStateId: syncState.id,
      shopId,
      correlationId,
    });

    return {
      success: true,
      syncStateId: syncState.id,
      syncState,
      warnings: ['Sync state updated successfully'],
      isExisting: true,
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error updating merchant sync state', {
      shopId,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}

/**
 * Upsert (create or update) merchant sync state
 * @param data - Sync state data
 * @param correlationId - Optional correlation ID for logging
 * @returns Sync state upsert result
 */
export async function upsertMerchantSyncState(
  data: MerchantSyncStateData,
  correlationId?: string
): Promise<SyncStateResult> {
  try {
    log.info('Upserting merchant sync state', {
      shopId: data.shopId,
      correlationId,
    });

    // Prepare sync state data
    const syncStateData = {
      shopId: data.shopId,
      accessToken: data.accessToken,
      backendUserId: data.backendUserId || null,
      backendOrganizationId: data.backendOrganizationId || null,
      syncStatus: data.syncStatus || SYNC_STATUS.PENDING,
      syncErrors: data.syncErrors ? JSON.stringify(data.syncErrors) : null,
    };

    // Prepare update data (excludes shopId since it's the unique key)
    const updateData = {
      accessToken: syncStateData.accessToken,
      backendUserId: syncStateData.backendUserId,
      backendOrganizationId: syncStateData.backendOrganizationId,
      syncStatus: syncStateData.syncStatus,
      syncErrors: syncStateData.syncErrors,
      lastSyncAt: new Date(),
    };

    // Upsert the sync state
    const syncState = await database.merchantSyncState.upsert({
      where: { shopId: data.shopId },
      update: updateData,
      create: {
        ...syncStateData,
        lastSyncAt: null, // New records don't have lastSyncAt initially
      },
      select: {
        id: true,
        shopId: true,
        syncStatus: true,
        backendUserId: true,
        backendOrganizationId: true,
        lastSyncAt: true,
      },
    });

    log.info('Merchant sync state upserted successfully', {
      syncStateId: syncState.id,
      shopId: data.shopId,
      correlationId,
    });

    return {
      success: true,
      syncStateId: syncState.id,
      syncState,
      warnings: ['Sync state upserted successfully'],
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error upserting merchant sync state', {
      shopId: data.shopId,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}

/**
 * Mark sync as completed successfully
 * @param shopId - Shop domain
 * @param userId - Backend user ID
 * @param organizationId - Backend organization ID
 * @param correlationId - Optional correlation ID for logging
 * @returns Sync completion result
 */

// biome-ignore lint/suspicious/useAwait: <explanation>
export async function markSyncCompleted(
  shopId: string,
  userId: string,
  organizationId: string,
  correlationId?: string
): Promise<SyncStateResult> {
  return updateMerchantSyncState(
    shopId,
    {
      backendUserId: userId,
      backendOrganizationId: organizationId,
      syncStatus: SYNC_STATUS.UP_TO_DATE,
      syncErrors: null,
      lastSyncAt: new Date(),
    },
    correlationId
  );
}

/**
 * Mark sync as failed
 * @param shopId - Shop domain
 * @param errors - Error messages
 * @param correlationId - Optional correlation ID for logging
 * @returns Sync failure result
 */

// biome-ignore lint/suspicious/useAwait: <explanation>
export async function markSyncFailed(
  shopId: string,
  errors: string[],
  correlationId?: string
): Promise<SyncStateResult> {
  return updateMerchantSyncState(
    shopId,
    {
      syncStatus: SYNC_STATUS.FAILED,
      syncErrors: errors,
    },
    correlationId
  );
}

/**
 * Get sync state with related user and organization data
 * @param shopId - Shop domain
 * @returns Sync state with relations or null
 */
export async function getSyncStateWithRelations(shopId: string) {
  try {
    return await database.merchantSyncState.findUnique({
      where: { shopId },
      include: {
        backendUser: {
          select: {
            id: true,
            name: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        backendOrganization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });
  } catch (error) {
    log.warn('Error getting sync state with relations:', { shopId, error });
    return null;
  }
}
