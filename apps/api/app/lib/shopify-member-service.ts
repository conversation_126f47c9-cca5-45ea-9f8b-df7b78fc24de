import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import { parseError } from '@repo/observability/error';

/**
 * Service for managing Shopify user-organization member relationships
 */

export interface MemberLinkingResult {
  success: boolean;
  memberId?: string;
  member?: {
    id: string;
    userId: string;
    organizationId: string;
    role: string;
  };
  errors?: string[];
  warnings?: string[];
  isExisting?: boolean;
}

/**
 * Check if a member relationship already exists
 * @param userId - User ID
 * @param organizationId - Organization ID
 * @returns Existing member or null
 */
export async function findExistingMember(
  userId: string,
  organizationId: string
) {
  try {
    return await database.member.findFirst({
      where: {
        userId,
        organizationId,
      },
      select: {
        id: true,
        userId: true,
        organizationId: true,
        role: true,
      },
    });
  } catch (error) {
    log.warn('Error finding existing member:', { userId, organizationId, error });
    return null;
  }
}

/**
 * Get all members for an organization
 * @param organizationId - Organization ID
 * @returns Array of members
 */
export async function getOrganizationMembers(organizationId: string) {
  try {
    return await database.member.findMany({
      where: { organizationId },
      select: {
        id: true,
        userId: true,
        organizationId: true,
        role: true,
      },
    });
  } catch (error) {
    log.warn('Error getting organization members:', { organizationId, error });
    return [];
  }
}

/**
 * Determine the appropriate role for a Shopify merchant
 * @param userId - User ID
 * @param organizationId - Organization ID
 * @param isShopOwner - Whether this user is the shop owner (default: true)
 * @returns Role string
 */
async function determineShopifyMemberRole(
  userId: string,
  organizationId: string,
  isShopOwner: boolean = true
): Promise<string> {
  try {
    // Get existing members to determine if this is the first member
    const existingMembers = await getOrganizationMembers(organizationId);
    
    // If no existing members, this user becomes the owner
    if (existingMembers.length === 0) {
      return 'owner';
    }

    // Check if there's already an owner
    const hasOwner = existingMembers.some(member => member.role === 'owner');
    
    if (!hasOwner && isShopOwner) {
      // No owner exists and this is the shop owner, make them owner
      return 'owner';
    }

    // Default to admin role for Shopify merchants
    return 'admin';
  } catch (error) {
    log.warn('Error determining member role, defaulting to admin:', { 
      userId, 
      organizationId, 
      error 
    });
    return 'admin';
  }
}

/**
 * Create a member relationship between user and organization
 * @param userId - User ID
 * @param organizationId - Organization ID
 * @param role - Member role (optional, will be determined automatically)
 * @param correlationId - Optional correlation ID for logging
 * @returns Member linking result
 */
export async function linkUserToOrganization(
  userId: string,
  organizationId: string,
  role?: string,
  correlationId?: string
): Promise<MemberLinkingResult> {
  try {
    log.info('Linking user to organization', {
      userId,
      organizationId,
      role,
      correlationId,
    });

    // Check if member relationship already exists
    const existingMember = await findExistingMember(userId, organizationId);
    if (existingMember) {
      log.info('Member relationship already exists', {
        memberId: existingMember.id,
        userId,
        organizationId,
        role: existingMember.role,
        correlationId,
      });

      return {
        success: true,
        memberId: existingMember.id,
        member: existingMember,
        warnings: ['Member relationship already exists'],
        isExisting: true,
      };
    }

    // Determine role if not provided
    const memberRole = role || await determineShopifyMemberRole(userId, organizationId);

    // Create the member relationship
    const member = await database.member.create({
      data: {
        userId,
        organizationId,
        role: memberRole,
      },
      select: {
        id: true,
        userId: true,
        organizationId: true,
        role: true,
      },
    });

    log.info('Member relationship created successfully', {
      memberId: member.id,
      userId,
      organizationId,
      role: member.role,
      correlationId,
    });

    return {
      success: true,
      memberId: member.id,
      member,
      warnings: ['Member relationship created successfully'],
      isExisting: false,
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error linking user to organization', {
      userId,
      organizationId,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}

/**
 * Update an existing member relationship
 * @param memberId - Member ID
 * @param role - New role
 * @param correlationId - Optional correlation ID for logging
 * @returns Member update result
 */
export async function updateMemberRole(
  memberId: string,
  role: string,
  correlationId?: string
): Promise<MemberLinkingResult> {
  try {
    log.info('Updating member role', {
      memberId,
      role,
      correlationId,
    });

    const member = await database.member.update({
      where: { id: memberId },
      data: { role },
      select: {
        id: true,
        userId: true,
        organizationId: true,
        role: true,
      },
    });

    log.info('Member role updated successfully', {
      memberId: member.id,
      userId: member.userId,
      organizationId: member.organizationId,
      role: member.role,
      correlationId,
    });

    return {
      success: true,
      memberId: member.id,
      member,
      warnings: ['Member role updated successfully'],
      isExisting: true,
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error updating member role', {
      memberId,
      role,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}

/**
 * Remove a member relationship
 * @param userId - User ID
 * @param organizationId - Organization ID
 * @param correlationId - Optional correlation ID for logging
 * @returns Member removal result
 */
export async function unlinkUserFromOrganization(
  userId: string,
  organizationId: string,
  correlationId?: string
): Promise<MemberLinkingResult> {
  try {
    log.info('Unlinking user from organization', {
      userId,
      organizationId,
      correlationId,
    });

    // Find the member relationship
    const existingMember = await findExistingMember(userId, organizationId);
    if (!existingMember) {
      return {
        success: false,
        errors: ['Member relationship not found'],
      };
    }

    // Delete the member relationship
    await database.member.delete({
      where: { id: existingMember.id },
    });

    log.info('Member relationship removed successfully', {
      memberId: existingMember.id,
      userId,
      organizationId,
      correlationId,
    });

    return {
      success: true,
      memberId: existingMember.id,
      warnings: ['Member relationship removed successfully'],
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error unlinking user from organization', {
      userId,
      organizationId,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}

/**
 * Get member details with user and organization information
 * @param memberId - Member ID
 * @returns Member details or null
 */
export async function getMemberDetails(memberId: string) {
  try {
    return await database.member.findUnique({
      where: { id: memberId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });
  } catch (error) {
    log.warn('Error getting member details:', { memberId, error });
    return null;
  }
}
