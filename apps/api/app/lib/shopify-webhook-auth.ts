import { env } from '@/env';
import { log } from '@repo/observability/log';
import { createHmac, timingSafeEqual } from 'node:crypto';
import { NextResponse } from 'next/server';

/**
 * Shopify webhook authentication utilities
 * Provides functions to verify webhook signatures and authenticate requests
 */

/**
 * Verify Shopify webhook signature using HMAC-SHA256
 * @param body - Raw request body as string
 * @param signature - Signature from X-Shopify-Hmac-Sha256 header
 * @param secret - Webhook secret for verification
 * @returns boolean indicating if signature is valid
 */
export function verifyShopifyWebhookSignature(
  body: string,
  signature: string,
  secret: string
): boolean {
  try {
    // Create HMAC using the webhook secret
    const hmac = createHmac('sha256', secret);
    hmac.update(body, 'utf8');
    const calculatedSignature = hmac.digest('base64');

    // Use timing-safe comparison to prevent timing attacks
    const providedSignature = Buffer.from(signature, 'base64');
    const expectedSignature = Buffer.from(calculatedSignature, 'base64');

    return (
      providedSignature.length === expectedSignature.length &&
      timingSafeEqual(providedSignature, expectedSignature)
    );
  } catch (error) {
    log.warn('Error verifying Shopify webhook signature:', { error });
    return false;
  }
}

/**
 * Alternative verification using API key authentication
 * For cases where we use a simple API key instead of HMAC signature
 * @param request - The incoming request
 * @returns boolean indicating if API key is valid
 */
export function verifyShopifyApiKey(request: Request): boolean {
  try {
    const apiKey = request.headers.get('X-Shopify-Api-Key');

    if (!apiKey) {
      return false;
    }

    // For now, we'll use the webhook secret as the API key
    // In production, you might want a separate API key
    return apiKey === env.SHOPIFY_WEBHOOK_SECRET;
  } catch (error) {
    log.warn('Error verifying Shopify API key:', { error });
    return false;
  }
}

/**
 * Authenticate Shopify webhook request
 * Supports both HMAC signature verification and API key authentication
 * @param request - The incoming request
 * @param body - Raw request body as string
 * @returns object with authentication result and error details
 */

// biome-ignore lint/suspicious/useAwait: <explanation>
export async function authenticateShopifyWebhook(
  request: Request,
  body: string
): Promise<{
  isValid: boolean;
  error?: string;
  method?: 'hmac' | 'api-key';
}> {
  // Check if webhook secret is configured
  if (!env.SHOPIFY_WEBHOOK_SECRET) {
    log.warn('Shopify webhook secret not configured');
    return {
      isValid: false,
      error: 'Webhook authentication not configured',
    };
  }

  // Try HMAC signature verification first
  const hmacSignature = request.headers.get('X-Shopify-Hmac-Sha256');
  if (hmacSignature) {
    const isValidHmac = verifyShopifyWebhookSignature(
      body,
      hmacSignature,
      env.SHOPIFY_WEBHOOK_SECRET
    );

    if (isValidHmac) {
      return {
        isValid: true,
        method: 'hmac',
      };
    }

    log.warn('Invalid Shopify HMAC signature');

    return {
      isValid: false,
      error: 'Invalid HMAC signature',
      method: 'hmac',
    };
  }

  // Fall back to API key authentication
  const isValidApiKey = verifyShopifyApiKey(request);
  if (isValidApiKey) {
    return {
      isValid: true,
      method: 'api-key',
    };
  }

  // No valid authentication method found
  log.warn('No valid Shopify authentication found', {
    hasHmacHeader: !!hmacSignature,
    hasApiKeyHeader: !!request.headers.get('X-Shopify-Api-Key'),
  });

  return {
    isValid: false,
    error: 'No valid authentication method found',
  };
}

/**
 * Create authentication error response
 * @param error - Error message
 * @param status - HTTP status code (default: 401)
 * @returns NextResponse with error
 */
export function createAuthErrorResponse(
  error: string,
  status = 401
): NextResponse {
  return NextResponse.json(
    {
      error: 'Authentication failed',
      message: error,
    },
    { status }
  );
}

/**
 * Higher-order function to wrap webhook handlers with Shopify authentication
 * @param handler - The webhook handler function
 * @returns Wrapped handler with authentication
 */
export function withShopifyWebhookAuth(
  handler: (request: Request, body: string) => Promise<Response>
) {
  return async (request: Request): Promise<Response> => {
    try {
      // Get the raw body
      const body = await request.text();

      // Authenticate the request
      const authResult = await authenticateShopifyWebhook(request, body);

      if (!authResult.isValid) {
        log.warn('Shopify webhook authentication failed', {
          error: authResult.error,
          method: authResult.method,
          url: request.url,
        });

        return createAuthErrorResponse(
          authResult.error || 'Authentication failed'
        );
      }

      // Log successful authentication
      log.info('Shopify webhook authenticated successfully', {
        method: authResult.method,
        url: request.url,
      });

      // Call the original handler
      return await handler(request, body);
    } catch (error) {
      log.error('Error in Shopify webhook authentication wrapper:', { error });
      return NextResponse.json(
        {
          error: 'Internal server error',
          message: 'Authentication processing failed',
        },
        { status: 500 }
      );
    }
  };
}
