import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import { parseError } from '@repo/observability/error';

/**
 * Service for managing Shopify organization creation and updates
 */

export interface ShopifyStoreData {
  shop: string;
  name?: string;
  currency?: string;
  timezone?: string;
  planName?: string;
  country?: string;
  registrationNumber?: string | null;
  tinCode?: string | null;
}

export interface OrganizationCreationResult {
  success: boolean;
  organizationId?: string;
  organization?: {
    id: string;
    name: string;
    slug?: string | null;
  };
  errors?: string[];
  warnings?: string[];
  isExisting?: boolean;
}

/**
 * Generate a URL-friendly slug from organization name
 * @param name - Organization name
 * @param shop - Shop domain for uniqueness
 * @returns Generated slug
 */
function generateOrganizationSlug(name: string, shop: string): string {
  // Clean the name for slug
  const cleanName = name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();

  // Clean the shop domain
  const cleanShop = shop
    .replace('.myshopify.com', '')
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '');

  // Combine name and shop for uniqueness
  return `${cleanName}-${cleanShop}`.substring(0, 50); // Limit length
}

/**
 * Prepare organization data for creation from Shopify store data
 * @param storeData - Shopify store data
 * @returns Prepared organization data
 */
function prepareOrganizationData(storeData: ShopifyStoreData) {
  const name = storeData.name || storeData.shop;
  const slug = generateOrganizationSlug(name, storeData.shop);

  // Prepare metadata with Shopify-specific information
  const metadata = {
    shopifyShop: storeData.shop,
    currency: storeData.currency,
    timezone: storeData.timezone,
    planName: storeData.planName,
    country: storeData.country,
    registrationNumber: storeData.registrationNumber,
    tinCode: storeData.tinCode,
    source: 'shopify',
    createdAt: new Date().toISOString(),
  };

  return {
    name,
    slug,
    metadata: JSON.stringify(metadata),
  };
}

/**
 * Check if an organization already exists for the given shop
 * @param shop - Shop domain
 * @returns Existing organization or null
 */
export async function findExistingOrganizationForShop(shop: string) {
  try {
    // First, check if there's a merchant sync state for this shop
    const syncState = await database.merchantSyncState.findUnique({
      where: { shopId: shop },
      include: {
        backendOrganization: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });

    if (syncState?.backendOrganization) {
      return syncState.backendOrganization;
    }

    return null;
  } catch (error) {
    log.warn('Error finding existing organization for shop:', { shop, error });
    return null;
  }
}

/**
 * Check if an organization exists by slug
 * @param slug - Organization slug
 * @returns Existing organization or null
 */
export async function findOrganizationBySlug(slug: string) {
  try {
    return await database.organization.findUnique({
      where: { slug },
      select: {
        id: true,
        name: true,
        slug: true,
      },
    });
  } catch (error) {
    log.warn('Error finding organization by slug:', { slug, error });
    return null;
  }
}

/**
 * Generate a unique slug by appending numbers if needed
 * @param baseSlug - Base slug to make unique
 * @returns Unique slug
 */
async function generateUniqueSlug(baseSlug: string): Promise<string> {
  let slug = baseSlug;
  let counter = 1;

  while (await findOrganizationBySlug(slug)) {
    slug = `${baseSlug}-${counter}`;
    counter++;

    // Prevent infinite loop
    if (counter > 100) {
      slug = `${baseSlug}-${Date.now()}`;
      break;
    }
  }

  return slug;
}

/**
 * Create a new organization from Shopify store data
 * @param storeData - Shopify store data
 * @param correlationId - Optional correlation ID for logging
 * @returns Organization creation result
 */
export async function createOrganizationFromShopifyData(
  storeData: ShopifyStoreData,
  correlationId?: string
): Promise<OrganizationCreationResult> {
  try {
    log.info('Creating organization from Shopify data', {
      shop: storeData.shop,
      correlationId,
    });

    // Check if organization already exists for this shop
    const existingOrg = await findExistingOrganizationForShop(storeData.shop);
    if (existingOrg) {
      log.info('Organization already exists for shop', {
        shop: storeData.shop,
        organizationId: existingOrg.id,
        correlationId,
      });

      return {
        success: true,
        organizationId: existingOrg.id,
        organization: existingOrg,
        warnings: ['Organization already exists for this shop'],
        isExisting: true,
      };
    }

    // Prepare organization data
    const orgData = prepareOrganizationData(storeData);

    // Ensure slug is unique
    const uniqueSlug = await generateUniqueSlug(orgData.slug);
    orgData.slug = uniqueSlug;

    // Create the organization
    const organization = await database.organization.create({
      data: orgData,
      select: {
        id: true,
        name: true,
        slug: true,
      },
    });

    log.info('Organization created successfully', {
      organizationId: organization.id,
      shop: storeData.shop,
      correlationId,
    });

    return {
      success: true,
      organizationId: organization.id,
      organization,
      warnings: ['Organization created successfully'],
      isExisting: false,
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error creating organization from Shopify data', {
      shop: storeData.shop,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}

/**
 * Update an existing organization with Shopify store data
 * @param organizationId - Organization ID to update
 * @param storeData - Shopify store data
 * @param correlationId - Optional correlation ID for logging
 * @returns Organization update result
 */
export async function updateOrganizationFromShopifyData(
  organizationId: string,
  storeData: ShopifyStoreData,
  correlationId?: string
): Promise<OrganizationCreationResult> {
  try {
    log.info('Updating organization from Shopify data', {
      organizationId,
      shop: storeData.shop,
      correlationId,
    });

    // Get current organization
    const currentOrg = await database.organization.findUnique({
      where: { id: organizationId },
      select: {
        id: true,
        name: true,
        slug: true,
        metadata: true,
      },
    });

    if (!currentOrg) {
      return {
        success: false,
        errors: ['Organization not found'],
      };
    }

    // Parse existing metadata
    let existingMetadata = {};
    try {
      if (currentOrg.metadata) {
        existingMetadata = JSON.parse(currentOrg.metadata);
      }
    } catch (error) {
      log.warn('Error parsing existing organization metadata', {
        organizationId,
        error,
      });
    }

    // Prepare update data
    const updateData: {
      name?: string;
      metadata?: string;
    } = {};

    // Update name if provided and different
    if (storeData.name && storeData.name !== currentOrg.name) {
      updateData.name = storeData.name;
    }

    // Update metadata with new Shopify information
    const newMetadata = {
      ...existingMetadata,
      shopifyShop: storeData.shop,
      currency: storeData.currency,
      timezone: storeData.timezone,
      planName: storeData.planName,
      country: storeData.country,
      registrationNumber: storeData.registrationNumber,
      tinCode: storeData.tinCode,
      source: 'shopify',
      updatedAt: new Date().toISOString(),
    };

    updateData.metadata = JSON.stringify(newMetadata);

    // Update the organization
    const organization = await database.organization.update({
      where: { id: organizationId },
      data: updateData,
      select: {
        id: true,
        name: true,
        slug: true,
      },
    });

    log.info('Organization updated successfully', {
      organizationId: organization.id,
      shop: storeData.shop,
      correlationId,
    });

    return {
      success: true,
      organizationId: organization.id,
      organization,
      warnings: ['Organization updated successfully'],
      isExisting: true,
    };
  } catch (error) {
    const message = parseError(error);
    log.error('Error updating organization from Shopify data', {
      organizationId,
      shop: storeData.shop,
      error: message,
      correlationId,
    });

    return {
      success: false,
      errors: [message],
    };
  }
}
