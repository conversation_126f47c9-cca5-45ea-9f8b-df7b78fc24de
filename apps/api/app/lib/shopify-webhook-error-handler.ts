import { log } from '@repo/observability/log';
import { parseError } from '@repo/observability/error';
import { NextResponse } from 'next/server';
import { markSyncFailed } from './shopify-sync-state-service';

/**
 * Comprehensive error handling utilities for Shopify webhooks
 * Provides consistent error handling, logging, and response patterns
 */

// biome-ignore lint/nursery/noEnum: <explanation>
export enum ShopifyWebhookErrorType {
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  USER_CREATION_ERROR = 'USER_CREATION_ERROR',
  ORGANIZATION_CREATION_ERROR = 'ORGANIZATION_CREATION_ERROR',
  MEMBER_LINKING_ERROR = 'MEMBER_LINKING_ERROR',
  SYNC_STATE_ERROR = 'SYNC_STATE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface ShopifyWebhookError {
  type: ShopifyWebhookErrorType;
  message: string;
  details?: any;
  correlationId?: string;
  shop?: string;
  cause?: Error;
  retryable?: boolean;
}

export interface ErrorContext {
  correlationId: string;
  shop?: string;
  operation?: string;
  duration?: number;
  payload?: any;
  step?: string;
}

/**
 * Create a structured Shopify webhook error
 */
export function createShopifyWebhookError(
  type: ShopifyWebhookErrorType,
  message: string,
  context?: Partial<ErrorContext>,
  cause?: Error
): ShopifyWebhookError {
  return {
    type,
    message,
    details: context,
    correlationId: context?.correlationId,
    shop: context?.shop,
    cause,
    retryable: isRetryableError(type),
  };
}

/**
 * Determine if an error type is retryable
 */
function isRetryableError(type: ShopifyWebhookErrorType): boolean {
  const retryableTypes = [
    ShopifyWebhookErrorType.DATABASE_ERROR,
    ShopifyWebhookErrorType.EXTERNAL_API_ERROR,
    ShopifyWebhookErrorType.TIMEOUT_ERROR,
    ShopifyWebhookErrorType.UNKNOWN_ERROR,
  ];

  return retryableTypes.includes(type);
}

/**
 * Log error with appropriate level and context
 */
export function logShopifyWebhookError(
  error: ShopifyWebhookError,
  context?: Partial<ErrorContext>
): void {
  const logContext = {
    errorType: error.type,
    message: error.message,
    correlationId: error.correlationId,
    shop: error.shop,
    retryable: error.retryable,
    ...context,
    ...(error.details || {}),
  };

  // Log cause if available
  if (error.cause) {
    logContext.cause = parseError(error.cause);
  }

  // Use appropriate log level based on error type
  switch (error.type) {
    case ShopifyWebhookErrorType.AUTHENTICATION_ERROR:
      log.warn('Shopify webhook authentication error', logContext);
      break;

    case ShopifyWebhookErrorType.VALIDATION_ERROR:
      log.warn('Shopify webhook validation error', logContext);
      break;

    case ShopifyWebhookErrorType.DATABASE_ERROR:
    case ShopifyWebhookErrorType.EXTERNAL_API_ERROR:
    case ShopifyWebhookErrorType.TIMEOUT_ERROR:
      log.error('Shopify webhook processing error', logContext);
      break;

    case ShopifyWebhookErrorType.UNKNOWN_ERROR:
      log.error('Shopify webhook unknown error', logContext);
      break;

    default:
      log.warn('Shopify webhook error', logContext);
  }
}

/**
 * Create HTTP error response from Shopify webhook error
 */
export function createErrorResponse(
  error: ShopifyWebhookError,
  includeDetails = false
): NextResponse {
  const responseBody: any = {
    success: false,
    error: error.message,
    type: error.type,
    correlationId: error.correlationId,
    retryable: error.retryable,
  };

  if (includeDetails && error.details) {
    responseBody.details = error.details;
  }

  // Determine HTTP status code based on error type
  let status: number;
  switch (error.type) {
    case ShopifyWebhookErrorType.AUTHENTICATION_ERROR:
      status = 401;
      break;

    case ShopifyWebhookErrorType.VALIDATION_ERROR:
      status = 400;
      break;

    case ShopifyWebhookErrorType.DATABASE_ERROR:
    case ShopifyWebhookErrorType.EXTERNAL_API_ERROR:
    case ShopifyWebhookErrorType.USER_CREATION_ERROR:
    case ShopifyWebhookErrorType.ORGANIZATION_CREATION_ERROR:
    case ShopifyWebhookErrorType.MEMBER_LINKING_ERROR:
    case ShopifyWebhookErrorType.SYNC_STATE_ERROR:
      status = 500;
      break;

    case ShopifyWebhookErrorType.TIMEOUT_ERROR:
      status = 504;
      break;

    default:
      status = 500;
  }

  return NextResponse.json(responseBody, { status });
}

/**
 * Handle error with logging and sync state update
 */
export async function handleShopifyWebhookError(
  error: ShopifyWebhookError,
  context?: Partial<ErrorContext>
): Promise<NextResponse> {
  // Log the error
  logShopifyWebhookError(error, context);

  // Update sync state if shop is available
  if (error.shop) {
    try {
      await markSyncFailed(error.shop, [error.message], error.correlationId);
    } catch (syncError) {
      log.error('Failed to update sync state after error', {
        originalError: error.message,
        syncError: parseError(syncError),
        shop: error.shop,
        correlationId: error.correlationId,
      });
    }
  }

  // Create and return error response
  return createErrorResponse(error, true);
}

/**
 * Wrap async operations with error handling
 */
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  errorType: ShopifyWebhookErrorType,
  errorMessage: string,
  context?: Partial<ErrorContext>
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    throw createShopifyWebhookError(
      errorType,
      errorMessage,
      context,
      error as Error
    );
  }
}

/**
 * Validate and handle operation result
 */
export function validateOperationResult<
  T extends { success: boolean; errors?: string[] },
>(
  result: T,
  errorType: ShopifyWebhookErrorType,
  operation: string,
  context?: Partial<ErrorContext>
): T {
  if (!result.success) {
    const errorMessage = `${operation} failed: ${result.errors?.join(', ') || 'Unknown error'}`;
    throw createShopifyWebhookError(errorType, errorMessage, {
      ...context,
      operation,
      errors: result.errors,
    });
  }

  return result;
}

/**
 * Create timeout wrapper for operations
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  operation: string,
  context?: Partial<ErrorContext>
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(
          createShopifyWebhookError(
            ShopifyWebhookErrorType.TIMEOUT_ERROR,
            `${operation} timed out after ${timeoutMs}ms`,
            {
              ...context,
              operation,
              timeoutMs,
            }
          )
        );
      }, timeoutMs);
    }),
  ]);
}

/**
 * Retry wrapper for operations
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000,
  context?: Partial<ErrorContext>
): Promise<T> {
  let lastError: ShopifyWebhookError | undefined;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as ShopifyWebhookError;

      // Don't retry non-retryable errors
      if (!lastError.retryable) {
        throw lastError;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }

      log.warn('Operation failed, retrying', {
        attempt,
        maxRetries,
        delayMs,
        error: lastError.message,
        correlationId: context?.correlationId,
        operation: context?.operation,
      });

      // Wait before retry with exponential backoff
      await new Promise((resolve) => setTimeout(resolve, delayMs * attempt));
    }
  }

  throw lastError;
}

/**
 * Business logic error handler for specific operations
 */
export class ShopifyWebhookErrorHandler {
  constructor(private context: ErrorContext) {}

  handleValidationError(message: string, details?: any): never {
    throw createShopifyWebhookError(
      ShopifyWebhookErrorType.VALIDATION_ERROR,
      message,
      { ...this.context, details }
    );
  }

  handleUserCreationError(message: string, cause?: Error): never {
    throw createShopifyWebhookError(
      ShopifyWebhookErrorType.USER_CREATION_ERROR,
      message,
      this.context,
      cause
    );
  }

  handleOrganizationCreationError(message: string, cause?: Error): never {
    throw createShopifyWebhookError(
      ShopifyWebhookErrorType.ORGANIZATION_CREATION_ERROR,
      message,
      this.context,
      cause
    );
  }

  handleMemberLinkingError(message: string, cause?: Error): never {
    throw createShopifyWebhookError(
      ShopifyWebhookErrorType.MEMBER_LINKING_ERROR,
      message,
      this.context,
      cause
    );
  }

  handleSyncStateError(message: string, cause?: Error): never {
    throw createShopifyWebhookError(
      ShopifyWebhookErrorType.SYNC_STATE_ERROR,
      message,
      this.context,
      cause
    );
  }

  handleDatabaseError(message: string, cause?: Error): never {
    throw createShopifyWebhookError(
      ShopifyWebhookErrorType.DATABASE_ERROR,
      message,
      this.context,
      cause
    );
  }
}
