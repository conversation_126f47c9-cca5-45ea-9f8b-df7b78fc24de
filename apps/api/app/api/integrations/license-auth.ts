import { env } from '@/env';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

/**
 * Type for route handler functions
 */
type RouteHandler<T = unknown> = (
  req: NextRequest,
  context: { params: Promise<T> }
) => Promise<NextResponse>;

/**
 * Extracts the API key from the request headers
 */
export const extractApiKey = (request: NextRequest): string | null => {
  const apiKeyHeader = request.headers.get('X-Invois-Key');

  return apiKeyHeader;
};

/**
 * Verifies the API key using the better-auth package
 * Returns the API key information if valid, or null if invalid
 */
export const verifyApiKey = (request: NextRequest) => {
  try {
    // Extract API key from request headers
    const apiKey = extractApiKey(request);

    // Simple verification
    if (apiKey !== env.CORE_INCOMING_API_KEY) {
      return false;
    }

    return true;
  } catch (error) {
    log.warn('API key verification error:', { error });
    return false;
  }
};

/**
 * Higher-order function that wraps a route handler with API key authentication
 * If the API key is invalid, returns a 401 Unauthorized response
 * If the API key is valid, calls the original handler with the request and context
 */
export function withLicenseKeyAuth<T = unknown>(handler: RouteHandler<T>) {
  return (req: NextRequest, context: { params: Promise<T> }) => {
    // Verify API key
    const apiKey = verifyApiKey(req);

    if (!apiKey) {
      log.warn('API request rejected: No valid API key provided', {
        path: req.nextUrl.pathname,
        method: req.method,
      });

      return NextResponse.json(
        { error: 'Unauthorized: Valid API key required' },
        { status: 401 }
      );
    }

    return handler(req, context);

    // Create a new request with the modified headers
    // const newRequest = new NextRequest(req.url, {
    //   method: req.method,
    //   headers: req.headers,
    //   body: req.body,
    //   cache: req.cache,
    //   credentials: req.credentials,
    //   integrity: req.integrity,
    //   keepalive: req.keepalive,
    //   mode: req.mode,
    //   redirect: req.redirect,
    //   referrer: req.referrer,
    //   referrerPolicy: req.referrerPolicy,
    // });

    // // Call the original handler with the new request
    // return handler(newRequest, context);
  };
}
