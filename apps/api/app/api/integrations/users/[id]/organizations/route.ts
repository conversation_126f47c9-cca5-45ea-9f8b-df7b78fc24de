import { database, serializePrisma } from '@repo/database';
import { type NextRequest, NextResponse } from 'next/server';
import { withLicense<PERSON>eyAuth } from '../../../license-auth';

type RouteParams = {
  id: string;
};

const handler = async (
  _request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) => {
  const { id: userId } = await params;

  const organizations = await database.organization.findMany({
    where: {
      members: {
        some: {
          userId,
        },
      },
    },
    select: {
      id: true,
      name: true,
      slug: true,
      logo: true,
      premiumTier: true,
      members: {
        where: {
          userId,
        },
        select: {
          role: true,
        },
      },
      _count: {
        select: {
          members: true,
        },
      },
    },
  });

  return NextResponse.json(
    {
      data: serializePrisma(
        organizations.map((org) => ({
          ...org,
          role: org.members[0]?.role,
          members: undefined,
        }))
      ),
    },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withLicense<PERSON>eyAuth<RouteParams>(handler);
export const GET = withLicenseKeyAuth<RouteParams>(handler);
