import { database, serializePrisma } from '@repo/database';
import { type NextRequest, NextResponse } from 'next/server';
import { withLicenseKeyAuth } from '../license-auth';

const handler = async (request: NextRequest) => {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const page = Number.parseInt(searchParams.get('page') || '1', 10);
  const perPage = Number.parseInt(searchParams.get('per_page') || '10', 10);
  const skip = (page - 1) * perPage;

  const [totalCount, organizations] = await Promise.all([
    database.organization.count({
      where: {
        OR: [
          {
            name: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
        ],
      },
    }),
    database.organization.findMany({
      where: {
        OR: [
          {
            name: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
        ],
      },
      select: {
        id: true,
        name: true,
        slug: true,
        logo: true,
        premiumTier: true,
        _count: {
          select: {
            members: true,
          },
        },
      },
      skip,
      take: perPage,
    }),
  ]);

  return NextResponse.json(
    {
      data: serializePrisma(organizations),
      meta: {
        total: totalCount,
        per_page: perPage,
        current_page: page,
        last_page: Math.ceil(totalCount / perPage),
        from: skip + 1,
        to: skip + organizations.length,
      },
    },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withLicenseKeyAuth(handler);
export const GET = withLicenseKeyAuth(handler);
