import { database, serializePrisma } from '@repo/database';
import { type NextRequest, NextResponse } from 'next/server';
import { withLicenseKeyAuth } from '../../license-auth';

type RouteParams = {
  id: string;
};

const getHandler = async (
  _request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) => {
  const { id } = await params;

  const organization = await database.organization.findUnique({
    where: {
      id,
    },
    select: {
      id: true,
      name: true,
      slug: true,
      logo: true,
      premiumTier: true,
      createdAt: true,
      members: {
        select: {
          id: true,
          role: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  });

  if (!organization) {
    return NextResponse.json(
      { error: 'Organization not found' },
      { status: 404 }
    );
  }

  return NextResponse.json(
    { data: serializePrisma(organization) },
    { status: 200 }
  );
};

const putHandler = async (
  request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) => {
  const { id } = await params;
  const body = await request.json();

  const organization = await database.organization.update({
    where: {
      id,
    },
    data: body,
    select: {
      id: true,
      name: true,
      slug: true,
      logo: true,
      premiumTier: true,
    },
  });

  return NextResponse.json(
    { data: serializePrisma(organization) },
    { status: 200 }
  );
};

export const OPTIONS = withLicenseKeyAuth(getHandler);
export const GET = withLicenseKeyAuth(getHandler);
export const PUT = withLicenseKeyAuth(putHandler);
