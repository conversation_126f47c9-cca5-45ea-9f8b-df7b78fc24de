import { fetchWithAuthForProxy, withApi<PERSON>eyAuth } from '@/app/lib/auth';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { type NextRequest, NextResponse } from 'next/server';

const getHandler = async (request: NextRequest) => {
  const searchParams = Object.fromEntries(
    request.nextUrl.searchParams.entries()
  );

  const targetUrl =
    Object.keys(searchParams).length > 0
      ? urlSerialize('/api/v1/consolidated-invoices', searchParams)
      : '/api/v1/consolidated-invoices';

  const { data, status } = await fetchWithAuthForProxy(
    targetUrl,
    {},
    request.headers
  );
  
  return NextResponse.json(data, {
    status,
  });
};

export const OPTIONS = withApiKeyAuth(getHandler);
export const GET = withApiKeyAuth(getHandler);