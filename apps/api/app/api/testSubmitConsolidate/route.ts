import { fetchWithAuthForProxy, withApi<PERSON>eyAuth } from '@/app/lib/auth';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { type NextRequest, NextResponse } from 'next/server';
const postHandler = async (request: NextRequest) => {
  const { data, status } = await fetchWithAuthForProxy(
    '/testSubmitConsolidate',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    },
    request.headers
  );

  return NextResponse.json(data, {
    status,
  });
};

export const OPTIONS = withApiKeyAuth(postHandler);
export const POST = withApiKeyAuth(postHandler);
