import { withApi<PERSON><PERSON><PERSON>uth } from '@/app/lib/auth';
import { log } from '@repo/observability/log';
import { put } from '@repo/storage';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

async function handler(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'File is required' }, { status: 400 });
    }

    const access =
      (formData.get('access') as 'public' | 'private') || 'private';

    const result = await put(file.name, file, {
      access,
      contentType: file.type,
    });

    return NextResponse.json({
      success: true,
      url: result.url,
      pathname: result.pathname,
    });
  } catch (error) {
    log.warn('Failed to upload file:', { error });

    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}

export const OPTIONS = withA<PERSON><PERSON><PERSON><PERSON>uth(handler);
export const POST = withA<PERSON><PERSON><PERSON><PERSON><PERSON>(handler);
