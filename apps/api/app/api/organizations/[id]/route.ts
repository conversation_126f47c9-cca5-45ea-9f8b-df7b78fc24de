import { withCors } from '@/app/lib/api';
import { database, serializePrisma } from '@repo/database';
import { type NextRequest, NextResponse } from 'next/server';

type RouteParams = {
  id: string;
};

const getHandler = async (
  _request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) => {
  const { id } = await params;

  const organization = await database.organization.findUnique({
    where: {
      id,
    },
    select: {
      id: true,
      name: true,
      slug: true,
      logo: true,
      premiumTier: true,
      createdAt: true,
      members: {
        select: {
          id: true,
          role: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  });

  if (!organization) {
    return NextResponse.json(
      { error: 'Organization not found' },
      { status: 404 }
    );
  }

  return NextResponse.json(
    { data: serializePrisma(organization) },
    { status: 200 }
  );
};

const putHandler = async (
  request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) => {
  const { id } = await params;
  const body = await request.json();

  const organization = await database.organization.update({
    where: {
      id,
    },
    data: body,
    select: {
      id: true,
      name: true,
      slug: true,
      logo: true,
      premiumTier: true,
    },
  });

  return NextResponse.json(
    { data: serializePrisma(organization) },
    { status: 200 }
  );
};

export const OPTIONS = withCors(getHandler);
export const GET = withCors(getHandler);
export const PUT = withCors(putHandler);
