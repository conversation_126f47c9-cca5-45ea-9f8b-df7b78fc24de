import { analytics } from '@repo/analytics/posthog/server';
import { database } from '@repo/database';
import { parseError } from '@repo/observability/error';
import { log } from '@repo/observability/log';
import type { ChipPaymentResponse } from '@repo/payments/chip';
import { revalidatePath } from 'next/cache';

/**
 * Handle premium tier upgrade payment
 * @param data Payment data from webhook
 */
export const handlePremiumUpgradePayment = async (
  data: ChipPaymentResponse
) => {
  if (!data.id) {
    log.warn('Premium upgrade payment missing ID');
    return;
  }

  try {
    // Find the premium upgrade record using the Chip payment ID as transaction ID
    const premiumUpgrade = await database.premiumUpgrade.findFirst({
      where: {
        transactionId: data.id, // The transactionId field stores the Chip payment ID
      },
      include: {
        organization: true,
        premiumTier: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (!premiumUpgrade) {
      log.warn(`Premium upgrade not found for payment ID: ${data.id}`);
      return;
    }

    // Update the premium upgrade status
    await database.premiumUpgrade.update({
      where: {
        id: premiumUpgrade.id,
      },
      data: {
        status: 'completed',
        paymentStatus: 'paid',
      },
    });

    // Update the organization to mark it as premium
    await database.organization.update({
      where: {
        id: premiumUpgrade.organizationId,
      },
      data: {
        premiumTierId: premiumUpgrade.premiumTierId,
      },
    });

    log.info(
      `Organization ${premiumUpgrade.organizationId} upgraded to premium tier ${premiumUpgrade.premiumTierId}`
    );

    // Revalidate the organization page
    if (premiumUpgrade.organization?.slug) {
      revalidatePath(`/organizations/${premiumUpgrade.organization.slug}`);
    }
    revalidatePath('/organizations');
    revalidatePath('/admin/premium-tiers');

    // Track the event in analytics
    analytics.capture({
      event: 'organization_upgraded_to_premium',
      distinctId: premiumUpgrade.id,
      properties: {
        organizationId: premiumUpgrade.organizationId,
        premiumTierId: premiumUpgrade.premiumTierId,
        paymentMethod: 'chip',
        amount: premiumUpgrade.amount,
      },
    });
  } catch (error) {
    const message = parseError(error);
    log.warn(`Error processing premium upgrade payment: ${message}`, {
      paymentId: data.id,
      error,
    });
  }
};

/**
 * Handle premium tier upgrade payment failed
 * @param data Payment data from webhook
 */
export const handlePremiumUpgradeFailed = async (data: ChipPaymentResponse) => {
  if (!data.id) {
    log.warn('Premium upgrade payment missing ID');
    return;
  }

  try {
    // Find the premium upgrade record using the Chip payment ID as transaction ID
    const premiumUpgrade = await database.premiumUpgrade.findFirst({
      where: {
        transactionId: data.id,
      },
      include: {
        organization: true,
        premiumTier: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (!premiumUpgrade) {
      log.warn(`Premium upgrade not found for failed payment ID: ${data.id}`);
      return;
    }

    // Update the premium upgrade status to failed
    await database.premiumUpgrade.update({
      where: {
        id: premiumUpgrade.id,
      },
      data: {
        status: 'failed',
        paymentStatus: 'pending',
      },
    });

    log.info(
      `Premium upgrade payment failed for organization ${premiumUpgrade.organizationId}`
    );

    // Revalidate the organization page
    if (premiumUpgrade.organization?.slug) {
      revalidatePath(`/organizations/${premiumUpgrade.organization.slug}`);
    }

    // Track the organization in analytics
    analytics.capture({
      event: 'organization_premium_upgrade_failed',
      distinctId: premiumUpgrade.id,
      properties: {
        organizationId: premiumUpgrade.organizationId,
        premiumTierId: premiumUpgrade.premiumTierId,
        paymentMethod: 'chip',
        amount: premiumUpgrade.amount,
        paymentId: data.id,
      },
    });
  } catch (error) {
    const message = parseError(error);
    log.warn(`Error processing failed premium upgrade payment: ${message}`, {
      paymentId: data.id,
      error,
    });
  }
};

/**
 * Handle premium tier upgrade payment cancelled
 * @param data Payment data from webhook
 */
export const handlePremiumUpgradeCancelled = async (
  data: ChipPaymentResponse
) => {
  if (!data.id) {
    log.warn('Premium upgrade payment missing ID');
    return;
  }

  try {
    // Find the premium upgrade record using the Chip payment ID as transaction ID
    const premiumUpgrade = await database.premiumUpgrade.findFirst({
      where: {
        transactionId: data.id,
      },
      include: {
        organization: true,
        premiumTier: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    if (!premiumUpgrade) {
      log.warn(
        `Premium upgrade not found for cancelled payment ID: ${data.id}`
      );
      return;
    }

    // Update the premium upgrade status to cancelled
    await database.premiumUpgrade.update({
      where: {
        id: premiumUpgrade.id,
      },
      data: {
        status: 'cancelled',
        paymentStatus: 'cancelled',
      },
    });

    log.info(
      `Premium upgrade payment cancelled for organization ${premiumUpgrade.organizationId}`
    );

    // Track the event in analytics
    analytics.capture({
      event: 'organization_premium_upgrade_cancelled',
      distinctId: premiumUpgrade.id,
      properties: {
        organizationId: premiumUpgrade.organizationId,
        premiumTierId: premiumUpgrade.premiumTierId,
        paymentMethod: 'chip',
        amount: premiumUpgrade.amount,
        paymentId: data.id,
      },
    });
  } catch (error) {
    const message = parseError(error);
    log.warn(`Error processing cancelled premium upgrade payment: ${message}`, {
      paymentId: data.id,
      error,
    });
  }
};
