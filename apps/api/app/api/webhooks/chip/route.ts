import { parseError } from '@repo/observability/error';
import { log } from '@repo/observability/log';
import { chip } from '@repo/payments';
import type { ChipPaymentResponse } from '@repo/payments/chip';
import { NextResponse } from 'next/server';
import {
  handlePremiumUpgradeCancelled,
  handlePremiumUpgradeFailed,
  handlePremiumUpgradePayment,
} from './(premium_tier)/handler';

/**
 * Handle payment completed event
 * @param data Payment data from webhook
 */
const handlePaymentCompleted = async (data: ChipPaymentResponse) => {
  if (!data.id) {
    log.warn('Payment completed event missing ID');
    return;
  }

  const product = data.purchase?.products?.[0];

  // determine the handler to use
  switch (product.category) {
    // Handle premium tier upgrade payment
    case 'premium_tier':
      await handlePremiumUpgradePayment(data);
      break;

    default:
      break;
  }
};

/**
 * Handle payment failed event
 * @param data Payment data from webhook
 */
const handlePaymentFailed = async (data: ChipPaymentResponse) => {
  if (!data.id) {
    log.warn('Payment failed event missing ID');
    return;
  }

  const product = data.purchase?.products?.[0];

  // determine the handler to use
  switch (product.category) {
    // Handle premium tier upgrade payment failed
    case 'premium_tier':
      await handlePremiumUpgradeFailed(data);
      break;

    default:
      break;
  }
};

/**
 * Handle payment canceled event
 * @param data Payment data from webhook
 */
const handlePaymentCanceled = async (data: ChipPaymentResponse) => {
  if (!data.id) {
    log.warn('Payment canceled event missing ID');
    return;
  }
  log.warn('paymentcancelled');

  const product = data.purchase?.products?.[0];

  // determine the handler to use
  switch (product.category) {
    // Handle premium tier upgrade payment cancelled
    case 'premium_tier':
      await handlePremiumUpgradeCancelled(data);
      break;

    default:
      break;
  }
};

/**
 * Webhook handler for Chip payment gateway
 * @param request Request object
 * @returns Response object
 */
export async function POST(request: Request) {
  try {
    // Get the request body
    const body = await request.text();

    // Get the signature from the headers
    const headersList = request.headers;
    const signature = headersList.get('x-signature') || '';

    // Verify the signature
    if (!signature) {
      log.warn('Missing signature in webhook request');
      return NextResponse.json({ error: 'Missing signature' }, { status: 401 });
    }

    // Verify the signature with the Chip API key
    const isValid = chip.verifyWebhookSignature(body, signature);

    if (!isValid) {
      log.warn('Invalid signature in webhook request');
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
    }

    // Process the webhook based on the event type or payment status
    const data = JSON.parse(body) as ChipPaymentResponse;

    log.info(`Received Chip webhook with status: ${data.status}`, {
      paymentId: data.id,
      status: data.status,
    });

    // Handle based on payment status
    switch (data.status) {
      case 'paid':
        await handlePaymentCompleted(data);
        break;
      case 'error':
      case 'failed':
        await handlePaymentFailed(data);
        break;
      case 'canceled':
        await handlePaymentCanceled(data);
        break;
      case 'created':
      case 'pending':
      case 'viewed':
        // These statuses don't require any action
        log.info(
          `Received Chip event with status: ${data.status} for payment ID: ${data.id}`
        );
        break;
      default:
        log.info(`Unhandled webhook status: ${data.status}`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    const message = parseError(error);
    log.warn(`Error processing webhook: ${message}`, { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
