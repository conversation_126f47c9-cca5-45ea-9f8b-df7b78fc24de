import { withShopifyWebhookAuth } from '@/app/lib/shopify-webhook-auth';
import {
  validateShopifyInstallPayload,
  extractMerchantData,
  extractStoreData,
} from '@/app/lib/shopify-webhook-schemas';
import { createMerchantAccount } from '@/app/lib/shopify-integration-service';
import { WebhookResponseBuilder } from '@/app/lib/shopify-webhook-responses';
import { log } from '@repo/observability/log';
import { parseError } from '@repo/observability/error';

/**
 * Simplified Shopify Install Webhook Handler
 * 
 * This endpoint serves as a clean integration point between the Shopify plugin domain
 * and the core SaaS domain. It handles merchant account creation with minimal complexity.
 * 
 * DOMAIN SEPARATION: This webhook respects domain boundaries by using the integration
 * service layer instead of directly manipulating core domain models.
 */

/**
 * Generate a correlation ID for tracking requests
 */
function generateCorrelationId(): string {
  return `shopify-install-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Simplified webhook handler function
 */
async function handleShopifyInstallWebhook(
  _request: Request,
  body: string
): Promise<Response> {
  const correlationId = generateCorrelationId();
  const startTime = Date.now();
  const responseBuilder = new WebhookResponseBuilder(correlationId)
    .withOperation('shopify-install')
    .withStartTime(startTime);

  try {
    log.info('Shopify install webhook received', {
      correlationId,
      contentLength: body.length,
    });

    // Parse and validate the payload
    let payload: unknown;
    try {
      payload = JSON.parse(body);
    } catch (error) {
      return responseBuilder.validationError(['Invalid JSON payload']);
    }

    // Validate payload structure
    const validation = validateShopifyInstallPayload(payload);
    if (!validation.success) {
      return responseBuilder.validationError(
        validation.errors || ['Payload validation failed']
      );
    }

    const webhookData = validation.data;
    if (!webhookData) {
      return responseBuilder.validationError(['No valid data in payload']);
    }

    responseBuilder.withShop(webhookData.shop);

    log.info('Processing Shopify install for shop', {
      shop: webhookData.shop,
      correlationId,
    });

    // Extract merchant and store data
    const merchantDataResult = extractMerchantData(
      webhookData.session,
      webhookData.storeInfo
    );

    if (!merchantDataResult.success || !merchantDataResult.data) {
      const errors = merchantDataResult.errors || ['Failed to extract merchant data'];
      return responseBuilder.validationError(errors);
    }

    const storeDataResult = extractStoreData(
      webhookData.shop,
      webhookData.shopData,
      webhookData.storeInfo
    );

    if (!storeDataResult.success || !storeDataResult.data) {
      const errors = storeDataResult.errors || ['Failed to extract store data'];
      return responseBuilder.validationError(errors);
    }

    // Create merchant account using integration service
    const result = await createMerchantAccount(
      webhookData.shop,
      webhookData.accessToken,
      merchantDataResult.data,
      storeDataResult.data,
      correlationId
    );

    log.info('Shopify install webhook completed', {
      shop: webhookData.shop,
      success: result.success,
      userId: result.userId,
      organizationId: result.organizationId,
      correlationId,
    });

    if (result.success) {
      return responseBuilder.success(
        {
          userId: result.userId,
          organizationId: result.organizationId,
          memberId: result.memberId,
          syncStateId: result.syncStateId,
          isExisting: result.isExisting,
        },
        result.warnings
      );
    }

    return responseBuilder.processingError(
      'Merchant account creation failed',
      result.errors || ['Unknown error'],
      false
    );
  } catch (error) {
    const message = parseError(error);

    log.error('Unexpected error in Shopify install webhook', {
      error: message,
      correlationId,
    });

    return responseBuilder.processingError(
      'An unexpected error occurred while processing the webhook',
      message,
      true
    );
  }
}

// Export the POST handler with authentication wrapper
export const POST = withShopifyWebhookAuth(handleShopifyInstallWebhook);
