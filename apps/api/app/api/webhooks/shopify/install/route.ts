import { withShopifyWebhookAuth } from '@/app/lib/shopify-webhook-auth';
import {
  validateShopifyInstallPayload,
  extractMerchantData,
  extractStoreData,
  validateUserCreationData,
  validateOrganizationCreationData,
} from '@/app/lib/shopify-webhook-schemas';
import {
  createUserFromShopifyData,
  type ShopifyMerchantData,
} from '@/app/lib/shopify-user-service';
import {
  createOrganizationFromShopifyData,
  type ShopifyStoreData,
} from '@/app/lib/shopify-organization-service';
import { linkUserToOrganization } from '@/app/lib/shopify-member-service';
import {
  upsertMerchantSyncState,
  markSyncCompleted,
  markSyncFailed,
  SYNC_STATUS,
} from '@/app/lib/shopify-sync-state-service';
import { WebhookResponseBuilder } from '@/app/lib/shopify-webhook-responses';
import { log } from '@repo/observability/log';
import { parseError } from '@repo/observability/error';

/**
 * Shopify Install Webhook Handler
 *
 * This endpoint handles webhook requests from the Shopify Remix app when a new merchant
 * installs the app. It creates the necessary user account, organization, and member
 * relationships in the backend system.
 */

interface OnboardingResult {
  success: boolean;
  userId?: string;
  organizationId?: string;
  memberId?: string;
  errors: string[];
  warnings: string[];
  correlationId: string;
}

/**
 * Generate a correlation ID for tracking requests
 */
function generateCorrelationId(): string {
  return `shopify-install-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Main webhook handler function
 */
async function handleShopifyInstallWebhook(
  _request: Request,
  body: string
): Promise<Response> {
  const correlationId = generateCorrelationId();
  const startTime = Date.now();
  const responseBuilder = new WebhookResponseBuilder(correlationId)
    .withOperation('shopify-install')
    .withStartTime(startTime);

  try {
    log.info('Shopify install webhook received', {
      correlationId,
      contentLength: body.length,
    });

    // Parse and validate the payload
    let payload: unknown;
    try {
      payload = JSON.parse(body);
    } catch (_error) {
      return responseBuilder.validationError(['Invalid JSON payload']);
    }

    // Validate payload structure
    const validation = validateShopifyInstallPayload(payload);
    if (!validation.success) {
      return responseBuilder.validationError(
        validation.errors || ['Payload validation failed']
      );
    }

    const webhookData = validation.data;
    if (!webhookData) {
      return responseBuilder.validationError(['No valid data in payload']);
    }

    responseBuilder.withShop(webhookData.shop);

    log.info('Processing Shopify install for shop', {
      shop: webhookData.shop,
      correlationId,
    });

    // Initialize sync state
    await upsertMerchantSyncState(
      {
        shopId: webhookData.shop,
        accessToken: webhookData.accessToken,
        syncStatus: SYNC_STATUS.SYNCING,
      },
      correlationId
    );

    // Extract and validate merchant data
    const merchantDataResult = extractMerchantData(
      webhookData.session,
      webhookData.storeInfo
    );

    if (!merchantDataResult.success || !merchantDataResult.data) {
      const errors = merchantDataResult.errors || [
        'Failed to extract merchant data',
      ];
      await markSyncFailed(webhookData.shop, errors, correlationId);
      return responseBuilder.validationError(errors);
    }

    const merchantData = merchantDataResult.data;

    // Validate user creation data
    const userValidation = validateUserCreationData(merchantData);
    if (!userValidation.success) {
      const errors = userValidation.errors || ['Invalid user data'];
      await markSyncFailed(webhookData.shop, errors, correlationId);
      return responseBuilder.validationError(errors);
    }

    // Extract and validate store data
    const storeDataResult = extractStoreData(
      webhookData.shop,
      webhookData.shopData,
      webhookData.storeInfo
    );

    if (!storeDataResult.success || !storeDataResult.data) {
      const errors = storeDataResult.errors || ['Failed to extract store data'];
      await markSyncFailed(webhookData.shop, errors, correlationId);
      return responseBuilder.validationError(errors);
    }

    const storeData = storeDataResult.data;

    // Validate organization creation data
    const orgValidation = validateOrganizationCreationData(storeData);
    if (!orgValidation.success) {
      const errors = orgValidation.errors || ['Invalid organization data'];
      await markSyncFailed(webhookData.shop, errors, correlationId);
      return responseBuilder.validationError(errors);
    }

    // Process the onboarding
    const result = await processShopifyOnboarding(
      webhookData.shop,
      webhookData.accessToken,
      merchantData,
      storeData,
      correlationId
    );

    log.info('Shopify install webhook completed', {
      shop: webhookData.shop,
      success: result.success,
      userId: result.userId,
      organizationId: result.organizationId,
      correlationId,
    });

    if (result.success) {
      return responseBuilder.success(
        {
          userId: result.userId,
          organizationId: result.organizationId,
          memberId: result.memberId,
        },
        result.warnings
      );
    }

    return responseBuilder.processingError(
      'Onboarding failed',
      result.errors,
      false
    );
  } catch (error) {
    const message = parseError(error);

    log.error('Unexpected error in Shopify install webhook', {
      error: message,
      correlationId,
    });

    return responseBuilder.processingError(
      'An unexpected error occurred while processing the webhook',
      message,
      true
    );
  }
}

/**
 * Process the complete Shopify onboarding flow
 */
async function processShopifyOnboarding(
  shop: string,
  _accessToken: string,
  merchantData: ShopifyMerchantData,
  storeData: ShopifyStoreData,
  correlationId: string
): Promise<OnboardingResult> {
  const result: OnboardingResult = {
    success: false,
    errors: [],
    warnings: [],
    correlationId,
  };

  try {
    // Step 1: Create or update user
    const userResult = await createUserFromShopifyData(
      merchantData,
      correlationId
    );

    if (!userResult.success) {
      result.errors.push(...(userResult.errors || ['User creation failed']));
      await markSyncFailed(shop, result.errors, correlationId);
      return result;
    }

    result.userId = userResult.userId;
    result.warnings.push(...(userResult.warnings || []));

    // Step 2: Create or update organization
    const orgResult = await createOrganizationFromShopifyData(
      storeData,
      correlationId
    );

    if (!orgResult.success) {
      result.errors.push(
        ...(orgResult.errors || ['Organization creation failed'])
      );
      await markSyncFailed(shop, result.errors, correlationId);
      return result;
    }

    result.organizationId = orgResult.organizationId;
    result.warnings.push(...(orgResult.warnings || []));

    // Step 3: Link user to organization
    if (!result.userId || !result.organizationId) {
      result.errors.push(
        'Missing user ID or organization ID for member linking'
      );
      await markSyncFailed(shop, result.errors, correlationId);
      return result;
    }

    const memberResult = await linkUserToOrganization(
      result.userId,
      result.organizationId,
      'owner', // Shopify merchants are owners of their organizations
      correlationId
    );

    if (!memberResult.success) {
      result.errors.push(...(memberResult.errors || ['Member linking failed']));
      await markSyncFailed(shop, result.errors, correlationId);
      return result;
    }

    result.memberId = memberResult.memberId;
    result.warnings.push(...(memberResult.warnings || []));

    // Step 4: Mark sync as completed
    await markSyncCompleted(
      shop,
      result.userId,
      result.organizationId,
      correlationId
    );

    result.success = true;
    return result;
  } catch (error) {
    const message = parseError(error);
    result.errors.push(message);
    await markSyncFailed(shop, result.errors, correlationId);
    return result;
  }
}

// Export the POST handler with authentication wrapper
export const POST = withShopifyWebhookAuth(handleShopifyInstallWebhook);
