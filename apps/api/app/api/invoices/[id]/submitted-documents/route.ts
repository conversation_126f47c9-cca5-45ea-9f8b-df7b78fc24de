import { fetchWithAuthForProxy, withApi<PERSON>eyAuth } from '@/app/lib/auth';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { type NextRequest, NextResponse } from 'next/server';

const getHandler = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  const { id } = await context.params;
  const searchParams = Object.fromEntries(
    request.nextUrl.searchParams.entries()
  );

  const baseUrl = `/api/v1/invoices/${id}/submitted-documents`;
  const targetUrl =
    Object.keys(searchParams).length > 0
      ? urlSerialize(baseUrl, searchParams)
      : baseUrl;

  const { data, status } = await fetchWithAuthForProxy(
    targetUrl,
    {},
    request.headers
  );

  return NextResponse.json(data, {
    status,
  });
};

export const OPTIONS = withApiKeyAuth(getHandler);
export const GET = withApiKeyAuth(getHandler);

