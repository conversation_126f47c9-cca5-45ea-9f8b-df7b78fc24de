import { fetchWithAuthForProxy, withApi<PERSON>eyAuth } from '@/app/lib/auth';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { type NextRequest, NextResponse } from 'next/server';

const getHandler = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  const { id } = await context.params;
  const searchParams = Object.fromEntries(
    request.nextUrl.searchParams.entries()
  );

  const baseUrl = `/api/v1/invoices/${id}`;
  const targetUrl =
    Object.keys(searchParams).length > 0
      ? urlSerialize(baseUrl, searchParams)
      : baseUrl;

  const { data, status } = await fetchWithAuthForProxy(
    targetUrl,
    {},
    request.headers
  );

  return NextResponse.json(data, {
    status,
  });
};

const putHandler = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  const { id } = await context.params;
  let body: unknown;
  try {
    body = await request.json();
  } catch (_) {
    return NextResponse.json(
      { error: 'Failed to parse request body' },
      { status: 400 }
    );
  }

  const { data, status } = await fetchWithAuthForProxy(
    `/api/v1/invoices/${id}`,
    {
      method: 'PUT',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    },
    request.headers
  );

  return NextResponse.json(data, {
    status,
  });
};

const deleteHandler = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  const { id } = await context.params;
  // DELETE might or might not have a body
  const fetchOptions: RequestInit = {
    method: 'DELETE',
  };

  const contentType = request.headers.get('content-type');
  if (contentType) {
    const body = await request.json();
    fetchOptions.body = JSON.stringify(body);
    fetchOptions.headers = {
      'Content-Type': 'application/json',
    };
  }

  const { data, status } = await fetchWithAuthForProxy(
    `/api/v1/invoices/${id}`,
    fetchOptions,
    request.headers
  );

  return NextResponse.json(data, {
    status,
  });
};

export const OPTIONS = withApiKeyAuth(getHandler);
export const GET = withApiKeyAuth(getHandler);
export const PUT = withApiKeyAuth(putHandler);
export const DELETE = withApiKeyAuth(deleteHandler);
