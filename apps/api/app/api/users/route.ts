import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/app/lib/auth';
import { database, serializePrisma } from '@repo/database';
import { type NextRequest, NextResponse } from 'next/server';

const getHandler = async (request: NextRequest) => {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const page = Number.parseInt(searchParams.get('page') || '1', 10);
  const perPage = Number.parseInt(searchParams.get('per_page') || '10', 10);
  const skip = (page - 1) * perPage;

  const [totalCount, users] = await Promise.all([
    database.user.count({
      where: {
        OR: [
          {
            firstName: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
          {
            lastName: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
          {
            email: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
          {
            phone: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
        ],
      },
    }),
    database.user.findMany({
      where: {
        OR: [
          {
            firstName: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
          {
            lastName: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
          {
            email: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
          {
            phone: {
              contains: query ?? '',
              mode: 'insensitive',
            },
          },
        ],
      },
      select: {
        id: true,
        name: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
        members: {
          select: {
            organization: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            role: true,
          },
        },
      },
      skip,
      take: perPage,
    }),
  ]);

  return NextResponse.json(
    {
      data: serializePrisma(users),
      meta: {
        total: totalCount,
        per_page: perPage,
        current_page: page,
        last_page: Math.ceil(totalCount / perPage),
        from: skip + 1,
        to: skip + users.length,
      },
    },
    {
      status: 200,
    }
  );
};

// TODO: need complete user information to create
const postHandler = async (request: NextRequest) => {
  const body = await request.json();

  const user = await database.user.create({
    data: body,
    select: {
      id: true,
      email: true,
      name: true,
    },
  });

  return NextResponse.json(
    { data: user },
    {
      status: 201,
    }
  );
};

export const OPTIONS = withApiKeyAuth(getHandler);
export const GET = withApiKeyAuth(getHandler);
export const POST = withApiKeyAuth(postHandler);
