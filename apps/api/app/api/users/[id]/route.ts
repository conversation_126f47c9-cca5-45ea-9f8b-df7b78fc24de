import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/app/lib/auth';
import { database, serializePrisma } from '@repo/database';
import { type NextRequest, NextResponse } from 'next/server';

type RouteParams = {
  id: string;
};

const getHandler = async (
  _request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) => {
  const { id } = await params;

  const user = await database.user.findUnique({
    where: {
      id,
    },
    select: {
      id: true,
      name: true,
      firstName: true,
      lastName: true,
      email: true,
      phone: true,
      dob: true,
      role: true,
      members: {
        select: {
          id: true,
          role: true,
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
              premiumTier: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      },
    },
  });

  if (!user) {
    return NextResponse.json({ error: 'User not found' }, { status: 404 });
  }

  return NextResponse.json(
    { data: serializePrisma(user) },
    {
      status: 200,
    }
  );
};

const putHandler = async (
  request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) => {
  const { id } = await params;

  const body = await request.json();

  const user = await database.user.update({
    where: {
      id,
    },
    data: body,
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      phone: true,
      dob: true,
      role: true,
      members: {
        select: {
          id: true,
          role: true,
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
              premiumTier: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      },
    },
  });

  return NextResponse.json(
    { data: serializePrisma(user) },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withApiKeyAuth(getHandler);
export const GET = withApiKeyAuth(getHandler);
export const PUT = withApiKeyAuth(putHandler);
