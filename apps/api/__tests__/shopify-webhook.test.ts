import { describe, it, expect, beforeEach, vi } from 'vitest';
import { NextRequest } from 'next/server';

/**
 * Basic tests for Shopify webhook functionality
 * These tests verify the webhook handles various scenarios correctly
 */

// Mock the dependencies
vi.mock('@repo/database', () => ({
  database: {
    merchantSyncState: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      upsert: vi.fn(),
    },
    user: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    },
    organization: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    },
    member: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
  },
}));

vi.mock('@repo/observability/log', () => ({
  log: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock('@repo/observability/error', () => ({
  parseError: vi.fn((error) => error?.message || 'Unknown error'),
}));

vi.mock('@/env', () => ({
  env: {
    SHOPIFY_WEBHOOK_SECRET: 'test-secret',
  },
}));

// Test data
const validShopifyPayload = {
  shop: 'test-store.myshopify.com',
  accessToken: 'test-access-token',
  session: {
    shop: 'test-store.myshopify.com',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    userId: '12345',
  },
  storeInfo: {
    name: 'Test Store',
    phone: '+1234567890',
    email: '<EMAIL>',
    registrationNumber: 'REG123',
    tinCode: 'TIN456',
  },
  shopData: {
    name: 'Test Store',
    currency: 'USD',
    timezone: 'America/New_York',
    planName: 'basic',
    country: 'US',
  },
};

const invalidPayload = {
  shop: '', // Missing required shop
  // Missing accessToken
  session: {
    shop: 'test-store.myshopify.com',
  },
};

describe('Shopify Webhook Validation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should validate a correct Shopify install payload', async () => {
    const { validateShopifyInstallPayload } = await import('@/app/lib/shopify-webhook-schemas');
    
    const result = validateShopifyInstallPayload(validShopifyPayload);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
    expect(result.data?.shop).toBe('test-store.myshopify.com');
    expect(result.data?.accessToken).toBe('test-access-token');
  });

  it('should reject invalid payload', async () => {
    const { validateShopifyInstallPayload } = await import('@/app/lib/shopify-webhook-schemas');
    
    const result = validateShopifyInstallPayload(invalidPayload);
    
    expect(result.success).toBe(false);
    expect(result.errors).toBeDefined();
    expect(result.errors?.length).toBeGreaterThan(0);
  });

  it('should extract merchant data correctly', async () => {
    const { extractMerchantData } = await import('@/app/lib/shopify-webhook-schemas');
    
    const result = extractMerchantData(
      validShopifyPayload.session,
      validShopifyPayload.storeInfo
    );
    
    expect(result.success).toBe(true);
    expect(result.data?.shop).toBe('test-store.myshopify.com');
    expect(result.data?.email).toBe('<EMAIL>');
    expect(result.data?.name).toBe('John Doe');
  });

  it('should extract store data correctly', async () => {
    const { extractStoreData } = await import('@/app/lib/shopify-webhook-schemas');
    
    const result = extractStoreData(
      validShopifyPayload.shop,
      validShopifyPayload.shopData,
      validShopifyPayload.storeInfo
    );
    
    expect(result.success).toBe(true);
    expect(result.data?.shop).toBe('test-store.myshopify.com');
    expect(result.data?.name).toBe('Test Store');
    expect(result.data?.currency).toBe('USD');
  });
});

describe('Shopify Webhook Authentication', () => {
  it('should verify HMAC signature correctly', async () => {
    const { verifyShopifyWebhookSignature } = await import('@/app/lib/shopify-webhook-auth');
    
    const body = JSON.stringify(validShopifyPayload);
    const secret = 'test-secret';
    
    // Create a valid signature
    const crypto = await import('crypto');
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(body, 'utf8');
    const signature = hmac.digest('base64');
    
    const isValid = verifyShopifyWebhookSignature(body, signature, secret);
    expect(isValid).toBe(true);
  });

  it('should reject invalid HMAC signature', async () => {
    const { verifyShopifyWebhookSignature } = await import('@/app/lib/shopify-webhook-auth');
    
    const body = JSON.stringify(validShopifyPayload);
    const secret = 'test-secret';
    const invalidSignature = 'invalid-signature';
    
    const isValid = verifyShopifyWebhookSignature(body, invalidSignature, secret);
    expect(isValid).toBe(false);
  });

  it('should verify API key correctly', async () => {
    const { verifyShopifyApiKey } = await import('@/app/lib/shopify-webhook-auth');
    
    const request = new Request('http://localhost/webhook', {
      method: 'POST',
      headers: {
        'X-Shopify-Api-Key': 'test-secret',
      },
    });
    
    const isValid = verifyShopifyApiKey(request);
    expect(isValid).toBe(true);
  });

  it('should reject invalid API key', async () => {
    const { verifyShopifyApiKey } = await import('@/app/lib/shopify-webhook-auth');
    
    const request = new Request('http://localhost/webhook', {
      method: 'POST',
      headers: {
        'X-Shopify-Api-Key': 'invalid-key',
      },
    });
    
    const isValid = verifyShopifyApiKey(request);
    expect(isValid).toBe(false);
  });
});

describe('Shopify User Service', () => {
  it('should prepare user data correctly', async () => {
    const { createUserFromShopifyData } = await import('@/app/lib/shopify-user-service');
    const { database } = await import('@repo/database');
    
    // Mock database responses
    vi.mocked(database.merchantSyncState.findUnique).mockResolvedValue(null);
    vi.mocked(database.user.findUnique).mockResolvedValue(null);
    vi.mocked(database.user.create).mockResolvedValue({
      id: 'user-123',
      email: '<EMAIL>',
      name: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      phone: '+1234567890',
    });
    
    const merchantData = {
      shop: 'test-store.myshopify.com',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
      name: 'John Doe',
    };
    
    const result = await createUserFromShopifyData(merchantData, 'test-correlation-id');
    
    expect(result.success).toBe(true);
    expect(result.userId).toBe('user-123');
    expect(result.user?.name).toBe('John Doe');
  });
});

describe('Shopify Organization Service', () => {
  it('should create organization correctly', async () => {
    const { createOrganizationFromShopifyData } = await import('@/app/lib/shopify-organization-service');
    const { database } = await import('@repo/database');
    
    // Mock database responses
    vi.mocked(database.merchantSyncState.findUnique).mockResolvedValue(null);
    vi.mocked(database.organization.findUnique).mockResolvedValue(null);
    vi.mocked(database.organization.create).mockResolvedValue({
      id: 'org-123',
      name: 'Test Store',
      slug: 'test-store-test-store',
    });
    
    const storeData = {
      shop: 'test-store.myshopify.com',
      name: 'Test Store',
      currency: 'USD',
      timezone: 'America/New_York',
      planName: 'basic',
      country: 'US',
    };
    
    const result = await createOrganizationFromShopifyData(storeData, 'test-correlation-id');
    
    expect(result.success).toBe(true);
    expect(result.organizationId).toBe('org-123');
    expect(result.organization?.name).toBe('Test Store');
  });
});

describe('Shopify Member Service', () => {
  it('should link user to organization correctly', async () => {
    const { linkUserToOrganization } = await import('@/app/lib/shopify-member-service');
    const { database } = await import('@repo/database');
    
    // Mock database responses
    vi.mocked(database.member.findFirst).mockResolvedValue(null);
    vi.mocked(database.member.findMany).mockResolvedValue([]);
    vi.mocked(database.member.create).mockResolvedValue({
      id: 'member-123',
      userId: 'user-123',
      organizationId: 'org-123',
      role: 'owner',
    });
    
    const result = await linkUserToOrganization(
      'user-123',
      'org-123',
      'owner',
      'test-correlation-id'
    );
    
    expect(result.success).toBe(true);
    expect(result.memberId).toBe('member-123');
    expect(result.member?.role).toBe('owner');
  });
});

describe('Shopify Sync State Service', () => {
  it('should create sync state correctly', async () => {
    const { createMerchantSyncState, SYNC_STATUS } = await import('@/app/lib/shopify-sync-state-service');
    const { database } = await import('@repo/database');
    
    // Mock database responses
    vi.mocked(database.merchantSyncState.findUnique).mockResolvedValue(null);
    vi.mocked(database.merchantSyncState.create).mockResolvedValue({
      id: 'sync-123',
      shopId: 'test-store.myshopify.com',
      syncStatus: SYNC_STATUS.PENDING,
      backendUserId: null,
      backendOrganizationId: null,
      lastSyncAt: null,
    });
    
    const syncData = {
      shopId: 'test-store.myshopify.com',
      accessToken: 'test-access-token',
      syncStatus: SYNC_STATUS.PENDING,
    };
    
    const result = await createMerchantSyncState(syncData, 'test-correlation-id');
    
    expect(result.success).toBe(true);
    expect(result.syncStateId).toBe('sync-123');
    expect(result.syncState?.shopId).toBe('test-store.myshopify.com');
  });
});
