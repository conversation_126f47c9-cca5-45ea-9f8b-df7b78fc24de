{"name": "web", "private": true, "scripts": {"dev": "next dev -p 3001 --turbopack", "build": "next build", "start": "next start", "analyze": "ANALYZE=true pnpm build", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@arcjet/next": "1.0.0-beta.7", "@radix-ui/react-icons": "^1.3.2", "@repo/design-system": "workspace:*", "@repo/email": "workspace:*", "@repo/feature-flags": "workspace:*", "@repo/internationalization": "workspace:*", "@repo/next-config": "workspace:*", "@repo/observability": "workspace:*", "@repo/rate-limit": "workspace:*", "@repo/security": "workspace:*", "@repo/seo": "workspace:*", "@sentry/nextjs": "^9.22.0", "@t3-oss/env-nextjs": "^0.13.4", "date-fns": "^4.1.0", "fumadocs-core": "^15.4.0", "import-in-the-middle": "^1.13.2", "lucide-react": "^0.511.0", "mdx-bundler": "^10.1.1", "next": "15.3.2", "react": "19.1.0", "react-dom": "19.1.0", "require-in-the-middle": "^7.5.2", "sharp": "^0.34.2", "shiki": "^3.4.2", "zod": "^3.25.28"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "19.1.5", "tailwindcss": "^4.1.7", "typescript": "^5.8.3"}}