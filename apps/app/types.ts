import type {
  getOrganization,
  getOrganizations,
} from './app/(authenticated)/(with-organization)/admin/organizations/actions';
import type { getPremiumTier } from './app/(authenticated)/(with-organization)/admin/premium-tiers/actions';
// Import actions from their respective locations
// These will be updated once we create the action files
import type { getUsers } from './app/(authenticated)/(with-organization)/admin/users/actions';

export type SerializedUsers = Awaited<ReturnType<typeof getUsers>>;
export type SerializedUsersData = SerializedUsers['data'];
export type SerializedUser = SerializedUsersData[number];

export type SerializedOrganizations = Awaited<
  ReturnType<typeof getOrganizations>
>;
export type SerializedOrganizationsData = SerializedOrganizations['data'];
export type SerializedOrganization = Awaited<
  ReturnType<typeof getOrganization>
>;

export type SerializedPremiumTier = Awaited<ReturnType<typeof getPremiumTier>>;
