import { log } from '@repo/observability/log';
import { useState } from 'react';
import { ApiEndpoint } from '../lib/api';
import { clientFetchWithAuth } from '../lib/api.client';
import { useJwtToken } from '../utils/auth-client-helpers';

export function useUpload() {
  const { bearerToken } = useJwtToken();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const uploadFile = async (
    file: File,
    access: 'public' | 'private' = 'public'
  ) => {
    if (!bearerToken) {
      setError(new Error('Authentication required'));
      return;
    }

    setIsLoading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('access', access);

      const response = await clientFetchWithAuth<{
        success: boolean;
        url: string;
        pathname: string;
      }>(ApiEndpoint.UPLOAD, bearerToken, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response;
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to create order');
      log.warn('Failed to upload file', { error });
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    uploadFile,
    isLoading,
    error,
  };
}
