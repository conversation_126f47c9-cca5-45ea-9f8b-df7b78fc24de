'use client';
/**
 * SWR Hooks for API Requests
 *
 * These hooks use a simplified API pattern:
 *   useApiSWR(urlSerialize(path, params), options)
 *
 * Always use urlSerialize() to add query parameters to the URL path.
 * This approach makes the API more consistent and avoids issues with
 * parameters not being properly applied.
 */
import { log } from '@repo/observability/log';
import type { ReactNode } from 'react';
import useSWR, {
  SWRConfig,
  type SWRConfiguration,
  type SWRResponse,
} from 'swr';
import { JwtProvider, useJwtToken } from '../utils/auth-client-helpers';
import { ApiType } from './api';
import { type FetcherOptions, multiFetcher } from './api.client';

/**
 * Options for the useSWR hook
 */
export interface UseApiSWROptions<Data = unknown, Error = unknown>
  extends Omit<SWRConfiguration<Data, Error>, 'fetcher'> {
  apiType?: ApiType;
  fetchOptions?: Omit<RequestInit, 'body'>;
}

/**
 * Custom hook for using SWR with automatic API endpoint detection
 *
 * @param path The resource path to fetch (use urlSerialize to add query parameters)
 * @param options SWR configuration options
 * @returns SWR response
 */
export function useApiSWR<Data = unknown, Error = unknown>(
  path: string | null,
  options: UseApiSWROptions<Data, Error> = {}
): SWRResponse<Data, Error> {
  const { jwt, bearerToken, isLoading: isJwtLoading } = useJwtToken();
  const { apiType, fetchOptions, ...swrOptions } = options;

  // Create the fetcher function
  const fetcher = (resource: string) => {
    // Create fetcher options with all the necessary parameters
    const fetcherOptions: FetcherOptions = {
      ...fetchOptions,
      apiType,
    };

    if (bearerToken) {
      fetcherOptions.bearerToken = bearerToken;
    }

    if (jwt) {
      fetcherOptions.jwt = jwt;
    }

    // Use the multiFetcher directly
    return multiFetcher<Data>(resource, fetcherOptions);
  };

  // Use SWR with the custom fetcher options
  return useSWR<Data, Error>(
    // Only fetch if we have a path and if we need auth, we have a JWT or don't need one
    !path || isJwtLoading ? null : path,
    {
      ...swrOptions,
      fetcher,
    }
  );
}

/**
 * Custom hook for using SWR with the license API
 */
export function useLicenseApiSWR<Data = unknown, Error = unknown>(
  path: string | null,
  options: Omit<UseApiSWROptions<Data, Error>, 'apiType'> = {}
) {
  return useApiSWR<Data, Error>(path, {
    ...options,
    apiType: ApiType.LICENSE,
  });
}

/**
 * Custom hook for using SWR with the core API
 */
export function useCoreApiSWR<Data = unknown, Error = unknown>(
  path: string | null,
  options: Omit<UseApiSWROptions<Data, Error>, 'apiType'> = {}
) {
  return useApiSWR<Data, Error>(path, {
    ...options,
    apiType: ApiType.CORE,
  });
}

/**
 * SWR Provider component that configures SWR for the application
 */
export function SWRProvider({ children }: { children: ReactNode }) {
  return (
    <SWRConfig
      value={{
        fetcher: (resource, init) => {
          // Use the enhanced multiFetcher with the provided options
          return multiFetcher(resource, init);
        },
        onError: (error) => {
          log.warn('SWR Error:', { error });
        },
      }}
    >
      <JwtProvider>{children}</JwtProvider>
    </SWRConfig>
  );
}

// Alias for useApiSWR for backward compatibility with existing code
export const useMultiApiSWR = useApiSWR;
