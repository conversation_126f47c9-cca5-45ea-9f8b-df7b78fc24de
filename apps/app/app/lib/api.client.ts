'use client';

import { urlSerialize } from '@repo/design-system/lib/utils';
import { log } from '@repo/observability/log';
import { createApiError } from '../utils/error-utils';
import {
  ApiType,
  getApiTypeFromPath,
  getBaseUrl,
  fetchWithAuth as sharedFetchWithAuth,
} from './api';

// Interface for the fetcher options - client-side specific
export interface FetcherOptions extends RequestInit {
  apiType?: ApiType;
  bearerToken?: string;
  jwt?: string;
  params?: Record<string, unknown>;
}

/**
 * Creates a full URL with query parameters
 * @param path Base path
 * @param params Query parameters
 * @returns Full URL with query parameters
 */
export const createUrlWithParams = (
  path: string,
  params?: Record<string, unknown>
): string => {
  if (!params || Object.keys(params).length === 0) {
    return path;
  }
  return urlSerialize(path, params);
};

/**
 * Custom fetcher function that supports multiple base URLs
 * @param resource The resource path to fetch
 * @param options Request options including apiType, jwt, and params
 * @returns The JSON response from the API
 */
export const multiFetcher = async <T>(
  resource: string,
  options?: FetcherOptions
): Promise<T> => {
  // Process params if provided
  const processedResource = options?.params
    ? createUrlWithParams(resource, options.params)
    : resource;

  // Determine API type from the resource path if not explicitly provided
  const apiType = options?.apiType || getApiTypeFromPath(processedResource);
  const baseUrl = getBaseUrl(apiType);

  // Remove apiType, jwt, and params from options before passing to fetch
  const {
    apiType: _,
    bearerToken,
    jwt,
    params: __,
    ...fetchOptions
  } = options || {};

  const token = apiType === ApiType.LICENSE ? bearerToken : jwt;

  // Add bearerToken/JWT to headers if provided
  if (token) {
    if (fetchOptions.headers) {
      // @ts-expect-error: content-type not in types
      const contentType = fetchOptions.headers?.['Content-Type'];

      // remove content-type if it's multipart/form-data because of nextjs bug
      if (contentType === 'multipart/form-data') {
        // @ts-expect-error: content-type not in types
        // biome-ignore lint/performance/noDelete: <explanation>
        delete fetchOptions.headers['Content-Type'];
      } else {
        // @ts-expect-error: content-type not in types
        fetchOptions.headers['Content-Type'] = 'application/json';
      }
    }

    fetchOptions.headers = {
      ...fetchOptions.headers,
      Authorization: `Bearer ${token}`,
    };
  }

  // Determine if the resource already includes the base URL
  const url = processedResource.startsWith('http')
    ? processedResource
    : baseUrl + processedResource;

  try {
    const response = await fetch(url, fetchOptions);

    // Handle error responses
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      log.warn('Fetch error', { status: response.status, errorData });

      // Create a standardized API error with the original error data
      // The actual error message extraction will be handled by the error-utils
      throw createApiError(
        `An error occurred while fetching the data: ${response.status}`,
        errorData,
        response.status
      );
    }

    return await response.json();
  } catch (error) {
    log.warn('Fetch exception', { error, url });
    throw error;
  }
};

/**
 * Fetches data from the specified API with JWT authentication
 * Re-export from shared implementation
 */
export const fetchWithAuth = sharedFetchWithAuth;

/**
 * Client-side fetcher with JWT authentication
 * @param resource The resource path to fetch
 * @param jwt JWT token for authentication
 * @param options Request options
 * @returns The JSON response from the API
 */
export const clientFetchWithAuth = <T>(
  resource: string,
  jwt: string,
  options: Omit<FetcherOptions, 'jwt'> = {}
): Promise<T> => {
  return multiFetcher<T>(resource, {
    ...options,
    jwt,
  });
};
