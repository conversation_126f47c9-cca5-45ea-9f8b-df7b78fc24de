import {
  PageActions,
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import { Header } from '../../components/header';
import { adminGetPremiumTiers } from './actions';
import { PremiumTierCard } from './components/premium-tier-card';
import { PremiumTierDialog } from './components/premium-tier-dialog';

export default async function PremiumTiersPage() {
  const tiers = await adminGetPremiumTiers();

  return (
    <>
      <Header page="Premium Tiers" />

      <main className="flex-1 space-y-8 px-4 lg:px-8">
        <PageHeader>
          <div>
            <PageHeaderHeading>Premium Tiers</PageHeaderHeading>
            <PageHeaderDescription>
              Manage invoice capacity limits for different premium tiers
            </PageHeaderDescription>
          </div>
          <PageActions>
            <PremiumTierDialog />
          </PageActions>
        </PageHeader>

        <div className="grid auto-rows-max gap-4 lg:grid-cols-3">
          {tiers.map((tier) => (
            <PremiumTierCard key={tier.id} tier={tier} />
          ))}

          {tiers.length === 0 && (
            <div className="col-span-full flex h-40 items-center justify-center rounded-lg border border-dashed">
              <div className="text-center">
                <p className="text-muted-foreground">No premium tiers found</p>
              </div>
            </div>
          )}
        </div>
      </main>
    </>
  );
}
