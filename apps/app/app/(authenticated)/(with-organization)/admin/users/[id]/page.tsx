import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@repo/design-system/components/ui/card';
import {
  PageHeader,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from '@repo/design-system/components/ui/tabs';
import type { Metadata } from 'next';
import { title } from 'radash';
import type { ReactElement } from 'react';
import { Header } from '../../../components/header';
import { getUser } from '../actions';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const user = await getUser(id);

  const title = 'MyInvoice - User';
  const description = `Details for ${user.firstName} ${user.lastName}`;

  return {
    title,
    description,
  };
}

export default async function UserDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const user = await getUser(id);

  return (
    <>
      <Header pages={['Users']} page={user.id} />

      <main className="flex-1 space-y-4 px-4 lg:px-8">
        <Tabs defaultValue="summary">
          <PageHeader>
            <div className="space-y-2">
              <PageHeaderHeading>
                {user.firstName || user.lastName
                  ? `${user.firstName} ${user.lastName}`
                  : user.email}
              </PageHeaderHeading>
              <TabsList>
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="organizations">Organizations</TabsTrigger>
              </TabsList>
            </div>
          </PageHeader>

          <TabsContent value="summary" className="mt-6">
            <div className="grid gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name</span>
                    <span>{`${user.firstName ?? ''} ${user.lastName ?? ''}`}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Email</span>
                    <span>{user.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Phone</span>
                    <span>{user.phone}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Date of Birth</span>
                    <span>
                      {user.dob
                        ? new Date(user.dob).toLocaleDateString()
                        : 'N/A'}
                    </span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Account Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Account Type</span>
                    <span className="capitalize">{title(user.role)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="organizations" className="mt-6">
            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Organizations</CardTitle>
                </CardHeader>
                <CardContent>
                  {user.members && user.members.length > 0 ? (
                    <div className="space-y-4">
                      {user.members.map((member) => (
                        <div key={member.id} className="border-b pb-4">
                          <div className="mb-2 flex items-center justify-between">
                            <h3 className="font-medium text-lg">
                              {member.organization.name}
                            </h3>
                            <span className="rounded-md bg-muted px-2 py-1 text-xs capitalize">
                              {member.role}
                            </span>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span className="text-muted-foreground">
                                Organization ID:
                              </span>
                              <span className="ml-2">
                                {member.organization.id}
                              </span>
                            </div>
                            {member.organization.slug && (
                              <div>
                                <span className="text-muted-foreground">
                                  Slug:
                                </span>
                                <span className="ml-2">
                                  {member.organization.slug}
                                </span>
                              </div>
                            )}
                            <div>
                              <span className="text-muted-foreground">
                                Premium Tier:
                              </span>
                              <span className="ml-2">
                                {member.organization.premiumTier?.name ||
                                  'None'}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-muted-foreground">
                      User is not a member of any organization
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </>
  );
}
