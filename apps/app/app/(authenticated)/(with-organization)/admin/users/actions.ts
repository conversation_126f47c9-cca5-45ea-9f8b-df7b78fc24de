import { ApiEndpoint } from '@/app/lib/api';
import { fetchWithAuth } from '@/app/lib/api';
import { getJwtFromSession } from '@/app/utils/auth-helpers';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { log } from '@repo/observability/log';
import { notFound } from 'next/navigation';

export type UserResponse = {
  data: {
    id: string;
    name: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
    phone: string | null;
    members: {
      organization: {
        id: string;
        name: string;
        slug: string | null;
      };
      role: string;
    }[];
  }[];
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    from: number;
    to: number;
  };
};

export const getUsers = async (
  page = 1,
  perPage = 10
): Promise<UserResponse> => {
  try {
    const response = await fetchWithAuth<UserResponse>(
      urlSerialize(ApiEndpoint.USERS, { page, per_page: perPage }),
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getUsers', { response });
    return response;
  } catch (error) {
    log.warn('Failed to fetch users from API', { error });
    throw new Error('Failed to fetch users from API');
  }
};

export type UserDetailResponse = {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
  phone: string | null;
  dob: string | null;
  role: string | null;
  members: {
    id: string;
    role: string;
    organization: {
      id: string;
      name: string;
      slug: string | null;
      premiumTier: {
        name: string;
      } | null;
    };
  }[];
};

export const getUser = async (id: string) => {
  try {
    const response = await fetchWithAuth<{ data: UserDetailResponse }>(
      `${ApiEndpoint.USERS}/${id}`,
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getUser', { response });

    if (!response.data) {
      return notFound();
    }

    return response.data;
  } catch (error) {
    log.warn('Failed to fetch user from API', { error });
    throw new Error('Failed to fetch user from API');
  }
};
