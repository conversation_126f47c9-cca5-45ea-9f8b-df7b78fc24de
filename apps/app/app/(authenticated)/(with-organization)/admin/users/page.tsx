import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';
import { getUsers } from './actions';
import { columns } from './components/user-column';
import { UserTable } from './components/user-table';

const title = 'MyInvoice - Users';
const description = 'MyInvoice - Users';

export const metadata: Metadata = {
  title,
  description,
};

const UsersPage = async (): Promise<ReactElement> => {
  const { data: users, meta } = await getUsers();

  return (
    <>
      <Header page="Users" />

      <main className="flex-1 space-y-8 px-4 lg:px-8">
        <PageHeader>
          <div>
            <PageHeaderHeading>Users</PageHeaderHeading>
            <PageHeaderDescription>Manage your users</PageHeaderDescription>
          </div>
        </PageHeader>

        <div className="md:min-h-min">
          <UserTable columns={columns} initialData={users} initialMeta={meta} />
        </div>
      </main>
    </>
  );
};

export default UsersPage;
