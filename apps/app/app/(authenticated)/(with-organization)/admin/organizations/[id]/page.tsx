import { Header } from '@/app/(authenticated)/(with-organization)/components/header';
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  PageHeader,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { getOrganization } from '../actions';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const organization = await getOrganization(id);

  const title = 'MyInvoice - Organization';
  const description = `Details for ${organization.name}`;

  return {
    title,
    description,
  };
}

export default async function OrganizationDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const organization = await getOrganization(id);

  return (
    <>
      <Header pages={['Organizations']} page={organization.name} />

      <main className="flex-1 space-y-4 px-4 lg:px-8">
        <Tabs defaultValue="summary">
          <PageHeader>
            <div className="space-y-2">
              <PageHeaderHeading>{organization.name}</PageHeaderHeading>
              <TabsList>
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="members">Members</TabsTrigger>
              </TabsList>
            </div>
          </PageHeader>

          <TabsContent value="summary" className="mt-6">
            <div className="grid gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Organization Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name</span>
                    <span>{organization.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Slug</span>
                    <span>{organization.slug || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created At</span>
                    <span>
                      {new Date(organization.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Premium Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Premium Tier</span>
                    <span className="capitalize">
                      {organization.premiumTier?.name || 'None'}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="members" className="mt-6">
            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Members</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {organization.members.length > 0 ? (
                    organization.members.map((member) => (
                      <div
                        key={member.id}
                        className="flex justify-between border-b py-2"
                      >
                        <div>
                          <div>{member.user.name}</div>
                          <div className="text-muted-foreground text-sm">
                            {member.user.email}
                          </div>
                        </div>
                        <div className="capitalize">{member.role}</div>
                      </div>
                    ))
                  ) : (
                    <div>No members found</div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </>
  );
}
