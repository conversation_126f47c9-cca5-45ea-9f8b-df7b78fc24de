import { ApiEndpoint } from '@/app/lib/api';
import { fetchWithAuth } from '@/app/lib/api';
import { getJwtFromSession } from '@/app/utils/auth-helpers';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { log } from '@repo/observability/log';
import { notFound } from 'next/navigation';

export type OrganizationResponse = {
  data: {
    id: string;
    name: string;
    slug: string | null;
    logo: string | null;
    premiumTier: {
      name: string;
    } | null;
    _count: {
      members: number;
    };
    createdAt: string;
  }[];
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    from: number;
    to: number;
  };
};

export type OrganizationDetailResponse = {
  id: string;
  name: string;
  slug: string | null;
  logo: string | null;
  premiumTier: {
    name: string;
  } | null;
  createdAt: string;
  members: {
    id: string;
    role: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
  }[];
};

export const getOrganizations = async (
  page = 1,
  perPage = 10
): Promise<OrganizationResponse> => {
  try {
    const response = await fetchWithAuth<OrganizationResponse>(
      urlSerialize(ApiEndpoint.ORGANIZATIONS, { page, per_page: perPage }),
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getOrganizations', { response });
    return response;
  } catch (error) {
    log.warn('Failed to fetch organizations from API', { error });
    throw new Error('Failed to fetch organizations from API');
  }
};

export const getOrganization = async (id: string) => {
  try {
    // Note: If there's no specific endpoint for a single organization,
    // we would need to create one in the API app. For now, we'll assume it exists.
    const response = await fetchWithAuth<{ data: OrganizationDetailResponse }>(
      `${ApiEndpoint.ORGANIZATIONS}/${id}`,
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getOrganization', { response });

    if (!response.data) {
      return notFound();
    }

    return response.data;
  } catch (error) {
    log.warn('Failed to fetch organization from API', { error });
    throw new Error('Failed to fetch organization from API');
  }
};
