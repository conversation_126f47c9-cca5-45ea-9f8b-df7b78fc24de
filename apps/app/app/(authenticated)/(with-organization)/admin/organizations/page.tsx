import {} from '@repo/design-system/components/ui/breadcrumb';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';
import { getOrganizations } from './actions';
import { columns } from './components/organization-column';
import { OrganizationTable } from './components/organization-table';

const title = 'MyInvoice - Organizations';
const description = 'MyInvoice - Organizations';

export const metadata: Metadata = {
  title,
  description,
};

const OrganizationsPage = async (): Promise<ReactElement> => {
  const { data: organizations, meta } = await getOrganizations();

  return (
    <>
      <Header page="Organizations" />

      <main className="flex-1 space-y-8 px-4 lg:px-8">
        <PageHeader>
          <div>
            <PageHeaderHeading>Organizations</PageHeaderHeading>
            <PageHeaderDescription>
              Manage your organizations
            </PageHeaderDescription>
          </div>
        </PageHeader>

        <div className="md:min-h-min">
          <OrganizationTable
            columns={columns}
            initialData={organizations}
            initialMeta={meta}
          />
        </div>
      </main>
    </>
  );
};

export default OrganizationsPage;
