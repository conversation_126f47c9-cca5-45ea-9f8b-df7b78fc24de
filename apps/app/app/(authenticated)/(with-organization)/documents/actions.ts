'use server';

import { ApiEndpoint, fetchWithAuth } from '@/app/lib/api';
import type { SubmittedDocument } from '@/app/types';
import { getJwtFromSession } from '@/app/utils/auth-helpers';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { log } from '@repo/observability/log';

type CoreResponse<T> = {
  meta: {
    total: number;
    per_page?: number;
    current_page?: number;
    last_page?: number;
  };
  data: T[];
};

export async function getSubmittedDocuments(
  page = 1,
  perPage = 10
): Promise<CoreResponse<SubmittedDocument>> {
  try {
    const documents = await fetchWithAuth<CoreResponse<SubmittedDocument>>(
      urlSerialize(ApiEndpoint.SUBMITTED_DOCUMENTS, {
        page,
        per_page: perPage,
      }),
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getSubmittedDocuments', { documents });
    return documents;
  } catch (error) {
    log.warn('Failed to fetch submitted documents from core backend', {
      error,
    });
    throw new Error('Failed to fetch submitted documents from core backend');
  }
}

export async function getSubmittedDocument(
  id: string
): Promise<{ data: SubmittedDocument }> {
  try {
    const document = await fetchWithAuth<{ data: SubmittedDocument }>(
      `${ApiEndpoint.SUBMITTED_DOCUMENTS}/${id}`,
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getSubmittedDocument', { document });
    return document;
  } catch (error) {
    log.warn('Failed to fetch submitted document from core backend', { error });
    throw new Error('Failed to fetch submitted document from core backend');
  }
}
