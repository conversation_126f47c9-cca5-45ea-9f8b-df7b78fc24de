'use client';

import { ApiEndpoint } from '@/app/lib/api';
import { clientFetchWithAuth } from '@/app/lib/api.client';
import { useJwtToken } from '@/app/utils/auth-client-helpers';
import { log } from '@repo/observability/log';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { mutate } from 'swr';

/**
 * Custom hook for deleting a submitted document
 * This uses direct client -> core API communication
 */
export function useDeleteSubmittedDocument() {
  const { jwt } = useJwtToken();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const deleteSubmittedDocument = async (id: string): Promise<void> => {
    if (!jwt) {
      setError(new Error('Authentication required'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await clientFetchWithAuth(
        `${ApiEndpoint.SUBMITTED_DOCUMENTS}/${id}`,
        jwt,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      log.info('Document deleted successfully', { id });

      // Navigate back to documents list
      router.push('/submitted-documents');
      mutate(
        (url) =>
          typeof url === 'string' &&
          url.startsWith(ApiEndpoint.SUBMITTED_DOCUMENTS)
      );
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to delete document');
      log.warn('Failed to delete document in core backend', { error });
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    deleteSubmittedDocument,
    isLoading,
    error,
  };
}
