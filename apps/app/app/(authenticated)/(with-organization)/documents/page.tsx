import type { SubmittedDocument } from '@/app/types';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import { log } from '@repo/observability/log';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getSubmittedDocuments } from './actions';
import { columns } from './components/submitted-document-column';
import { SubmittedDocumentTable } from './components/submitted-document-table';

const title = 'MyInvoice - Documents';
const description = 'MyInvoice - View all documents';

export const metadata: Metadata = {
  title,
  description,
};

const SubmittedDocumentsPage = async (): Promise<ReactElement> => {
  let documents: SubmittedDocument[] = [];
  let meta:
    | {
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
        from: number;
        to: number;
      }
    | undefined;

  try {
    const response = await getSubmittedDocuments();
    documents = response.data;

    // Transform the meta data to match the expected format
    if (response.meta) {
      meta = {
        total: response.meta.total,
        per_page: response.meta.per_page || 10,
        current_page: response.meta.current_page || 1,
        last_page:
          response.meta.last_page || Math.ceil(response.meta.total / 10),
        from:
          ((response.meta.current_page || 1) - 1) *
            (response.meta.per_page || 10) +
          1,
        to: Math.min(
          (response.meta.current_page || 1) * (response.meta.per_page || 10),
          response.meta.total
        ),
      };
    }
  } catch (error) {
    log.warn('Failed to fetch submitted documents', { error });
  }

  return (
    <>
      <Header page="Documents" />

      <main className="flex-1 space-y-8 px-4 lg:px-8">
        <PageHeader>
          <div>
            <PageHeaderHeading>Documents</PageHeaderHeading>
            <PageHeaderDescription>
              Submitted documents like invoices, credit notes, and debit notes
            </PageHeaderDescription>
          </div>
        </PageHeader>

        <div className="md:min-h-min">
          <SubmittedDocumentTable
            columns={columns}
            initialData={documents}
            initialMeta={meta}
          />
        </div>
      </main>
    </>
  );
};

export default SubmittedDocumentsPage;
