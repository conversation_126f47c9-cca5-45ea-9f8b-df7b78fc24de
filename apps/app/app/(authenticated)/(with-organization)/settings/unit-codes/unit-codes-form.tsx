'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {} from '@repo/design-system/components/ui/dialog';
import { Input } from '@repo/design-system/components/ui/input';
import {
  <PERSON>rollArea,
  ScrollBar,
} from '@repo/design-system/components/ui/scroll-area';
import { Star, StarOff, X } from 'lucide-react';
import { useState } from 'react';
import { unitTypesService } from '../../orders/schemas/unit-types';
import { useUnitCodesStore } from '../../orders/stores/unit-codes-store';

export function UnitCodesForm() {
  const {
    recentlyUsed,
    favorites,
    addToFavorites,
    removeFromFavorites,
    clearRecentlyUsed,
  } = useUnitCodesStore();
  const [searchTerm, setSearchTerm] = useState('');

  // Filter unit types based on search term
  const allUnitTypes = unitTypesService.getAllTypes();
  const filteredUnitTypes = searchTerm
    ? allUnitTypes.filter(
        (unit) =>
          unit.Name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          unit.Code.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : allUnitTypes;

  // Check if a unit code is in favorites
  const isFavorite = (code: string) => favorites.includes(code);

  // Toggle favorite status
  const toggleFavorite = (code: string) => {
    if (isFavorite(code)) {
      removeFromFavorites(code);
    } else {
      addToFavorites(code);
    }
  };

  return (
    <>
      {/* Search Input */}
      <Input
        placeholder="Search unit codes..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />

      {/* Favorites Section */}
      <div className="space-y-2">
        <h3 className="font-medium text-sm">Favorites</h3>
        <div className="flex flex-wrap gap-2">
          {favorites.length === 0 ? (
            <p className="text-muted-foreground text-sm">
              No favorites yet. Add some below.
            </p>
          ) : (
            favorites.map((code) => {
              const unit = unitTypesService.getByCode(code);
              if (!unit) {
                return null;
              }

              return (
                <Badge
                  key={unit.Code}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {unit.Name} ({unit.Code})
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0"
                    onClick={() => removeFromFavorites(unit.Code)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              );
            })
          )}
        </div>
      </div>

      {/* Recently Used Section */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm">Recently Used</h3>
          {recentlyUsed.length > 0 && (
            <Button variant="ghost" size="sm" onClick={clearRecentlyUsed}>
              Clear
            </Button>
          )}
        </div>
        <div className="flex flex-wrap gap-2">
          {recentlyUsed.length === 0 ? (
            <p className="text-muted-foreground text-sm">
              No recently used unit codes.
            </p>
          ) : (
            recentlyUsed.map((code) => {
              const unit = unitTypesService.getByCode(code);
              if (!unit) {
                return null;
              }

              return (
                <Badge
                  key={unit.Code}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  {unit.Name} ({unit.Code})
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0"
                    onClick={() => toggleFavorite(unit.Code)}
                  >
                    {isFavorite(unit.Code) ? (
                      <StarOff className="h-3 w-3" />
                    ) : (
                      <Star className="h-3 w-3" />
                    )}
                  </Button>
                </Badge>
              );
            })
          )}
        </div>
      </div>

      {/* All Units Section */}
      <div className="space-y-2">
        <h3 className="font-medium text-sm">All Units</h3>
        <ScrollArea className="h-[400px]">
          <div className="flex flex-wrap gap-2">
            {filteredUnitTypes.map((unit) => (
              <Badge
                key={unit.Code}
                variant={isFavorite(unit.Code) ? 'secondary' : 'outline'}
                className="flex cursor-pointer items-center gap-1"
                onClick={() => toggleFavorite(unit.Code)}
              >
                {unit.Name} ({unit.Code})
                {isFavorite(unit.Code) ? (
                  <StarOff className="ml-1 h-3 w-3" />
                ) : (
                  <Star className="ml-1 h-3 w-3" />
                )}
              </Badge>
            ))}
          </div>
          <ScrollBar orientation="vertical" />
        </ScrollArea>
      </div>
    </>
  );
}
