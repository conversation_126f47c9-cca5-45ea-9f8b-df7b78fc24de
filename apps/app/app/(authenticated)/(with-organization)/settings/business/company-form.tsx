'use client';

import { ApiEndpoint } from '@/app/lib/api';
import { clientFetchWithAuth } from '@/app/lib/api.client';
import { useApiSWR } from '@/app/lib/swr';
import type { Company } from '@/app/types';
import { useJwtToken } from '@/app/utils/auth-client-helpers';
import {
  Check,
  ChevronsUpDown,
  LoaderCircle,
} from '@repo/design-system/components/icons';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@repo/design-system/components/ui/command';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/design-system/components/ui/popover';
import { ScrollArea } from '@repo/design-system/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import { toast } from '@repo/design-system/components/ui/sonner';
import { cn, urlSerialize } from '@repo/design-system/lib/utils';
import { useState } from 'react';
import { mutate } from 'swr';
import { z } from 'zod';

// Import constants and components
import { BUSINESS_TYPES } from '@repo/auth/components/onboarding/business-type-selector';
import { msicOptions } from '@repo/auth/constants/msic';

// Registration type mapping for labels and descriptions
const REGISTRATION_TYPE_INFO = {
  BRN: {
    label: 'Business Registration Number',
    placeholder: 'Enter business registration number',
    description: 'The official registration number of your business entity',
  },
  NRIC: {
    label: 'NRIC Number',
    placeholder: 'Enter your NRIC number',
    description: 'Your National Registration Identity Card number',
  },
  PASSPORT: {
    label: 'Passport Number',
    placeholder: 'Enter your passport number',
    description: 'Your international passport identification number',
  },
  ARMY: {
    label: 'Army ID Number',
    placeholder: 'Enter your army ID number',
    description: 'Your military identification number',
  },
};

export default function CompanyForm() {
  const { data, isLoading, error } = useApiSWR<{ data: Company }>(
    // Use urlSerialize to add parameters to the URL
    urlSerialize(ApiEndpoint.ME_COMPANIES),
    {
      revalidateOnFocus: false,
    }
  );

  if (error) {
    return <div>Failed to load company</div>;
  }

  if (!data?.data || isLoading) {
    return (
      <section className="grid grid-cols-1 gap-4 space-y-6 xl:grid-cols-2">
        <Skeleton className="h-80" />
        <Skeleton className="h-80" />
        <Skeleton className="h-80" />
        <Skeleton className="h-80" />
        <Skeleton className="h-80" />
      </section>
    );
  }

  const company = data.data;

  return (
    <section className="grid grid-cols-1 gap-4 space-y-6 xl:grid-cols-2">
      <ApiCredentialsForm company={company} />
      <BasicInfoForm company={company} />
      <BusinessDetailsForm company={company} />
      <TaxInformationForm company={company} />
      <ContactInfoForm company={company} />
    </section>
  );
}

// API Credentials Form Schema
const apiCredentialsSchema = z.object({
  client_id: z.string().min(1, 'Client ID is required'),
  client_secret: z.string().min(1, 'Client Secret is required'),
});

function ApiCredentialsForm({ company }: { company: Company }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { jwt } = useJwtToken();

  const form = useForm<z.infer<typeof apiCredentialsSchema>>({
    resolver: zodResolver(apiCredentialsSchema),
    defaultValues: {
      client_id: company.client_id || '',
      client_secret: company.client_secret || '',
    },
  });

  async function onSubmit(values: z.infer<typeof apiCredentialsSchema>) {
    if (!jwt) {
      return;
    }

    setIsSubmitting(true);

    try {
      await clientFetchWithAuth(ApiEndpoint.ME_COMPANIES, jwt, {
        method: 'PUT',
        body: JSON.stringify(values),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Invalidate cache to refetch updated data
      mutate(urlSerialize(ApiEndpoint.ME_COMPANIES));
      toast.success('API credentials updated successfully');
    } catch (_error) {
      toast.error('Failed to update API credentials');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="h-full">
          <CardHeader>
            <CardTitle>API Credentials</CardTitle>
            <CardDescription>
              Your MyInvois API credentials for e-invoice submission.
            </CardDescription>
          </CardHeader>
          <CardContent className="mx-auto w-full flex-1 space-y-4">
            <FormField
              control={form.control}
              name="client_id"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Client ID</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your Client ID"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="client_secret"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Client Secret</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Enter your Client Secret"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </CardContent>
          <CardFooter className="justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}

// Basic Info Form Schema
const basicInfoSchema = z.object({
  name: z.string().min(1, 'Business name is required'),
  tin_code: z.string().min(1, 'TIN code is required'),
  registration_number: z.string().min(1, 'Registration number is required'),
  registration_type: z.enum(['BRN', 'NRIC', 'PASSPORT', 'ARMY'], {
    errorMap: () => ({ message: 'Please select a valid registration type' }),
  }),
});

function BasicInfoForm({ company }: { company: Company }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { jwt } = useJwtToken();

  const form = useForm<z.infer<typeof basicInfoSchema>>({
    resolver: zodResolver(basicInfoSchema),
    defaultValues: {
      name: company.name || '',
      tin_code: company.tin_code || '',
      registration_number: company.registration_number || '',
      registration_type: company.registration_type || 'BRN',
    },
  });

  const registrationType = form.watch('registration_type');

  async function onSubmit(values: z.infer<typeof basicInfoSchema>) {
    if (!jwt) {
      return;
    }

    setIsSubmitting(true);

    try {
      await clientFetchWithAuth(ApiEndpoint.ME_COMPANIES, jwt, {
        method: 'PUT',
        body: JSON.stringify(values),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Invalidate cache to refetch updated data
      mutate(urlSerialize(ApiEndpoint.ME_COMPANIES));
      toast.success('Basic information updated successfully');
    } catch (_error) {
      toast.error('Failed to update basic information');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Your business registration and identification details.
            </CardDescription>
          </CardHeader>
          <CardContent className="mx-auto w-full flex-1 space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Business Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your business name"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tin_code"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>TIN Code</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your TIN code"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="registration_type"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Registration Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl className="w-full">
                        <SelectTrigger>
                          <SelectValue placeholder="Select registration type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="BRN">BRN</SelectItem>
                        <SelectItem value="NRIC">NRIC</SelectItem>
                        <SelectItem value="PASSPORT">Passport</SelectItem>
                        <SelectItem value="ARMY">Army</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="registration_number"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>
                      {REGISTRATION_TYPE_INFO[registrationType]?.label ||
                        'Registration Number'}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={
                          REGISTRATION_TYPE_INFO[registrationType]
                            ?.placeholder || 'Enter registration number'
                        }
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    {REGISTRATION_TYPE_INFO[registrationType]?.description && (
                      <FormDescription>
                        {REGISTRATION_TYPE_INFO[registrationType].description}
                      </FormDescription>
                    )}
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}

// Business Details Form Schema
const businessDetailsSchema = z.object({
  business_activity_description: z
    .string()
    .min(1, 'Business activity description is required'),
  msic_code: z.string().min(1, 'MSIC code is required'),
  business_type: z
    .enum(
      [
        'retail',
        'healthcare',
        'education',
        'technology',
        'finance',
        'manufacturing',
        'services',
        'other',
      ],
      {
        errorMap: () => ({ message: 'Please select a valid business type' }),
      }
    )
    .optional(),
});

function BusinessDetailsForm({ company }: { company: Company }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [open, setOpen] = useState(false);
  const { jwt } = useJwtToken();

  const form = useForm<z.infer<typeof businessDetailsSchema>>({
    resolver: zodResolver(businessDetailsSchema),
    defaultValues: {
      business_activity_description:
        company.business_activity_description || '',
      msic_code: company.msic_code || '',
      business_type: undefined, // Company type doesn't have business_type field
    },
  });

  async function onSubmit(values: z.infer<typeof businessDetailsSchema>) {
    if (!jwt) {
      return;
    }

    setIsSubmitting(true);

    try {
      await clientFetchWithAuth(ApiEndpoint.ME_COMPANIES, jwt, {
        method: 'PUT',
        body: JSON.stringify(values),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Invalidate cache to refetch updated data
      mutate(urlSerialize(ApiEndpoint.ME_COMPANIES));
      toast.success('Business details updated successfully');
    } catch (_error) {
      toast.error('Failed to update business details');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Business Details</CardTitle>
            <CardDescription>
              Information about your business activities and classification.
            </CardDescription>
          </CardHeader>
          <CardContent className="mx-auto w-full flex-1 space-y-4">
            <FormField
              control={form.control}
              name="business_activity_description"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Business Activity Description</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter business activity description"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="business_type"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Business Type</FormLabel>
                  <FormControl>
                    <Select
                      disabled={isSubmitting}
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue
                          aria-label={field.value}
                          placeholder="Select your business type"
                        >
                          {field.value
                            ? BUSINESS_TYPES.find(
                                (type) => type.value === field.value
                              )?.label
                            : ''}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {BUSINESS_TYPES.map((type) => (
                          <SelectItem
                            key={type.value}
                            value={type.value}
                            textValue={type.label}
                          >
                            <div className="flex flex-col items-start justify-center">
                              <span>{type.label}</span>
                              <span className="text-muted-foreground text-xs">
                                {type.description}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="msic_code"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>MSIC Code</FormLabel>
                  <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          // biome-ignore lint/a11y/useSemanticElements: <explanation>
                          role="combobox"
                          aria-expanded={open}
                          className="w-full justify-between overflow-hidden"
                        >
                          <span className="truncate">
                            {field.value
                              ? msicOptions.find(
                                  (option) => option.Code === field.value
                                )?.Description
                              : 'Select MSIC Code...'}
                          </span>
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[400px] p-0">
                      <Command>
                        <CommandInput placeholder="Search MSIC code or description..." />
                        <CommandEmpty>No MSIC code found.</CommandEmpty>
                        <CommandGroup className="max-h-[300px] overflow-y-auto">
                          <ScrollArea className="h-72">
                            {msicOptions.map((option) => (
                              <CommandItem
                                key={option.Code}
                                value={`${option.Code} ${option.Description}`}
                                onSelect={() => {
                                  field.onChange(option.Code);
                                  setOpen(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    'mr-2 h-4 w-4',
                                    field.value === option.Code
                                      ? 'opacity-100'
                                      : 'opacity-0'
                                  )}
                                />
                                {option.Code} - {option.Description}
                              </CommandItem>
                            ))}
                          </ScrollArea>
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </CardContent>
          <CardFooter className="justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}

// Tax Information Form Schema
const taxInformationSchema = z.object({
  applicable_tax_types: z
    .array(z.string())
    .min(1, 'At least one tax type must be selected'),
  sales_tax_rates: z.array(z.string()).optional(),
  service_tax_rates: z.array(z.string()).optional(),
  sst_registration_number: z.string().optional(),
  tourism_tax_registration_number: z.string().optional(),
});

function TaxInformationForm({ company }: { company: Company }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { jwt } = useJwtToken();

  const form = useForm<z.infer<typeof taxInformationSchema>>({
    resolver: zodResolver(taxInformationSchema),
    defaultValues: {
      applicable_tax_types: ['06'], // Default to 'Not Applicable'
      sales_tax_rates: [],
      service_tax_rates: [],
      sst_registration_number: company.sst_registration_number || '',
      tourism_tax_registration_number:
        company.tourism_tax_registration_number || '',
    },
  });

  async function onSubmit(values: z.infer<typeof taxInformationSchema>) {
    if (!jwt) {
      return;
    }

    setIsSubmitting(true);

    try {
      await clientFetchWithAuth(ApiEndpoint.ME_COMPANIES, jwt, {
        method: 'PUT',
        body: JSON.stringify(values),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Invalidate cache to refetch updated data
      mutate(urlSerialize(ApiEndpoint.ME_COMPANIES));
      toast.success('Tax information updated successfully');
    } catch (_error) {
      toast.error('Failed to update tax information');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Tax Information</CardTitle>
            <CardDescription>
              Your business tax registration details.
            </CardDescription>
          </CardHeader>
          <CardContent className="mx-auto w-full flex-1 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="sst_registration_number"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>SST Registration Number</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter SST registration number"
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tourism_tax_registration_number"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Tourism Tax Registration Number</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter tourism tax registration number"
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}

// Contact Information Form Schema
const contactInfoSchema = z.object({
  country: z.string().min(1, 'Country is required'),
  state: z.string().min(1, 'State is required'),
  zip_code: z.string().min(1, 'ZIP code is required'),
  city: z.string().min(1, 'City is required'),
  address: z.string().min(1, 'Address is required'),
  phone: z.string().min(1, 'Phone number is required'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
});

function ContactInfoForm({ company }: { company: Company }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { jwt } = useJwtToken();

  const form = useForm<z.infer<typeof contactInfoSchema>>({
    resolver: zodResolver(contactInfoSchema),
    defaultValues: {
      country: company.country || '',
      state: company.state || '',
      zip_code: company.zip_code || '',
      city: company.city || '',
      address: company.address || '',
      phone: company.phone || '',
      email: company.email || '',
    },
  });

  async function onSubmit(values: z.infer<typeof contactInfoSchema>) {
    if (!jwt) {
      return;
    }

    setIsSubmitting(true);

    try {
      await clientFetchWithAuth(ApiEndpoint.ME_COMPANIES, jwt, {
        method: 'PUT',
        body: JSON.stringify(values),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Invalidate cache to refetch updated data
      mutate(urlSerialize(ApiEndpoint.ME_COMPANIES));
      toast.success('Contact information updated successfully');
    } catch (_error) {
      toast.error('Failed to update contact information');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>
              Your business address and contact details.
            </CardDescription>
          </CardHeader>
          <CardContent className="mx-auto w-full flex-1 space-y-4">
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <GridFormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your address"
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>City</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your city"
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="zip_code"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>ZIP Code</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your ZIP code"
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="country"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your country"
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="state"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>State</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your state"
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter your phone number"
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter your email"
                        disabled={isSubmitting}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
