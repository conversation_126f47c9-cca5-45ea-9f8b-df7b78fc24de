'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import { Input } from '@repo/design-system/components/ui/input';
import {
  ScrollArea,
  ScrollBar,
} from '@repo/design-system/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { Star, StarOff, X } from 'lucide-react';
import { useState } from 'react';
import { classificationCategories } from '../../orders/schemas/classification-categories';
import { classificationCodesService } from '../../orders/schemas/classifications';
import { useClassificationsStore } from '../../orders/stores/classifications-store';

export function ClassificationsForm() {
  const {
    recentlyUsed,
    favorites,
    addToFavorites,
    removeFromFavorites,
    clearRecentlyUsed,
  } = useClassificationsStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Get all classifications
  const allClassifications = classificationCodesService.getAllCodes();

  // Filter classifications based on search term and selected category
  const filteredClassifications = allClassifications.filter(
    (classification) => {
      const matchesSearch =
        !searchTerm ||
        classification.Description.toLowerCase().includes(
          searchTerm.toLowerCase()
        ) ||
        classification.Code.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory =
        !selectedCategory ||
        classificationCategories
          .find((cat) => cat.id === selectedCategory)
          ?.codes.includes(classification.Code);

      return matchesSearch && matchesCategory;
    }
  );

  // Check if a classification code is in favorites
  const isFavorite = (code: string) => favorites.includes(code);

  // Toggle favorite status
  const toggleFavorite = (code: string) => {
    if (isFavorite(code)) {
      removeFromFavorites(code);
    } else {
      addToFavorites(code);
    }
  };

  // Get category name for a classification code
  const getCategoryName = (code: string) => {
    const category = classificationCategories.find((cat) =>
      cat.codes.includes(code)
    );
    return category?.name || 'Other';
  };

  return (
    <>
      {/* Search and Filter */}
      <div className="flex flex-col gap-2 sm:flex-row">
        <Input
          placeholder="Search classifications..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1"
        />
        <Select
          onValueChange={(value) =>
            value === 'all'
              ? setSelectedCategory(null)
              : setSelectedCategory(value || null)
          }
          defaultValue={selectedCategory || 'all'}
        >
          <SelectTrigger className="w-fit">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {classificationCategories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Favorites Section */}
      <div className="space-y-2">
        <h3 className="font-medium text-sm">Favorites</h3>
        <div className="flex flex-wrap gap-2">
          {favorites.length === 0 ? (
            <p className="text-muted-foreground text-sm">
              No favorites yet. Add some below.
            </p>
          ) : (
            favorites.map((code) => {
              const classification = classificationCodesService.getByCode(code);
              if (!classification) {
                return null;
              }

              return (
                <Badge
                  key={classification.Code}
                  variant="secondary"
                  className="flex items-center gap-1"
                >
                  {classification.Code} - {classification.Description}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0"
                    onClick={() => removeFromFavorites(classification.Code)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              );
            })
          )}
        </div>
      </div>

      {/* Recently Used Section */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm">Recently Used</h3>
          {recentlyUsed.length > 0 && (
            <Button variant="ghost" size="sm" onClick={clearRecentlyUsed}>
              Clear
            </Button>
          )}
        </div>
        <div className="flex flex-wrap gap-2">
          {recentlyUsed.length === 0 ? (
            <p className="text-muted-foreground text-sm">
              No recently used classifications.
            </p>
          ) : (
            recentlyUsed.map((code) => {
              const classification = classificationCodesService.getByCode(code);
              if (!classification) {
                return null;
              }

              return (
                <Badge
                  key={classification.Code}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  {classification.Code} - {classification.Description}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0"
                    onClick={() => toggleFavorite(classification.Code)}
                  >
                    {isFavorite(classification.Code) ? (
                      <StarOff className="h-3 w-3" />
                    ) : (
                      <Star className="h-3 w-3" />
                    )}
                  </Button>
                </Badge>
              );
            })
          )}
        </div>
      </div>

      {/* All Classifications Section */}
      <div className="space-y-2">
        <h3 className="font-medium text-sm">
          {selectedCategory
            ? classificationCategories.find(
                (cat) => cat.id === selectedCategory
              )?.name
            : 'All'}{' '}
          Classifications
        </h3>
        <ScrollArea className="h-[400px]">
          <div className="flex flex-col gap-2">
            {filteredClassifications.map((classification) => (
              <Badge
                key={classification.Code}
                variant={
                  isFavorite(classification.Code) ? 'secondary' : 'outline'
                }
                className="flex cursor-pointer items-center justify-between"
              >
                <div>
                  <span className="font-medium">{classification.Code}</span> -{' '}
                  {classification.Description}
                  <span className="ml-2 text-muted-foreground text-xs">
                    {getCategoryName(classification.Code)}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5 p-0"
                  onClick={() => toggleFavorite(classification.Code)}
                >
                  {isFavorite(classification.Code) ? (
                    <StarOff className="h-3 w-3" />
                  ) : (
                    <Star className="h-3 w-3" />
                  )}
                </Button>
              </Badge>
            ))}
          </div>
          <ScrollBar orientation="vertical" />
        </ScrollArea>
      </div>
    </>
  );
}
