import { Tab<PERSON>ink, TabLinkList } from '@/app/components/tab-links';
import {} from '@repo/design-system/components/ui/breadcrumb';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import {} from '@repo/design-system/components/ui/sidebar';
import type { Metadata } from 'next';
import type React from 'react';
import { Header } from '../components/header';

const title = 'MyInvoice - Settings';
const description = 'MyInvoice - Settings';

export const metadata: Metadata = {
  title,
  description,
};

interface SettingsLayoutProps {
  children: React.ReactNode;
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  return (
    <>
      <Header page="Settings" />

      <main className="flex-1 space-y-4 px-4 lg:px-8">
        <PageHeader>
          <div>
            <PageHeaderHeading>Settings</PageHeaderHeading>
            <PageHeaderDescription>
              Manage your account settings & business information
            </PageHeaderDescription>
            <TabLinkList className="mt-4">
              <TabLink href="/settings">Account</TabLink>
              <TabLink href="/settings/business">Business</TabLink>
              <TabLink href="/settings/unit-codes">Unit Codes</TabLink>
              <TabLink href="/settings/classifications">
                Classifications
              </TabLink>
            </TabLinkList>
          </div>
        </PageHeader>

        <div className="md:min-h-min">
          <div className="@container/main flex-1">{children}</div>
        </div>
      </main>
    </>
  );
}
