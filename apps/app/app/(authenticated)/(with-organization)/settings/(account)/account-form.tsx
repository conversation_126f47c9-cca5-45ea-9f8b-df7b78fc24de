'use client';

import { useUpload } from '@/app/hooks/use-upload';
import authClient, { type User, useSession } from '@repo/auth/client';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Datepicker } from '@repo/design-system/components/ui/datepicker';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/design-system/components/ui/popover';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import { toast } from '@repo/design-system/components/ui/sonner';
import { useFileUpload } from '@repo/design-system/hooks/use-file-upload';
import { formatDateTime } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import { CalendarIcon, CircleUserRoundIcon, XIcon } from 'lucide-react';
import { notFound } from 'next/navigation';
import { useState } from 'react';
import { z } from 'zod';

export default function AccountForm() {
  const { data: session, isPending } = useSession();

  if (isPending) {
    return <Skeleton />;
  }

  if (!session?.user || !session.session) {
    return notFound();
  }

  return (
    <section className="space-y-6">
      <ProfileImageForm userData={session?.user} />
      <BasicInfoForm userData={session?.user} />
      <EmailForm userData={session?.user} />
    </section>
  );
}

function ProfileImageForm({ userData }: { userData: User }) {
  const { uploadFile } = useUpload();

  const [
    { files, isDragging },
    {
      removeFile,
      openFileDialog,
      getInputProps,
      handleDragEnter,
      handleDragLeave,
      handleDragOver,
      handleDrop,
    },
  ] = useFileUpload({
    accept: 'image/*',
    onFilesAdded: async (files) => {
      try {
        if (files.length > 0) {
          const file = files[0].file;
          const result = await uploadFile(file as File, 'public');

          if (!result) {
            toast.error('Failed to upload profile image');
            removeFile(files[0].id);
            return;
          }

          if (!result.success) {
            toast.error('Failed to upload profile image');
            removeFile(files[0].id);
            return;
          }

          await authClient.updateUser({
            image: result.url,
          });
          toast.success('Profile image updated successfully');
        }
      } catch (_error) {
        toast.error('Failed to update profile image');
        removeFile(files[0].id);
      }
    },
  });

  // TODO: add loading state
  const previewUrl = userData.image || files[0]?.preview || null;

  return (
    <Card>
      <CardHeader className="flex flex-row justify-between space-x-4">
        <div className="space-y-1">
          <CardTitle>Profile Image</CardTitle>
          <CardDescription>
            Upload a profile picture to personalize your account.
          </CardDescription>
        </div>
        <div className="flex flex-col items-center gap-2">
          <div className="relative inline-flex">
            {/* Drop area */}
            <button
              type="button"
              className="relative flex size-16 items-center justify-center overflow-hidden rounded-full border border-input outline-none transition-colors hover:bg-accent/50 focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 has-disabled:pointer-events-none has-[img]:border-none has-disabled:opacity-50 data-[dragging=true]:bg-accent/50"
              onClick={openFileDialog}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              data-dragging={isDragging || undefined}
              aria-label={previewUrl ? 'Change image' : 'Upload image'}
            >
              {previewUrl ? (
                // biome-ignore lint/nursery/noImgElement: <explanation>
                <img
                  className="size-full object-cover"
                  src={previewUrl}
                  alt={files[0]?.file?.name || 'Uploaded image'}
                  width={64}
                  height={64}
                  style={{ objectFit: 'cover' }}
                />
              ) : (
                <div aria-hidden="true">
                  <CircleUserRoundIcon className="size-4 opacity-60" />
                </div>
              )}
            </button>
            {previewUrl && (
              <Button
                onClick={() => removeFile(files[0]?.id)}
                size="icon"
                className="-top-1 -right-1 absolute size-6 rounded-full border-2 border-background shadow-none focus-visible:border-background"
                aria-label="Remove image"
              >
                <XIcon className="size-3.5" />
              </Button>
            )}
            <input
              {...getInputProps()}
              className="sr-only"
              aria-label="Upload image file"
              tabIndex={-1}
            />
          </div>
        </div>
      </CardHeader>
    </Card>
  );
}

// Basic Info Form
const accountBasicInfoSchema = z.object({
  name: z.string().min(1, 'Name is required.'),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
  dob: z
    .date({
      required_error: 'A date of birth is required.',
    })
    .optional(),
});

function BasicInfoForm({ userData }: { userData: User }) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof accountBasicInfoSchema>>({
    resolver: zodResolver(accountBasicInfoSchema),
    defaultValues: {
      name: userData?.name || '',
      firstName: userData?.firstName || '',
      lastName: userData?.lastName || '',
      phone: userData?.phone || '',
      dob: userData?.dob ? new Date(userData.dob) : undefined,
    },
  });

  async function onSubmit(values: z.infer<typeof accountBasicInfoSchema>) {
    setIsSubmitting(true);

    try {
      await authClient.updateUser({
        name: values.name,
        firstName: values.firstName,
        lastName: values.lastName,
        phone: values.phone,
        dob: values.dob,
      });

      toast.success('Basic information updated successfully');
    } catch (_error) {
      toast.error('Failed to update basic information');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>Your basic information.</CardDescription>
          </CardHeader>
          <CardContent className="mx-auto w-full space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Display Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="Phone" {...field} />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="First Name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Last Name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="dob"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Date of Birth</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={'outline'}
                            className={cn(
                              'pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              formatDateTime(field.value, 'PPP')
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Datepicker
                          selected={field.value}
                          onSelect={field.onChange}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save'}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}

// Email Form
const emailSchema = z.object({
  email: z.string().email('Please enter a valid email address.'),
});

function EmailForm({ userData }: { userData: User }) {
  // const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof emailSchema>>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: userData?.email || '',
    },
  });

  // async function onSubmit(values: z.infer<typeof emailSchema>) {
  //   setIsSubmitting(true);
  //   try {
  //     await authClient.updateUser({
  //     });

  //     toast.success('Email updated successfully');
  //   } catch (_error) {
  //     toast.error('Failed to update email');
  //   } finally {
  //     setIsSubmitting(false);
  //   }
  // }

  return (
    <Form {...form}>
      <form
      // onSubmit={form.handleSubmit(onSubmit)}
      >
        <Card>
          <CardHeader>
            <CardTitle>Email Address</CardTitle>
            <CardDescription>Email address is not editable.</CardDescription>
          </CardHeader>
          <CardContent className="mx-auto w-full space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <GridFormItem>
                  <FormControl>
                    <Input
                      readOnly
                      placeholder="Email"
                      type="email"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </CardContent>
          {/* <CardFooter className="justify-end">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save'}
            </Button>
          </CardFooter> */}
        </Card>
      </form>
    </Form>
  );
}
