import { Tab<PERSON><PERSON>, <PERSON>b<PERSON>inkList } from '@/app/components/tab-links';
import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import type { Metadata } from 'next';
import type React from 'react';
import { Header } from '../components/header';

const title = 'MyInvoice - Developers';
const description = 'MyInvoice - Developers';

export const metadata: Metadata = {
  title,
  description,
};

interface DevelopersLayoutProps {
  children: React.ReactNode;
}

export default function DevelopersLayout({ children }: DevelopersLayoutProps) {
  return (
    <>
      <Header page="Developers" />

      <main className="flex-1 space-y-4 px-4 lg:px-8">
        <PageHeader>
          <div className="space-y-2">
            <PageHeaderHeading>Developers</PageHeaderHeading>
            <PageHeaderDescription>
              Manage your api keys & webhooks
            </PageHeaderDescription>
            <TabLinkList className="mt-4">
              <TabLink href="/developers">Overview</TabLink>
              <TabLink href="/developers/api-keys">API Keys</TabLink>
            </TabLinkList>
          </div>
        </PageHeader>

        <div className="md:min-h-min">
          <div className="@container/main flex-1">{children}</div>
        </div>
      </main>
    </>
  );
}
