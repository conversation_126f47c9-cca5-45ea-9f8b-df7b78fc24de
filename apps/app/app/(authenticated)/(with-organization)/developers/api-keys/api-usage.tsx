'use client';

import { env } from '@/env';
import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { toast } from '@repo/design-system/components/ui/sonner';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { Check, Copy } from 'lucide-react';
import { useState } from 'react';

export function ApiUsageGuide() {
  const [copied, setCopied] = useState<string | null>(null);

  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text);
    setCopied(id);
    toast.success('Code copied to clipboard');

    setTimeout(() => {
      setCopied(null);
    }, 2000);
  };

  return (
    <Card className="@container/card mt-6">
      <CardHeader>
        <CardTitle>API Usage Guide</CardTitle>
        <CardDescription>
          Learn how to use your API keys to authenticate with the API.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="curl">
          <TabsList className="mb-4">
            <TabsTrigger value="curl">cURL</TabsTrigger>
            <TabsTrigger value="node">Node.js</TabsTrigger>
            <TabsTrigger value="python">Python</TabsTrigger>
          </TabsList>

          <TabsContent value="curl" className="relative">
            <div className="relative rounded-md bg-muted p-4 font-mono text-sm">
              <pre>
                {`curl -X GET \\
  https://${env.NEXT_PUBLIC_API_URL}/v1/orders \\
  -H "Authorization: Bearer myinv_your_api_key_here"`}
              </pre>
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() =>
                  copyToClipboard(
                    `curl -X GET \\
  https://${env.NEXT_PUBLIC_API_URL}/v1/orders \\
  -H "Authorization: Bearer myinv_your_api_key_here"`,
                    'curl'
                  )
                }
              >
                {copied === 'curl' ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="node" className="relative">
            <div className="relative rounded-md bg-muted p-4 font-mono text-sm">
              <pre>
                {`const fetch = require('node-fetch');

const apiKey = 'myinv_your_api_key_here';

async function fetchOrders() {
  const response = await fetch('https://${env.NEXT_PUBLIC_API_URL}/v1/orders', {
    headers: {
      'Authorization': \`Bearer \${apiKey}\`,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  console.log(data);
}

fetchOrders();`}
              </pre>
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() =>
                  copyToClipboard(
                    `const fetch = require('node-fetch');

const apiKey = 'myinv_your_api_key_here';

async function fetchOrders() {
  const response = await fetch('https://${env.NEXT_PUBLIC_API_URL}/v1/orders', {
    headers: {
      'Authorization': \`Bearer \${apiKey}\`,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  console.log(data);
}

fetchOrders();`,
                    'node'
                  )
                }
              >
                {copied === 'node' ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="python" className="relative">
            <div className="relative rounded-md bg-muted p-4 font-mono text-sm">
              <pre>
                {`import requests

api_key = 'myinv_your_api_key_here'

def fetch_orders():
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(
        'https://${env.NEXT_PUBLIC_API_URL}/v1/orders',
        headers=headers
    )
    
    data = response.json()
    print(data)

fetch_orders()`}
              </pre>
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() =>
                  copyToClipboard(
                    `import requests

api_key = 'myinv_your_api_key_here'

def fetch_orders():
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(
        'https://${env.NEXT_PUBLIC_API_URL}/v1/orders',
        headers=headers
    )
    
    data = response.json()
    print(data)

fetch_orders()`,
                    'python'
                  )
                }
              >
                {copied === 'python' ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
