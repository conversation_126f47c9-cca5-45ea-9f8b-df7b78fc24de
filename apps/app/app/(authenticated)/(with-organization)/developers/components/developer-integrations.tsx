import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  ToggleGroup,
  ToggleGroupItem,
} from '@repo/design-system/components/ui/toggle-group';

export function DevelopersIntegration() {
  return (
    <div className="space-y-4">
      <div className="flex flex-row items-center justify-between">
        <h3 className="font-medium text-lg">Your Integration</h3>
        <ToggleGroup
          variant="outline"
          className="inline-flex"
          type="single"
          defaultValue="12h"
        >
          <ToggleGroupItem value="12h">12h</ToggleGroupItem>
          <ToggleGroupItem value="1w">1d</ToggleGroupItem>
          <ToggleGroupItem value="30d">1w</ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div className="grid @5xl/main:grid-cols-3 @xl/main:grid-cols-2 grid-cols-1 gap-4">
        <Card className="@container/card">
          <CardHeader className="relative">
            <CardDescription>API Requests</CardDescription>
            <CardTitle className="font-semibold @[250px]/card:text-3xl text-2xl tabular-nums">
              0
            </CardTitle>
          </CardHeader>
        </Card>
        {/* <Card className="@container/card">
            <CardHeader className="relative">
              <CardDescription>Documents Submitted</CardDescription>
              <CardTitle className="font-semibold @[250px]/card:text-3xl text-2xl tabular-nums">
                0
              </CardTitle>
            </CardHeader>
            <CardFooter className="flex-row gap-4">
              <div className="flex-col items-start gap-1">
                <CardDescription>Invoices</CardDescription>
                <CardTitle className="font-semibold text-lg tabular-nums">
                  0
                </CardTitle>
              </div>
              <div className="flex-col items-start gap-1">
                <CardDescription>Credit Notes</CardDescription>
                <CardTitle className="font-semibold text-lg tabular-nums">
                  0
                </CardTitle>
              </div>
              <div className="flex-col items-start gap-1">
                <CardDescription>Debit Notes</CardDescription>
                <CardTitle className="font-semibold text-lg tabular-nums">
                  0
                </CardTitle>
              </div>
            </CardFooter>
          </Card> */}
      </div>
    </div>
  );
}
