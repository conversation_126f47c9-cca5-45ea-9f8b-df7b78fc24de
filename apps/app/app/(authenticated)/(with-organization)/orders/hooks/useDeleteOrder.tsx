'use client';

import { ApiEndpoint } from '@/app/lib/api';
import { clientFetchWithAuth } from '@/app/lib/api.client';
import { useJwtToken } from '@/app/utils/auth-client-helpers';
import { log } from '@repo/observability/log';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { mutate } from 'swr';

/**
 * Custom hook for deleting an order
 * This uses direct client -> core API communication
 */
export function useDeleteOrder() {
  const { jwt } = useJwtToken();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const deleteOrder = async (id: string): Promise<void> => {
    if (!jwt) {
      setError(new Error('Authentication required'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await clientFetchWithAuth(`${ApiEndpoint.ORDERS}/${id}`, jwt, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      log.info('Order deleted successfully', { id });

      // Navigate back to orders list
      router.push('/orders');

      mutate(
        (url) => typeof url === 'string' && url.startsWith(ApiEndpoint.ORDERS)
      );
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to delete order');
      log.warn('Failed to delete order in core backend', { error });
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    deleteOrder,
    isLoading,
    error,
  };
}
