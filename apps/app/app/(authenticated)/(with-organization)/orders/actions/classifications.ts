'use server';

import { classificationCodesService } from '../schemas/classifications';

/**
 * Server action to search for classifications
 * This allows us to dynamically load classifications based on search criteria
 * instead of loading all at once on the client
 */
export async function searchClassifications(searchTerm: string) {
  // If search term is empty, return an empty array
  // This prevents loading all classifications when no search term is provided
  if (!searchTerm || searchTerm.trim() === '') {
    return [];
  }

  const allCodes = classificationCodesService.getAllCodes();
  const lowerSearchTerm = searchTerm.toLowerCase();

  // Filter classifications based on search term
  return allCodes.filter(
    (classification) =>
      classification.Description.toLowerCase().includes(lowerSearchTerm) ||
      classification.Code.includes(lowerSearchTerm)
  );
}

/**
 * Get classifications by codes
 * This is useful for fetching specific classifications by their codes
 */
export async function getClassificationsByCodes(codes: string[]) {
  if (!codes || codes.length === 0) {
    return [];
  }

  const classifications = codes
    .map((code) => classificationCodesService.getByCode(code))
    .filter(Boolean);

  return classifications;
}

/**
 * Get classifications by category
 * This allows us to load classifications by category without loading all classifications
 */
export async function getClassificationsByCategory(categoryId: string) {
  if (!categoryId) {
    return [];
  }

  // Import here to avoid circular dependencies
  const { classificationCategories } = await import(
    '../schemas/classification-categories'
  );

  // Find the category
  const category = classificationCategories.find(
    (cat) => cat.id === categoryId
  );
  if (!category) {
    return [];
  }

  // Get classifications for the category codes
  const classifications = category.codes
    .map((code) => classificationCodesService.getByCode(code))
    .filter(Boolean);

  return classifications;
}
