'use server';

import { unitTypesService } from '../schemas/unit-types';

/**
 * Server action to search for unit types
 * This allows us to dynamically load unit types based on search criteria
 * instead of loading all at once on the client
 */
export async function searchUnitTypes(searchTerm: string) {
  // Get all unit types from the service
  const allUnitTypes = unitTypesService.getAllTypes();

  // If search term is empty, return an empty array
  // This prevents loading all units when no search term is provided
  if (!searchTerm || searchTerm.trim() === '') {
    return [];
  }

  // Filter unit types based on search term
  const filteredUnitTypes = allUnitTypes.filter(
    (unit) =>
      unit.Name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      unit.Code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Return the filtered results
  return filteredUnitTypes;
}

/**
 * Get unit types by codes
 * This is useful for fetching specific unit types by their codes
 */
export async function getUnitTypesByCodes(codes: string[]) {
  if (!codes || codes.length === 0) {
    return [];
  }

  const unitTypes = codes
    .map((code) => unitTypesService.getByCode(code))
    .filter(Boolean);
  return unitTypes;
}
