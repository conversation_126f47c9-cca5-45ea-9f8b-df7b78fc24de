import { unitTypesService } from '../schemas/unit-types';

// Common keywords that might appear in product descriptions and their associated unit codes
const KEYWORD_TO_UNIT_MAP: Record<string, string[]> = {
  // Weight-related keywords
  kg: ['KGM'], // kilogram
  kilogram: ['KGM'],
  gram: ['GRM'],
  g: ['GRM'],
  ton: ['TNE'], // metric ton
  tonne: ['TNE'],
  pound: ['LBR'],
  lb: ['LBR'],
  ounce: ['ONZ'],
  oz: ['ONZ'],

  // Volume-related keywords
  liter: ['LTR'],
  litre: ['LTR'],
  l: ['LTR'],
  ml: ['MLT'], // milliliter
  milliliter: ['MLT'],
  gallon: ['GLL'],
  cubic: ['MTQ', 'CMQ', 'DMQ'], // cubic meter, cubic centimeter, cubic decimeter

  // Length-related keywords
  meter: ['MTR'],
  metre: ['MTR'],
  m: ['MTR'],
  cm: ['CMT'], // centimeter
  centimeter: ['CMT'],
  mm: ['MMT'], // millimeter
  millimeter: ['MMT'],
  inch: ['INH'],
  foot: ['FOT'],
  feet: ['FOT'],
  yard: ['YRD'],

  // Area-related keywords
  square: ['MTK', 'CMK'], // square meter, square centimeter
  sqm: ['MTK'],
  sq: ['MTK', 'CMK'],

  // Time-related keywords
  hour: ['HUR'],
  hr: ['HUR'],
  day: ['DAY'],
  week: ['WEE'],
  month: ['MON'],
  year: ['ANN'],
  minute: ['MIN'],
  min: ['MIN'],
  second: ['SEC'],
  sec: ['SEC'],

  // Count-related keywords
  piece: ['PCE', 'PCS'],
  pc: ['PCE'],
  pcs: ['PCS'],
  each: ['EA'],
  unit: ['EA', 'PCE'],
  item: ['EA', 'PCE'],
  set: ['SET'],
  pair: ['PR'],
  dozen: ['DZN'],
  box: ['BOX'],
  carton: ['CTN'],
  pack: ['PK', 'PKG'],
  package: ['PKG'],
  bag: ['BAG'],
  bottle: ['BO'],
  can: ['CAN'],
  container: ['X1F'],
  pallet: ['PAL'],
  roll: ['RO'],
  sheet: ['SHT'],
  bundle: ['BE'],
  batch: ['BH'],
  lot: ['LOT'],

  // Service-related keywords
  service: ['HUR', 'DAY', 'MON'],
  consultation: ['HUR'],
  session: ['HUR', 'DAY'],
  subscription: ['MON', 'ANN'],
  license: ['MON', 'ANN'],
  rental: ['DAY', 'MON'],
  lease: ['MON', 'ANN'],

  // Energy-related keywords
  watt: ['WTT'],
  kilowatt: ['KWT'],
  kw: ['KWT'],
  kwh: ['KWH'], // kilowatt hour
  energy: ['KWH', 'MWH'],

  // Digital-related keywords
  byte: ['BYT'],
  kilobyte: ['KBT'],
  kb: ['KBT'],
  megabyte: ['MBT'],
  mb: ['MBT'],
  gigabyte: ['GBT'],
  gb: ['GBT'],
  terabyte: ['TBT'],
  tb: ['TBT'],
};

/**
 * Suggests unit codes based on the product description
 * @param description The product description
 * @returns An array of suggested unit codes
 */
export function suggestUnitCodes(description: string): string[] {
  if (!description) {
    return [];
  }

  const normalizedDescription = description.toLowerCase();
  const suggestedCodes = new Set<string>();

  // Check for keywords in the description
  for (const [keyword, codes] of Object.entries(KEYWORD_TO_UNIT_MAP)) {
    if (normalizedDescription.includes(keyword)) {
      for (const code of codes) {
        suggestedCodes.add(code);
      }
    }
  }

  // Convert to array and ensure all codes exist in our unit types service
  return Array.from(suggestedCodes).filter(
    (code) => unitTypesService.getByCode(code) !== undefined
  );
}
