import type { Order } from '@/app/types';
import {
  <PERSON><PERSON><PERSON>,
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@repo/design-system/components/ui/page-header';
import { log } from '@repo/observability/log';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getOrders } from './actions';
import { columns } from './components/order-column';
import { OrderDialog } from './components/order-dialog';
import { OrderTable } from './components/order-table';

const title = 'MyInvoice - Orders';
const description = 'MyInvoice - Orders';

export const metadata: Metadata = {
  title,
  description,
};

const OrdersPage = async (): Promise<ReactElement> => {
  let orders: Order[] = [];
  let meta:
    | {
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
        from: number;
        to: number;
      }
    | undefined;

  try {
    const response = await getOrders();
    orders = response.data;

    // Transform the meta data to match the expected format
    if (response.meta) {
      meta = {
        total: response.meta.total,
        per_page: response.meta.per_page,
        current_page: response.meta.current_page,
        last_page: response.meta.last_page,
        from: (response.meta.current_page - 1) * response.meta.per_page + 1,
        to: Math.min(
          response.meta.current_page * response.meta.per_page,
          response.meta.total
        ),
      };
    }
  } catch (error) {
    log.warn('Failed to fetch orders', { error });
  }

  return (
    <>
      <Header page="Orders" />

      <main className="flex-1 space-y-8 px-4 lg:px-8">
        <PageHeader>
          <div>
            <PageHeaderHeading>Orders</PageHeaderHeading>
            <PageHeaderDescription>Orders created by you</PageHeaderDescription>
          </div>
          <PageActions>
            <OrderDialog />
          </PageActions>
        </PageHeader>

        <div className="md:min-h-min">
          <OrderTable
            columns={columns}
            initialData={orders}
            initialMeta={meta}
          />
        </div>
      </main>
    </>
  );
};

export default OrdersPage;
