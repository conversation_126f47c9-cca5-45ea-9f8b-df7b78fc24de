import { z } from 'zod';

export const PaymentModes = {
  getAllTypes: () => [
    {
      code: '01',
      description: 'Cash',
    },
    {
      code: '02',
      description: 'Cheque',
    },
    {
      code: '03',
      description: 'Bank Transfer',
    },
    {
      code: '04',
      description: 'Credit Card',
    },
    {
      code: '05',
      description: 'Debit Card',
    },
    {
      code: '06',
      description: 'E-Wallet',
    },
    {
      code: '07',
      description: 'Digital Bank',
    },
    {
      code: '08',
      description: 'Others',
    },
  ],
  getByCode: (code: string) =>
    PaymentModes.getAllTypes().find((payment) => payment.code === code),
  getAsEnum: () =>
    z.enum(
      PaymentModes.getAllTypes().map((c) => c.code) as [string, ...string[]]
    ),
};
