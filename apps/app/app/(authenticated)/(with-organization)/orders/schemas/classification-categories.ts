import { classificationCodesService } from './classifications';

export type ClassificationCategory = {
  id: string;
  name: string;
  codes: string[];
};

// Group classifications into logical categories
export const classificationCategories: ClassificationCategory[] = [
  {
    id: 'education',
    name: 'Education & Learning',
    codes: ['002', '010', '019', '026'],
  },
  {
    id: 'medical',
    name: 'Medical & Healthcare',
    codes: ['019', '020', '021', '041', '042', '043'],
  },
  {
    id: 'technology',
    name: 'Technology & Electronics',
    codes: ['003', '017', '029'],
  },
  {
    id: 'ecommerce',
    name: 'E-Commerce & Retail',
    codes: ['008', '009', '044'],
  },
  {
    id: 'automotive',
    name: 'Automotive & Transport',
    codes: ['025', '028'],
  },
  {
    id: 'finance',
    name: 'Finance & Insurance',
    codes: ['014', '015', '016', '024', '040'],
  },
  {
    id: 'property',
    name: 'Property & Construction',
    codes: ['005', '018'],
  },
  {
    id: 'self_billed',
    name: 'Self-Billed Transactions',
    codes: ['033', '034', '035', '036', '037', '045'],
  },
  {
    id: 'lifestyle',
    name: 'Lifestyle & Wellness',
    codes: ['001', '013', '038', '039'],
  },
  {
    id: 'business',
    name: 'Business Operations',
    codes: ['004', '006', '011', '012', '027', '030', '031', '032'],
  },
  {
    id: 'other',
    name: 'Other',
    codes: ['007', '022', '023'],
  },
];

// Get all classifications with their categories
export const getClassificationsWithCategories = () => {
  const allCodes = classificationCodesService.getAllCodes();

  return allCodes.map((classification) => {
    const category = classificationCategories.find((cat) =>
      cat.codes.includes(classification.Code)
    );

    return {
      ...classification,
      Category: category?.name || 'Other',
      CategoryId: category?.id || 'other',
    };
  });
};

// Get classifications by category
export const getClassificationsByCategory = (categoryId: string) => {
  const allClassifications = getClassificationsWithCategories();
  return allClassifications.filter(
    (classification) => classification.CategoryId === categoryId
  );
};

// Get frequently used classifications (this would ideally be personalized per user)
export const getFrequentlyUsedClassifications = () => {
  // TODO: This is a placeholder - in a real implementation, this would be based on user history
  return [
    classificationCodesService.getByCode('003'), // Computer, smartphone or tablet
    classificationCodesService.getByCode('008'), // e-Commerce - e-Invoice to buyer / purchaser
    classificationCodesService.getByCode('022'), // Others
    classificationCodesService.getByCode('030'), // Repair and maintenance
    classificationCodesService.getByCode('044'), // Vouchers, gift cards, loyalty points, etc
  ].filter(Boolean);
};

// Get classifications by search term
export const searchClassifications = (searchTerm: string) => {
  if (!searchTerm) {
    return [];
  }

  const allCodes = classificationCodesService.getAllCodes();
  const lowerSearchTerm = searchTerm.toLowerCase();

  return allCodes.filter(
    (classification) =>
      classification.Description.toLowerCase().includes(lowerSearchTerm) ||
      classification.Code.includes(lowerSearchTerm)
  );
};

// Get classifications by business type (this would be based on onboarding data)
export const getClassificationsByBusinessType = (businessType: string) => {
  // TODO: This is a placeholder - in a real implementation, this would be based on business type
  switch (businessType) {
    case 'retail':
      return (
        classificationCategories.find((cat) => cat.id === 'ecommerce')?.codes ||
        []
      );
    case 'healthcare':
      return (
        classificationCategories.find((cat) => cat.id === 'medical')?.codes ||
        []
      );
    case 'education':
      return (
        classificationCategories.find((cat) => cat.id === 'education')?.codes ||
        []
      );
    case 'technology':
      return (
        classificationCategories.find((cat) => cat.id === 'technology')
          ?.codes || []
      );
    default:
      return [];
  }
};
