import { z } from 'zod';

export const taxTypesService = {
  getAllTypes: () => [
    {
      code: '01',
      description: 'Sales Tax',
    },
    {
      code: '02',
      description: 'Service Tax',
    },
    {
      code: '03',
      description: 'Tourism Tax',
    },
    {
      code: '04',
      description: 'High-Value Goods Tax',
    },
    {
      code: '05',
      description: 'Sales Tax on Low Value Goods',
    },
    {
      code: '06',
      description: 'Not Applicable',
    },
    {
      code: 'E',
      description: 'Tax Exemption',
    },
  ],
  getByCode: (code: string) =>
    taxTypesService.getAllTypes().find((tax) => tax.code === code),
  getAsEnum: () =>
    z.enum(
      taxTypesService.getAllTypes().map((c) => c.code) as [string, ...string[]]
    ),
};
