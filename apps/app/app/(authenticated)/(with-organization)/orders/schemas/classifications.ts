import { z } from 'zod';

export const classificationCodesService = {
  getAllCodes: () => [
    {
      Code: '001',
      Description: 'Breastfeeding equipment ',
    },
    {
      Code: '002',
      Description: 'Child care centres and kindergartens fees',
    },
    {
      Code: '003',
      Description: 'Computer, smartphone or tablet',
    },
    {
      Code: '004',
      Description: 'Consolidated e-Invoice ',
    },
    {
      Code: '005',
      Description:
        'Construction materials (as specified under Fourth Schedule of the Lembaga Pembangunan Industri Pembinaan Malaysia Act 1994)',
    },
    {
      Code: '006',
      Description: 'Disbursement ',
    },
    {
      Code: '007',
      Description: 'Donation',
    },
    {
      Code: '008',
      Description: 'e-Commerce - e-Invoice to buyer / purchaser',
    },
    {
      Code: '009',
      Description:
        'e-Commerce - Self-billed e-Invoice to seller, logistics, etc. ',
    },
    {
      Code: '010',
      Description: 'Education fees',
    },
    {
      Code: '011',
      Description: 'Goods on consignment (Consignor)',
    },
    {
      Code: '012',
      Description: 'Goods on consignment (Consignee)',
    },
    {
      Code: '013',
      Description: 'Gym membership',
    },
    {
      Code: '014',
      Description: 'Insurance - Education and medical benefits',
    },
    {
      Code: '015',
      Description: 'Insurance - Takaful or life insurance',
    },
    {
      Code: '016',
      Description: 'Interest and financing expenses',
    },
    {
      Code: '017',
      Description: 'Internet subscription ',
    },
    {
      Code: '018',
      Description: 'Land and building',
    },
    {
      Code: '019',
      Description:
        'Medical examination for learning disabilities and early intervention or rehabilitation treatments of learning disabilities',
    },
    {
      Code: '020',
      Description: 'Medical examination or vaccination expenses',
    },
    {
      Code: '021',
      Description: 'Medical expenses for serious diseases',
    },
    {
      Code: '022',
      Description: 'Others',
    },
    {
      Code: '023',
      Description:
        'Petroleum operations (as defined in Petroleum (Income Tax) Act 1967)',
    },
    {
      Code: '024',
      Description: 'Private retirement scheme or deferred annuity scheme ',
    },
    {
      Code: '025',
      Description: 'Motor vehicle',
    },
    {
      Code: '026',
      Description:
        'Subscription of books / journals / magazines / newspapers / other similar publications',
    },
    {
      Code: '027',
      Description: 'Reimbursement ',
    },
    {
      Code: '028',
      Description: 'Rental of motor vehicle',
    },
    {
      Code: '029',
      Description:
        'EV charging facilities (Installation, rental, sale / purchase or subscription fees) ',
    },
    {
      Code: '030',
      Description: 'Repair and maintenance',
    },
    {
      Code: '031',
      Description: 'Research and development ',
    },
    {
      Code: '032',
      Description: 'Foreign income ',
    },
    {
      Code: '033',
      Description: 'Self-billed - Betting and gaming ',
    },
    {
      Code: '034',
      Description: 'Self-billed - Importation of goods ',
    },
    {
      Code: '035',
      Description: 'Self-billed - Importation of services',
    },
    {
      Code: '036',
      Description: 'Self-billed - Others',
    },
    {
      Code: '037',
      Description:
        'Self-billed - Monetary payment to agents, dealers or distributors ',
    },
    {
      Code: '038',
      Description:
        'Sports equipment, rental / entry fees for sports facilities, registration in sports competition or sports training fees imposed by associations / sports clubs / companies registered with the Sports Commissioner or Companies Commission of Malaysia and carrying out sports activities as listed under the Sports Development Act 1997',
    },
    {
      Code: '039',
      Description: 'Supporting equipment for disabled person',
    },
    {
      Code: '040',
      Description: 'Voluntary contribution to approved provident fund ',
    },
    {
      Code: '041',
      Description: 'Dental examination or treatment',
    },
    {
      Code: '042',
      Description: 'Fertility treatment',
    },
    {
      Code: '043',
      Description:
        'Treatment and home care nursing, daycare centres and residential care centers',
    },
    {
      Code: '044',
      Description: 'Vouchers, gift cards, loyalty points, etc',
    },
    {
      Code: '045',
      Description:
        'Self-billed - Non-monetary payment to agents, dealers or distributors',
    },
  ],
  getByCode: (code: string) =>
    classificationCodesService.getAllCodes().find((c) => c.Code === code),
  getAsEnum: () =>
    z.enum(
      classificationCodesService.getAllCodes().map((c) => c.Code) as [
        string,
        ...string[],
      ]
    ),
};
