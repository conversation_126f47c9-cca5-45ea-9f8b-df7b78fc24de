import { z } from 'zod';

export const unitTypesService = {
  getAllTypes: () => [
    {
      Code: '10',
      Name: 'group',
    },
    {
      Code: '11',
      Name: 'outfit',
    },
    {
      Code: '13',
      Name: 'ration',
    },
    {
      Code: '14',
      Name: 'shot',
    },
    {
      Code: '15',
      Name: 'stick, military',
    },
    {
      Code: '1I',
      Name: 'fixed rate',
    },
    {
      Code: '20',
      Name: 'twenty foot container',
    },
    {
      Code: '21',
      Name: 'forty foot container',
    },
    {
      Code: '22',
      Name: 'decilitre per gram',
    },
    {
      Code: '23',
      Name: 'gram per cubic centimetre',
    },
    {
      Code: '24',
      Name: 'theoretical pound',
    },
    {
      Code: '25',
      Name: 'gram per square centimetre',
    },
    {
      Code: '27',
      Name: 'theoretical ton',
    },
    {
      Code: '28',
      Name: 'kilogram per square metre',
    },
    {
      Code: '2A',
      Name: 'radian per second',
    },
    {
      Code: '2B',
      Name: 'radian per second squared',
    },
    {
      Code: '2C',
      Name: 'roentgen',
    },
    {
      Code: '2G',
      Name: 'volt AC',
    },
    {
      Code: '2H',
      Name: 'volt DC',
    },
    {
      Code: '2I',
      Name: 'British thermal unit (international table) per hour',
    },
    {
      Code: '2J',
      Name: 'cubic centimetre per second',
    },
    {
      Code: '2K',
      Name: 'cubic foot per hour',
    },
    {
      Code: '2L',
      Name: 'cubic foot per minute',
    },
    {
      Code: '2M',
      Name: 'centimetre per second',
    },
    {
      Code: '2N',
      Name: 'decibel',
    },
    {
      Code: '2P',
      Name: 'kilobyte',
    },
    {
      Code: '2Q',
      Name: 'kilobecquerel',
    },
    {
      Code: '2R',
      Name: 'kilocurie',
    },
    {
      Code: '2U',
      Name: 'megagram',
    },
    {
      Code: '2X',
      Name: 'metre per minute',
    },
    {
      Code: '2Y',
      Name: 'milliroentgen',
    },
    {
      Code: '2Z',
      Name: 'millivolt',
    },
    {
      Code: '33',
      Name: 'kilopascal square metre per gram',
    },
    {
      Code: '34',
      Name: 'kilopascal per millimetre',
    },
    {
      Code: '35',
      Name: 'millilitre per square centimetre second',
    },
    {
      Code: '37',
      Name: 'ounce per square foot',
    },
    {
      Code: '38',
      Name: 'ounce per square foot per 0,01inch',
    },
    {
      Code: '3B',
      Name: 'megajoule',
    },
    {
      Code: '3C',
      Name: 'manmonth',
    },
    {
      Code: '40',
      Name: 'millilitre per second',
    },
    {
      Code: '41',
      Name: 'millilitre per minute',
    },
    {
      Code: '4C',
      Name: 'centistokes',
    },
    {
      Code: '4G',
      Name: 'microlitre',
    },
    {
      Code: '4H',
      Name: 'micrometre (micron)',
    },
    {
      Code: '4K',
      Name: 'milliampere',
    },
    {
      Code: '4L',
      Name: 'megabyte',
    },
    {
      Code: '4M',
      Name: 'milligram per hour',
    },
    {
      Code: '4N',
      Name: 'megabecquerel',
    },
    {
      Code: '4O',
      Name: 'microfarad',
    },
    {
      Code: '4P',
      Name: 'newton per metre',
    },
    {
      Code: '4Q',
      Name: 'ounce inch',
    },
    {
      Code: '4R',
      Name: 'ounce foot',
    },
    {
      Code: '4T',
      Name: 'picofarad',
    },
    {
      Code: '4U',
      Name: 'pound per hour',
    },
    {
      Code: '4W',
      Name: 'ton (US) per hour',
    },
    {
      Code: '4X',
      Name: 'kilolitre per hour',
    },
    {
      Code: '56',
      Name: 'sitas',
    },
    {
      Code: '57',
      Name: 'mesh',
    },
    {
      Code: '58',
      Name: 'net kilogram',
    },
    {
      Code: '59',
      Name: 'part per million',
    },
    {
      Code: '5A',
      Name: 'barrel (US) per minute',
    },
    {
      Code: '5B',
      Name: 'batch',
    },
    {
      Code: '5E',
      Name: 'MMSCF/day',
    },
    {
      Code: '5J',
      Name: 'hydraulic horse power',
    },
    {
      Code: '60',
      Name: 'percent weight',
    },
    {
      Code: '61',
      Name: 'part per billion (US)',
    },
    {
      Code: '74',
      Name: 'millipascal',
    },
    {
      Code: '77',
      Name: 'milli-inch',
    },
    {
      Code: '80',
      Name: 'pound per square inch absolute',
    },
    {
      Code: '81',
      Name: 'henry',
    },
    {
      Code: '85',
      Name: 'foot pound-force',
    },
    {
      Code: '87',
      Name: 'pound per cubic foot',
    },
    {
      Code: '89',
      Name: 'poise',
    },
    {
      Code: '91',
      Name: 'stokes',
    },
    {
      Code: 'A10',
      Name: 'ampere square metre per joule second',
    },
    {
      Code: 'A11',
      Name: 'angstrom',
    },
    {
      Code: 'A12',
      Name: 'astronomical unit',
    },
    {
      Code: 'A13',
      Name: 'attojoule',
    },
    {
      Code: 'A14',
      Name: 'barn',
    },
    {
      Code: 'A15',
      Name: 'barn per electronvolt',
    },
    {
      Code: 'A16',
      Name: 'barn per steradian electronvolt',
    },
    {
      Code: 'A17',
      Name: 'barn per steradian',
    },
    {
      Code: 'A18',
      Name: 'becquerel per kilogram',
    },
    {
      Code: 'A19',
      Name: 'becquerel per cubic metre',
    },
    {
      Code: 'A2',
      Name: 'ampere per centimetre',
    },
    {
      Code: 'A20',
      Name: 'British thermal unit (international table) per second square foot degree\n\t\t\t\tRankine',
    },
    {
      Code: 'A21',
      Name: 'British thermal unit (international table) per pound degree Rankine',
    },
    {
      Code: 'A22',
      Name: 'British thermal unit (international table) per second foot degree Rankine',
    },
    {
      Code: 'A23',
      Name: 'British thermal unit (international table) per hour square foot degree Rankine',
    },
    {
      Code: 'A24',
      Name: 'candela per square metre',
    },
    {
      Code: 'A26',
      Name: 'coulomb metre',
    },
    {
      Code: 'A27',
      Name: 'coulomb metre squared per volt',
    },
    {
      Code: 'A28',
      Name: 'coulomb per cubic centimetre',
    },
    {
      Code: 'A29',
      Name: 'coulomb per cubic metre',
    },
    {
      Code: 'A3',
      Name: 'ampere per millimetre',
    },
    {
      Code: 'A30',
      Name: 'coulomb per cubic millimetre',
    },
    {
      Code: 'A31',
      Name: 'coulomb per kilogram second',
    },
    {
      Code: 'A32',
      Name: 'coulomb per mole',
    },
    {
      Code: 'A33',
      Name: 'coulomb per square centimetre',
    },
    {
      Code: 'A34',
      Name: 'coulomb per square metre',
    },
    {
      Code: 'A35',
      Name: 'coulomb per square millimetre',
    },
    {
      Code: 'A36',
      Name: 'cubic centimetre per mole',
    },
    {
      Code: 'A37',
      Name: 'cubic decimetre per mole',
    },
    {
      Code: 'A38',
      Name: 'cubic metre per coulomb',
    },
    {
      Code: 'A39',
      Name: 'cubic metre per kilogram',
    },
    {
      Code: 'A4',
      Name: 'ampere per square centimetre',
    },
    {
      Code: 'A40',
      Name: 'cubic metre per mole',
    },
    {
      Code: 'A41',
      Name: 'ampere per square metre',
    },
    {
      Code: 'A42',
      Name: 'curie per kilogram',
    },
    {
      Code: 'A43',
      Name: 'deadweight tonnage',
    },
    {
      Code: 'A44',
      Name: 'decalitre',
    },
    {
      Code: 'A45',
      Name: 'decametre',
    },
    {
      Code: 'A47',
      Name: 'decitex',
    },
    {
      Code: 'A48',
      Name: 'degree Rankine',
    },
    {
      Code: 'A49',
      Name: 'denier',
    },
    {
      Code: 'A5',
      Name: 'ampere square metre',
    },
    {
      Code: 'A53',
      Name: 'electronvolt',
    },
    {
      Code: 'A54',
      Name: 'electronvolt per metre',
    },
    {
      Code: 'A55',
      Name: 'electronvolt square metre',
    },
    {
      Code: 'A56',
      Name: 'electronvolt square metre per kilogram',
    },
    {
      Code: 'A59',
      Name: '8-part cloud cover',
    },
    {
      Code: 'A6',
      Name: 'ampere per square metre kelvin squared',
    },
    {
      Code: 'A68',
      Name: 'exajoule',
    },
    {
      Code: 'A69',
      Name: 'farad per metre',
    },
    {
      Code: 'A7',
      Name: 'ampere per square millimetre',
    },
    {
      Code: 'A70',
      Name: 'femtojoule',
    },
    {
      Code: 'A71',
      Name: 'femtometre',
    },
    {
      Code: 'A73',
      Name: 'foot per second squared',
    },
    {
      Code: 'A74',
      Name: 'foot pound-force per second',
    },
    {
      Code: 'A75',
      Name: 'freight ton',
    },
    {
      Code: 'A76',
      Name: 'gal',
    },
    {
      Code: 'A8',
      Name: 'ampere second',
    },
    {
      Code: 'A84',
      Name: 'gigacoulomb per cubic metre',
    },
    {
      Code: 'A85',
      Name: 'gigaelectronvolt',
    },
    {
      Code: 'A86',
      Name: 'gigahertz',
    },
    {
      Code: 'A87',
      Name: 'gigaohm',
    },
    {
      Code: 'A88',
      Name: 'gigaohm metre',
    },
    {
      Code: 'A89',
      Name: 'gigapascal',
    },
    {
      Code: 'A9',
      Name: 'rate',
    },
    {
      Code: 'A90',
      Name: 'gigawatt',
    },
    {
      Code: 'A91',
      Name: 'gon',
    },
    {
      Code: 'A93',
      Name: 'gram per cubic metre',
    },
    {
      Code: 'A94',
      Name: 'gram per mole',
    },
    {
      Code: 'A95',
      Name: 'gray',
    },
    {
      Code: 'A96',
      Name: 'gray per second',
    },
    {
      Code: 'A97',
      Name: 'hectopascal',
    },
    {
      Code: 'A98',
      Name: 'henry per metre',
    },
    {
      Code: 'A99',
      Name: 'bit',
    },
    {
      Code: 'AA',
      Name: 'ball',
    },
    {
      Code: 'AB',
      Name: 'bulk pack',
    },
    {
      Code: 'ACR',
      Name: 'acre',
    },
    {
      Code: 'ACT',
      Name: 'activity',
    },
    {
      Code: 'AD',
      Name: 'byte',
    },
    {
      Code: 'AE',
      Name: 'ampere per metre',
    },
    {
      Code: 'AH',
      Name: 'additional minute',
    },
    {
      Code: 'AI',
      Name: 'average minute per call',
    },
    {
      Code: 'AK',
      Name: 'fathom',
    },
    {
      Code: 'AL',
      Name: 'access line',
    },
    {
      Code: 'AMH',
      Name: 'ampere hour',
    },
    {
      Code: 'AMP',
      Name: 'ampere',
    },
    {
      Code: 'ANN',
      Name: 'year',
    },
    {
      Code: 'APZ',
      Name: 'troy ounce or apothecary ounce',
    },
    {
      Code: 'AQ',
      Name: 'anti-hemophilic factor (AHF) unit',
    },
    {
      Code: 'AS',
      Name: 'assortment',
    },
    {
      Code: 'ASM',
      Name: 'alcoholic strength by mass',
    },
    {
      Code: 'ASU',
      Name: 'alcoholic strength by volume',
    },
    {
      Code: 'ATM',
      Name: 'standard atmosphere',
    },
    {
      Code: 'AWG',
      Name: 'american wire gauge',
    },
    {
      Code: 'AY',
      Name: 'assembly',
    },
    {
      Code: 'AZ',
      Name: 'British thermal unit (international table) per pound',
    },
    {
      Code: 'B1',
      Name: 'barrel (US) per day',
    },
    {
      Code: 'B10',
      Name: 'bit per second',
    },
    {
      Code: 'B11',
      Name: 'joule per kilogram kelvin',
    },
    {
      Code: 'B12',
      Name: 'joule per metre',
    },
    {
      Code: 'B13',
      Name: 'joule per square metre',
    },
    {
      Code: 'B14',
      Name: 'joule per metre to the fourth power',
    },
    {
      Code: 'B15',
      Name: 'joule per mole',
    },
    {
      Code: 'B16',
      Name: 'joule per mole kelvin',
    },
    {
      Code: 'B17',
      Name: 'credit',
    },
    {
      Code: 'B18',
      Name: 'joule second',
    },
    {
      Code: 'B19',
      Name: 'digit',
    },
    {
      Code: 'B20',
      Name: 'joule square metre per kilogram',
    },
    {
      Code: 'B21',
      Name: 'kelvin per watt',
    },
    {
      Code: 'B22',
      Name: 'kiloampere',
    },
    {
      Code: 'B23',
      Name: 'kiloampere per square metre',
    },
    {
      Code: 'B24',
      Name: 'kiloampere per metre',
    },
    {
      Code: 'B25',
      Name: 'kilobecquerel per kilogram',
    },
    {
      Code: 'B26',
      Name: 'kilocoulomb',
    },
    {
      Code: 'B27',
      Name: 'kilocoulomb per cubic metre',
    },
    {
      Code: 'B28',
      Name: 'kilocoulomb per square metre',
    },
    {
      Code: 'B29',
      Name: 'kiloelectronvolt',
    },
    {
      Code: 'B3',
      Name: 'batting pound',
    },
    {
      Code: 'B30',
      Name: 'gibibit',
    },
    {
      Code: 'B31',
      Name: 'kilogram metre per second',
    },
    {
      Code: 'B32',
      Name: 'kilogram metre squared',
    },
    {
      Code: 'B33',
      Name: 'kilogram metre squared per second',
    },
    {
      Code: 'B34',
      Name: 'kilogram per cubic decimetre',
    },
    {
      Code: 'B35',
      Name: 'kilogram per litre',
    },
    {
      Code: 'B4',
      Name: 'barrel, imperial',
    },
    {
      Code: 'B41',
      Name: 'kilojoule per kelvin',
    },
    {
      Code: 'B42',
      Name: 'kilojoule per kilogram',
    },
    {
      Code: 'B43',
      Name: 'kilojoule per kilogram kelvin',
    },
    {
      Code: 'B44',
      Name: 'kilojoule per mole',
    },
    {
      Code: 'B45',
      Name: 'kilomole',
    },
    {
      Code: 'B46',
      Name: 'kilomole per cubic metre',
    },
    {
      Code: 'B47',
      Name: 'kilonewton',
    },
    {
      Code: 'B48',
      Name: 'kilonewton metre',
    },
    {
      Code: 'B49',
      Name: 'kiloohm',
    },
    {
      Code: 'B50',
      Name: 'kiloohm metre',
    },
    {
      Code: 'B52',
      Name: 'kilosecond',
    },
    {
      Code: 'B53',
      Name: 'kilosiemens',
    },
    {
      Code: 'B54',
      Name: 'kilosiemens per metre',
    },
    {
      Code: 'B55',
      Name: 'kilovolt per metre',
    },
    {
      Code: 'B56',
      Name: 'kiloweber per metre',
    },
    {
      Code: 'B57',
      Name: 'light year',
    },
    {
      Code: 'B58',
      Name: 'litre per mole',
    },
    {
      Code: 'B59',
      Name: 'lumen hour',
    },
    {
      Code: 'B60',
      Name: 'lumen per square metre',
    },
    {
      Code: 'B61',
      Name: 'lumen per watt',
    },
    {
      Code: 'B62',
      Name: 'lumen second',
    },
    {
      Code: 'B63',
      Name: 'lux hour',
    },
    {
      Code: 'B64',
      Name: 'lux second',
    },
    {
      Code: 'B66',
      Name: 'megaampere per square metre',
    },
    {
      Code: 'B67',
      Name: 'megabecquerel per kilogram',
    },
    {
      Code: 'B68',
      Name: 'gigabit',
    },
    {
      Code: 'B69',
      Name: 'megacoulomb per cubic metre',
    },
    {
      Code: 'B7',
      Name: 'cycle',
    },
    {
      Code: 'B70',
      Name: 'megacoulomb per square metre',
    },
    {
      Code: 'B71',
      Name: 'megaelectronvolt',
    },
    {
      Code: 'B72',
      Name: 'megagram per cubic metre',
    },
    {
      Code: 'B73',
      Name: 'meganewton',
    },
    {
      Code: 'B74',
      Name: 'meganewton metre',
    },
    {
      Code: 'B75',
      Name: 'megaohm',
    },
    {
      Code: 'B76',
      Name: 'megaohm metre',
    },
    {
      Code: 'B77',
      Name: 'megasiemens per metre',
    },
    {
      Code: 'B78',
      Name: 'megavolt',
    },
    {
      Code: 'B79',
      Name: 'megavolt per metre',
    },
    {
      Code: 'B8',
      Name: 'joule per cubic metre',
    },
    {
      Code: 'B80',
      Name: 'gigabit per second',
    },
    {
      Code: 'B81',
      Name: 'reciprocal metre squared reciprocal second',
    },
    {
      Code: 'B82',
      Name: 'inch per linear foot',
    },
    {
      Code: 'B83',
      Name: 'metre to the fourth power',
    },
    {
      Code: 'B84',
      Name: 'microampere',
    },
    {
      Code: 'B85',
      Name: 'microbar',
    },
    {
      Code: 'B86',
      Name: 'microcoulomb',
    },
    {
      Code: 'B87',
      Name: 'microcoulomb per cubic metre',
    },
    {
      Code: 'B88',
      Name: 'microcoulomb per square metre',
    },
    {
      Code: 'B89',
      Name: 'microfarad per metre',
    },
    {
      Code: 'B90',
      Name: 'microhenry',
    },
    {
      Code: 'B91',
      Name: 'microhenry per metre',
    },
    {
      Code: 'B92',
      Name: 'micronewton',
    },
    {
      Code: 'B93',
      Name: 'micronewton metre',
    },
    {
      Code: 'B94',
      Name: 'microohm',
    },
    {
      Code: 'B95',
      Name: 'microohm metre',
    },
    {
      Code: 'B96',
      Name: 'micropascal',
    },
    {
      Code: 'B97',
      Name: 'microradian',
    },
    {
      Code: 'B98',
      Name: 'microsecond',
    },
    {
      Code: 'B99',
      Name: 'microsiemens',
    },
    {
      Code: 'BAR',
      Name: 'bar [unit of pressure]',
    },
    {
      Code: 'BB',
      Name: 'base box',
    },
    {
      Code: 'BFT',
      Name: 'board foot',
    },
    {
      Code: 'BHP',
      Name: 'brake horse power',
    },
    {
      Code: 'BIL',
      Name: 'billion (EUR)',
    },
    {
      Code: 'BLD',
      Name: 'dry barrel (US)',
    },
    {
      Code: 'BLL',
      Name: 'barrel (US)',
    },
    {
      Code: 'BP',
      Name: 'hundred board foot',
    },
    {
      Code: 'BPM',
      Name: 'beats per minute',
    },
    {
      Code: 'BQL',
      Name: 'becquerel',
    },
    {
      Code: 'BTU',
      Name: 'British thermal unit (international table)',
    },
    {
      Code: 'BUA',
      Name: 'bushel (US)',
    },
    {
      Code: 'BUI',
      Name: 'bushel (UK)',
    },
    {
      Code: 'C0',
      Name: 'call',
    },
    {
      Code: 'C10',
      Name: 'millifarad',
    },
    {
      Code: 'C11',
      Name: 'milligal',
    },
    {
      Code: 'C12',
      Name: 'milligram per metre',
    },
    {
      Code: 'C13',
      Name: 'milligray',
    },
    {
      Code: 'C14',
      Name: 'millihenry',
    },
    {
      Code: 'C15',
      Name: 'millijoule',
    },
    {
      Code: 'C16',
      Name: 'millimetre per second',
    },
    {
      Code: 'C17',
      Name: 'millimetre squared per second',
    },
    {
      Code: 'C18',
      Name: 'millimole',
    },
    {
      Code: 'C19',
      Name: 'mole per kilogram',
    },
    {
      Code: 'C20',
      Name: 'millinewton',
    },
    {
      Code: 'C21',
      Name: 'kibibit',
    },
    {
      Code: 'C22',
      Name: 'millinewton per metre',
    },
    {
      Code: 'C23',
      Name: 'milliohm metre',
    },
    {
      Code: 'C24',
      Name: 'millipascal second',
    },
    {
      Code: 'C25',
      Name: 'milliradian',
    },
    {
      Code: 'C26',
      Name: 'millisecond',
    },
    {
      Code: 'C27',
      Name: 'millisiemens',
    },
    {
      Code: 'C28',
      Name: 'millisievert',
    },
    {
      Code: 'C29',
      Name: 'millitesla',
    },
    {
      Code: 'C3',
      Name: 'microvolt per metre',
    },
    {
      Code: 'C30',
      Name: 'millivolt per metre',
    },
    {
      Code: 'C31',
      Name: 'milliwatt',
    },
    {
      Code: 'C32',
      Name: 'milliwatt per square metre',
    },
    {
      Code: 'C33',
      Name: 'milliweber',
    },
    {
      Code: 'C34',
      Name: 'mole',
    },
    {
      Code: 'C35',
      Name: 'mole per cubic decimetre',
    },
    {
      Code: 'C36',
      Name: 'mole per cubic metre',
    },
    {
      Code: 'C37',
      Name: 'kilobit',
    },
    {
      Code: 'C38',
      Name: 'mole per litre',
    },
    {
      Code: 'C39',
      Name: 'nanoampere',
    },
    {
      Code: 'C40',
      Name: 'nanocoulomb',
    },
    {
      Code: 'C41',
      Name: 'nanofarad',
    },
    {
      Code: 'C42',
      Name: 'nanofarad per metre',
    },
    {
      Code: 'C43',
      Name: 'nanohenry',
    },
    {
      Code: 'C44',
      Name: 'nanohenry per metre',
    },
    {
      Code: 'C45',
      Name: 'nanometre',
    },
    {
      Code: 'C46',
      Name: 'nanoohm metre',
    },
    {
      Code: 'C47',
      Name: 'nanosecond',
    },
    {
      Code: 'C48',
      Name: 'nanotesla',
    },
    {
      Code: 'C49',
      Name: 'nanowatt',
    },
    {
      Code: 'C50',
      Name: 'neper',
    },
    {
      Code: 'C51',
      Name: 'neper per second',
    },
    {
      Code: 'C52',
      Name: 'picometre',
    },
    {
      Code: 'C53',
      Name: 'newton metre second',
    },
    {
      Code: 'C54',
      Name: 'newton metre squared per kilogram squared',
    },
    {
      Code: 'C55',
      Name: 'newton per square metre',
    },
    {
      Code: 'C56',
      Name: 'newton per square millimetre',
    },
    {
      Code: 'C57',
      Name: 'newton second',
    },
    {
      Code: 'C58',
      Name: 'newton second per metre',
    },
    {
      Code: 'C59',
      Name: 'octave',
    },
    {
      Code: 'C60',
      Name: 'ohm centimetre',
    },
    {
      Code: 'C61',
      Name: 'ohm metre',
    },
    {
      Code: 'C62',
      Name: 'one',
    },
    {
      Code: 'C63',
      Name: 'parsec',
    },
    {
      Code: 'C64',
      Name: 'pascal per kelvin',
    },
    {
      Code: 'C65',
      Name: 'pascal second',
    },
    {
      Code: 'C66',
      Name: 'pascal second per cubic metre',
    },
    {
      Code: 'C67',
      Name: 'pascal second per metre',
    },
    {
      Code: 'C68',
      Name: 'petajoule',
    },
    {
      Code: 'C69',
      Name: 'phon',
    },
    {
      Code: 'C7',
      Name: 'centipoise',
    },
    {
      Code: 'C70',
      Name: 'picoampere',
    },
    {
      Code: 'C71',
      Name: 'picocoulomb',
    },
    {
      Code: 'C72',
      Name: 'picofarad per metre',
    },
    {
      Code: 'C73',
      Name: 'picohenry',
    },
    {
      Code: 'C74',
      Name: 'kilobit per second',
    },
    {
      Code: 'C75',
      Name: 'picowatt',
    },
    {
      Code: 'C76',
      Name: 'picowatt per square metre',
    },
    {
      Code: 'C78',
      Name: 'pound-force',
    },
    {
      Code: 'C79',
      Name: 'kilovolt ampere hour',
    },
    {
      Code: 'C8',
      Name: 'millicoulomb per kilogram',
    },
    {
      Code: 'C80',
      Name: 'rad',
    },
    {
      Code: 'C81',
      Name: 'radian',
    },
    {
      Code: 'C82',
      Name: 'radian square metre per mole',
    },
    {
      Code: 'C83',
      Name: 'radian square metre per kilogram',
    },
    {
      Code: 'C84',
      Name: 'radian per metre',
    },
    {
      Code: 'C85',
      Name: 'reciprocal angstrom',
    },
    {
      Code: 'C86',
      Name: 'reciprocal cubic metre',
    },
    {
      Code: 'C87',
      Name: 'reciprocal cubic metre per second',
    },
    {
      Code: 'C88',
      Name: 'reciprocal electron volt per cubic metre',
    },
    {
      Code: 'C89',
      Name: 'reciprocal henry',
    },
    {
      Code: 'C9',
      Name: 'coil group',
    },
    {
      Code: 'C90',
      Name: 'reciprocal joule per cubic metre',
    },
    {
      Code: 'C91',
      Name: 'reciprocal kelvin or kelvin to the power minus one',
    },
    {
      Code: 'C92',
      Name: 'reciprocal metre',
    },
    {
      Code: 'C93',
      Name: 'reciprocal square metre',
    },
    {
      Code: 'C94',
      Name: 'reciprocal minute',
    },
    {
      Code: 'C95',
      Name: 'reciprocal mole',
    },
    {
      Code: 'C96',
      Name: 'reciprocal pascal or pascal to the power minus one',
    },
    {
      Code: 'C97',
      Name: 'reciprocal second',
    },
    {
      Code: 'C99',
      Name: 'reciprocal second per metre squared',
    },
    {
      Code: 'CCT',
      Name: 'carrying capacity in metric ton',
    },
    {
      Code: 'CDL',
      Name: 'candela',
    },
    {
      Code: 'CEL',
      Name: 'degree Celsius',
    },
    {
      Code: 'CEN',
      Name: 'hundred',
    },
    {
      Code: 'CG',
      Name: 'card',
    },
    {
      Code: 'CGM',
      Name: 'centigram',
    },
    {
      Code: 'CKG',
      Name: 'coulomb per kilogram',
    },
    {
      Code: 'CLF',
      Name: 'hundred leave',
    },
    {
      Code: 'CLT',
      Name: 'centilitre',
    },
    {
      Code: 'CMK',
      Name: 'square centimetre',
    },
    {
      Code: 'CMQ',
      Name: 'cubic centimetre',
    },
    {
      Code: 'CMT',
      Name: 'centimetre',
    },
    {
      Code: 'CNP',
      Name: 'hundred pack',
    },
    {
      Code: 'CNT',
      Name: 'cental (UK)',
    },
    {
      Code: 'COU',
      Name: 'coulomb',
    },
    {
      Code: 'CTG',
      Name: 'content gram',
    },
    {
      Code: 'CTM',
      Name: 'metric carat',
    },
    {
      Code: 'CTN',
      Name: 'content ton (metric)',
    },
    {
      Code: 'CUR',
      Name: 'curie',
    },
    {
      Code: 'CWA',
      Name: 'hundred pound (cwt) / hundred weight (US)',
    },
    {
      Code: 'CWI',
      Name: 'hundred weight (UK)',
    },
    {
      Code: 'D03',
      Name: 'kilowatt hour per hour',
    },
    {
      Code: 'D04',
      Name: 'lot [unit of weight]',
    },
    {
      Code: 'D1',
      Name: 'reciprocal second per steradian',
    },
    {
      Code: 'D10',
      Name: 'siemens per metre',
    },
    {
      Code: 'D11',
      Name: 'mebibit',
    },
    {
      Code: 'D12',
      Name: 'siemens square metre per mole',
    },
    {
      Code: 'D13',
      Name: 'sievert',
    },
    {
      Code: 'D15',
      Name: 'sone',
    },
    {
      Code: 'D16',
      Name: 'square centimetre per erg',
    },
    {
      Code: 'D17',
      Name: 'square centimetre per steradian erg',
    },
    {
      Code: 'D18',
      Name: 'metre kelvin',
    },
    {
      Code: 'D19',
      Name: 'square metre kelvin per watt',
    },
    {
      Code: 'D2',
      Name: 'reciprocal second per steradian metre squared',
    },
    {
      Code: 'D20',
      Name: 'square metre per joule',
    },
    {
      Code: 'D21',
      Name: 'square metre per kilogram',
    },
    {
      Code: 'D22',
      Name: 'square metre per mole',
    },
    {
      Code: 'D23',
      Name: 'pen gram (protein)',
    },
    {
      Code: 'D24',
      Name: 'square metre per steradian',
    },
    {
      Code: 'D25',
      Name: 'square metre per steradian joule',
    },
    {
      Code: 'D26',
      Name: 'square metre per volt second',
    },
    {
      Code: 'D27',
      Name: 'steradian',
    },
    {
      Code: 'D29',
      Name: 'terahertz',
    },
    {
      Code: 'D30',
      Name: 'terajoule',
    },
    {
      Code: 'D31',
      Name: 'terawatt',
    },
    {
      Code: 'D32',
      Name: 'terawatt hour',
    },
    {
      Code: 'D33',
      Name: 'tesla',
    },
    {
      Code: 'D34',
      Name: 'tex',
    },
    {
      Code: 'D36',
      Name: 'megabit',
    },
    {
      Code: 'D41',
      Name: 'tonne per cubic metre',
    },
    {
      Code: 'D42',
      Name: 'tropical year',
    },
    {
      Code: 'D43',
      Name: 'unified atomic mass unit',
    },
    {
      Code: 'D44',
      Name: 'var',
    },
    {
      Code: 'D45',
      Name: 'volt squared per kelvin squared',
    },
    {
      Code: 'D46',
      Name: 'volt - ampere',
    },
    {
      Code: 'D47',
      Name: 'volt per centimetre',
    },
    {
      Code: 'D48',
      Name: 'volt per kelvin',
    },
    {
      Code: 'D49',
      Name: 'millivolt per kelvin',
    },
    {
      Code: 'D5',
      Name: 'kilogram per square centimetre',
    },
    {
      Code: 'D50',
      Name: 'volt per metre',
    },
    {
      Code: 'D51',
      Name: 'volt per millimetre',
    },
    {
      Code: 'D52',
      Name: 'watt per kelvin',
    },
    {
      Code: 'D53',
      Name: 'watt per metre kelvin',
    },
    {
      Code: 'D54',
      Name: 'watt per square metre',
    },
    {
      Code: 'D55',
      Name: 'watt per square metre kelvin',
    },
    {
      Code: 'D56',
      Name: 'watt per square metre kelvin to the fourth power',
    },
    {
      Code: 'D57',
      Name: 'watt per steradian',
    },
    {
      Code: 'D58',
      Name: 'watt per steradian square metre',
    },
    {
      Code: 'D59',
      Name: 'weber per metre',
    },
    {
      Code: 'D6',
      Name: 'roentgen per second',
    },
    {
      Code: 'D60',
      Name: 'weber per millimetre',
    },
    {
      Code: 'D61',
      Name: 'minute [unit of angle]',
    },
    {
      Code: 'D62',
      Name: 'second [unit of angle]',
    },
    {
      Code: 'D63',
      Name: 'book',
    },
    {
      Code: 'D65',
      Name: 'round',
    },
    {
      Code: 'D68',
      Name: 'number of words',
    },
    {
      Code: 'D69',
      Name: 'inch to the fourth power',
    },
    {
      Code: 'D73',
      Name: 'joule square metre',
    },
    {
      Code: 'D74',
      Name: 'kilogram per mole',
    },
    {
      Code: 'D77',
      Name: 'megacoulomb',
    },
    {
      Code: 'D78',
      Name: 'megajoule per second',
    },
    {
      Code: 'D80',
      Name: 'microwatt',
    },
    {
      Code: 'D81',
      Name: 'microtesla',
    },
    {
      Code: 'D82',
      Name: 'microvolt',
    },
    {
      Code: 'D83',
      Name: 'millinewton metre',
    },
    {
      Code: 'D85',
      Name: 'microwatt per square metre',
    },
    {
      Code: 'D86',
      Name: 'millicoulomb',
    },
    {
      Code: 'D87',
      Name: 'millimole per kilogram',
    },
    {
      Code: 'D88',
      Name: 'millicoulomb per cubic metre',
    },
    {
      Code: 'D89',
      Name: 'millicoulomb per square metre',
    },
    {
      Code: 'D91',
      Name: 'rem',
    },
    {
      Code: 'D93',
      Name: 'second per cubic metre',
    },
    {
      Code: 'D94',
      Name: 'second per cubic metre radian',
    },
    {
      Code: 'D95',
      Name: 'joule per gram',
    },
    {
      Code: 'DAA',
      Name: 'decare',
    },
    {
      Code: 'DAD',
      Name: 'ten day',
    },
    {
      Code: 'DAY',
      Name: 'day',
    },
    {
      Code: 'DB',
      Name: 'dry pound',
    },
    {
      Code: 'DBM',
      Name: 'Decibel-milliwatts',
    },
    {
      Code: 'DBW',
      Name: 'Decibel watt',
    },
    {
      Code: 'DD',
      Name: 'degree [unit of angle]',
    },
    {
      Code: 'DEC',
      Name: 'decade',
    },
    {
      Code: 'DG',
      Name: 'decigram',
    },
    {
      Code: 'DJ',
      Name: 'decagram',
    },
    {
      Code: 'DLT',
      Name: 'decilitre',
    },
    {
      Code: 'DMA',
      Name: 'cubic decametre',
    },
    {
      Code: 'DMK',
      Name: 'square decimetre',
    },
    {
      Code: 'DMO',
      Name: 'standard kilolitre',
    },
    {
      Code: 'DMQ',
      Name: 'cubic decimetre',
    },
    {
      Code: 'DMT',
      Name: 'decimetre',
    },
    {
      Code: 'DN',
      Name: 'decinewton metre',
    },
    {
      Code: 'DPC',
      Name: 'dozen piece',
    },
    {
      Code: 'DPR',
      Name: 'dozen pair',
    },
    {
      Code: 'DPT',
      Name: 'displacement tonnage',
    },
    {
      Code: 'DRA',
      Name: 'dram (US)',
    },
    {
      Code: 'DRI',
      Name: 'dram (UK)',
    },
    {
      Code: 'DRL',
      Name: 'dozen roll',
    },
    {
      Code: 'DT',
      Name: 'dry ton',
    },
    {
      Code: 'DTN',
      Name: 'decitonne',
    },
    {
      Code: 'DWT',
      Name: 'pennyweight',
    },
    {
      Code: 'DZN',
      Name: 'dozen',
    },
    {
      Code: 'DZP',
      Name: 'dozen pack',
    },
    {
      Code: 'E01',
      Name: 'newton per square centimetre',
    },
    {
      Code: 'E07',
      Name: 'megawatt hour per hour',
    },
    {
      Code: 'E08',
      Name: 'megawatt per hertz',
    },
    {
      Code: 'E09',
      Name: 'milliampere hour',
    },
    {
      Code: 'E10',
      Name: 'degree day',
    },
    {
      Code: 'E12',
      Name: 'mille',
    },
    {
      Code: 'E14',
      Name: 'kilocalorie (international table)',
    },
    {
      Code: 'E15',
      Name: 'kilocalorie (thermochemical) per hour',
    },
    {
      Code: 'E16',
      Name: 'million Btu(IT) per hour',
    },
    {
      Code: 'E17',
      Name: 'cubic foot per second',
    },
    {
      Code: 'E18',
      Name: 'tonne per hour',
    },
    {
      Code: 'E19',
      Name: 'ping',
    },
    {
      Code: 'E20',
      Name: 'megabit per second',
    },
    {
      Code: 'E21',
      Name: 'shares',
    },
    {
      Code: 'E22',
      Name: 'TEU',
    },
    {
      Code: 'E23',
      Name: 'tyre',
    },
    {
      Code: 'E25',
      Name: 'active unit',
    },
    {
      Code: 'E27',
      Name: 'dose',
    },
    {
      Code: 'E28',
      Name: 'air dry ton',
    },
    {
      Code: 'E30',
      Name: 'strand',
    },
    {
      Code: 'E31',
      Name: 'square metre per litre',
    },
    {
      Code: 'E32',
      Name: 'litre per hour',
    },
    {
      Code: 'E33',
      Name: 'foot per thousand',
    },
    {
      Code: 'E34',
      Name: 'gigabyte',
    },
    {
      Code: 'E35',
      Name: 'terabyte',
    },
    {
      Code: 'E36',
      Name: 'petabyte',
    },
    {
      Code: 'E37',
      Name: 'pixel',
    },
    {
      Code: 'E38',
      Name: 'megapixel',
    },
    {
      Code: 'E39',
      Name: 'dots per inch',
    },
    {
      Code: 'E4',
      Name: 'gross kilogram',
    },
    {
      Code: 'E40',
      Name: 'part per hundred thousand',
    },
    {
      Code: 'E41',
      Name: 'kilogram-force per square millimetre',
    },
    {
      Code: 'E42',
      Name: 'kilogram-force per square centimetre',
    },
    {
      Code: 'E43',
      Name: 'joule per square centimetre',
    },
    {
      Code: 'E44',
      Name: 'kilogram-force metre per square centimetre',
    },
    {
      Code: 'E45',
      Name: 'milliohm',
    },
    {
      Code: 'E46',
      Name: 'kilowatt hour per cubic metre',
    },
    {
      Code: 'E47',
      Name: 'kilowatt hour per kelvin',
    },
    {
      Code: 'E48',
      Name: 'service unit',
    },
    {
      Code: 'E49',
      Name: 'working day',
    },
    {
      Code: 'E50',
      Name: 'accounting unit',
    },
    {
      Code: 'E51',
      Name: 'job',
    },
    {
      Code: 'E52',
      Name: 'run foot',
    },
    {
      Code: 'E53',
      Name: 'test',
    },
    {
      Code: 'E54',
      Name: 'trip',
    },
    {
      Code: 'E55',
      Name: 'use',
    },
    {
      Code: 'E56',
      Name: 'well',
    },
    {
      Code: 'E57',
      Name: 'zone',
    },
    {
      Code: 'E58',
      Name: 'exabit per second',
    },
    {
      Code: 'E59',
      Name: 'exbibyte',
    },
    {
      Code: 'E60',
      Name: 'pebibyte',
    },
    {
      Code: 'E61',
      Name: 'tebibyte',
    },
    {
      Code: 'E62',
      Name: 'gibibyte',
    },
    {
      Code: 'E63',
      Name: 'mebibyte',
    },
    {
      Code: 'E64',
      Name: 'kibibyte',
    },
    {
      Code: 'E65',
      Name: 'exbibit per metre',
    },
    {
      Code: 'E66',
      Name: 'exbibit per square metre',
    },
    {
      Code: 'E67',
      Name: 'exbibit per cubic metre',
    },
    {
      Code: 'E68',
      Name: 'gigabyte per second',
    },
    {
      Code: 'E69',
      Name: 'gibibit per metre',
    },
    {
      Code: 'E70',
      Name: 'gibibit per square metre',
    },
    {
      Code: 'E71',
      Name: 'gibibit per cubic metre',
    },
    {
      Code: 'E72',
      Name: 'kibibit per metre',
    },
    {
      Code: 'E73',
      Name: 'kibibit per square metre',
    },
    {
      Code: 'E74',
      Name: 'kibibit per cubic metre',
    },
    {
      Code: 'E75',
      Name: 'mebibit per metre',
    },
    {
      Code: 'E76',
      Name: 'mebibit per square metre',
    },
    {
      Code: 'E77',
      Name: 'mebibit per cubic metre',
    },
    {
      Code: 'E78',
      Name: 'petabit',
    },
    {
      Code: 'E79',
      Name: 'petabit per second',
    },
    {
      Code: 'E80',
      Name: 'pebibit per metre',
    },
    {
      Code: 'E81',
      Name: 'pebibit per square metre',
    },
    {
      Code: 'E82',
      Name: 'pebibit per cubic metre',
    },
    {
      Code: 'E83',
      Name: 'terabit',
    },
    {
      Code: 'E84',
      Name: 'terabit per second',
    },
    {
      Code: 'E85',
      Name: 'tebibit per metre',
    },
    {
      Code: 'E86',
      Name: 'tebibit per cubic metre',
    },
    {
      Code: 'E87',
      Name: 'tebibit per square metre',
    },
    {
      Code: 'E88',
      Name: 'bit per metre',
    },
    {
      Code: 'E89',
      Name: 'bit per square metre',
    },
    {
      Code: 'E90',
      Name: 'reciprocal centimetre',
    },
    {
      Code: 'E91',
      Name: 'reciprocal day',
    },
    {
      Code: 'E92',
      Name: 'cubic decimetre per hour',
    },
    {
      Code: 'E93',
      Name: 'kilogram per hour',
    },
    {
      Code: 'E94',
      Name: 'kilomole per second',
    },
    {
      Code: 'E95',
      Name: 'mole per second',
    },
    {
      Code: 'E96',
      Name: 'degree per second',
    },
    {
      Code: 'E97',
      Name: 'millimetre per degree Celcius metre',
    },
    {
      Code: 'E98',
      Name: 'degree Celsius per kelvin',
    },
    {
      Code: 'E99',
      Name: 'hectopascal per bar',
    },
    {
      Code: 'EA',
      Name: 'each',
    },
    {
      Code: 'EB',
      Name: 'electronic mail box',
    },
    {
      Code: 'EQ',
      Name: 'equivalent gallon',
    },
    {
      Code: 'F01',
      Name: 'bit per cubic metre',
    },
    {
      Code: 'F02',
      Name: 'kelvin per kelvin',
    },
    {
      Code: 'F03',
      Name: 'kilopascal per bar',
    },
    {
      Code: 'F04',
      Name: 'millibar per bar',
    },
    {
      Code: 'F05',
      Name: 'megapascal per bar',
    },
    {
      Code: 'F06',
      Name: 'poise per bar',
    },
    {
      Code: 'F07',
      Name: 'pascal per bar',
    },
    {
      Code: 'F08',
      Name: 'milliampere per inch',
    },
    {
      Code: 'F10',
      Name: 'kelvin per hour',
    },
    {
      Code: 'F11',
      Name: 'kelvin per minute',
    },
    {
      Code: 'F12',
      Name: 'kelvin per second',
    },
    {
      Code: 'F13',
      Name: 'slug',
    },
    {
      Code: 'F14',
      Name: 'gram per kelvin',
    },
    {
      Code: 'F15',
      Name: 'kilogram per kelvin',
    },
    {
      Code: 'F16',
      Name: 'milligram per kelvin',
    },
    {
      Code: 'F17',
      Name: 'pound-force per foot',
    },
    {
      Code: 'F18',
      Name: 'kilogram square centimetre',
    },
    {
      Code: 'F19',
      Name: 'kilogram square millimetre',
    },
    {
      Code: 'F20',
      Name: 'pound inch squared',
    },
    {
      Code: 'F21',
      Name: 'pound-force inch',
    },
    {
      Code: 'F22',
      Name: 'pound-force foot per ampere',
    },
    {
      Code: 'F23',
      Name: 'gram per cubic decimetre',
    },
    {
      Code: 'F24',
      Name: 'kilogram per kilomol',
    },
    {
      Code: 'F25',
      Name: 'gram per hertz',
    },
    {
      Code: 'F26',
      Name: 'gram per day',
    },
    {
      Code: 'F27',
      Name: 'gram per hour',
    },
    {
      Code: 'F28',
      Name: 'gram per minute',
    },
    {
      Code: 'F29',
      Name: 'gram per second',
    },
    {
      Code: 'F30',
      Name: 'kilogram per day',
    },
    {
      Code: 'F31',
      Name: 'kilogram per minute',
    },
    {
      Code: 'F32',
      Name: 'milligram per day',
    },
    {
      Code: 'F33',
      Name: 'milligram per minute',
    },
    {
      Code: 'F34',
      Name: 'milligram per second',
    },
    {
      Code: 'F35',
      Name: 'gram per day kelvin',
    },
    {
      Code: 'F36',
      Name: 'gram per hour kelvin',
    },
    {
      Code: 'F37',
      Name: 'gram per minute kelvin',
    },
    {
      Code: 'F38',
      Name: 'gram per second kelvin',
    },
    {
      Code: 'F39',
      Name: 'kilogram per day kelvin',
    },
    {
      Code: 'F40',
      Name: 'kilogram per hour kelvin',
    },
    {
      Code: 'F41',
      Name: 'kilogram per minute kelvin',
    },
    {
      Code: 'F42',
      Name: 'kilogram per second kelvin',
    },
    {
      Code: 'F43',
      Name: 'milligram per day kelvin',
    },
    {
      Code: 'F44',
      Name: 'milligram per hour kelvin',
    },
    {
      Code: 'F45',
      Name: 'milligram per minute kelvin',
    },
    {
      Code: 'F46',
      Name: 'milligram per second kelvin',
    },
    {
      Code: 'F47',
      Name: 'newton per millimetre',
    },
    {
      Code: 'F48',
      Name: 'pound-force per inch',
    },
    {
      Code: 'F49',
      Name: 'rod [unit of distance]',
    },
    {
      Code: 'F50',
      Name: 'micrometre per kelvin',
    },
    {
      Code: 'F51',
      Name: 'centimetre per kelvin',
    },
    {
      Code: 'F52',
      Name: 'metre per kelvin',
    },
    {
      Code: 'F53',
      Name: 'millimetre per kelvin',
    },
    {
      Code: 'F54',
      Name: 'milliohm per metre',
    },
    {
      Code: 'F55',
      Name: 'ohm per mile (statute mile)',
    },
    {
      Code: 'F56',
      Name: 'ohm per kilometre',
    },
    {
      Code: 'F57',
      Name: 'milliampere per pound-force per square inch',
    },
    {
      Code: 'F58',
      Name: 'reciprocal bar',
    },
    {
      Code: 'F59',
      Name: 'milliampere per bar',
    },
    {
      Code: 'F60',
      Name: 'degree Celsius per bar',
    },
    {
      Code: 'F61',
      Name: 'kelvin per bar',
    },
    {
      Code: 'F62',
      Name: 'gram per day bar',
    },
    {
      Code: 'F63',
      Name: 'gram per hour bar',
    },
    {
      Code: 'F64',
      Name: 'gram per minute bar',
    },
    {
      Code: 'F65',
      Name: 'gram per second bar',
    },
    {
      Code: 'F66',
      Name: 'kilogram per day bar',
    },
    {
      Code: 'F67',
      Name: 'kilogram per hour bar',
    },
    {
      Code: 'F68',
      Name: 'kilogram per minute bar',
    },
    {
      Code: 'F69',
      Name: 'kilogram per second bar',
    },
    {
      Code: 'F70',
      Name: 'milligram per day bar',
    },
    {
      Code: 'F71',
      Name: 'milligram per hour bar',
    },
    {
      Code: 'F72',
      Name: 'milligram per minute bar',
    },
    {
      Code: 'F73',
      Name: 'milligram per second bar',
    },
    {
      Code: 'F74',
      Name: 'gram per bar',
    },
    {
      Code: 'F75',
      Name: 'milligram per bar',
    },
    {
      Code: 'F76',
      Name: 'milliampere per millimetre',
    },
    {
      Code: 'F77',
      Name: 'pascal second per kelvin',
    },
    {
      Code: 'F78',
      Name: 'inch of water',
    },
    {
      Code: 'F79',
      Name: 'inch of mercury',
    },
    {
      Code: 'F80',
      Name: 'water horse power',
    },
    {
      Code: 'F81',
      Name: 'bar per kelvin',
    },
    {
      Code: 'F82',
      Name: 'hectopascal per kelvin',
    },
    {
      Code: 'F83',
      Name: 'kilopascal per kelvin',
    },
    {
      Code: 'F84',
      Name: 'millibar per kelvin',
    },
    {
      Code: 'F85',
      Name: 'megapascal per kelvin',
    },
    {
      Code: 'F86',
      Name: 'poise per kelvin',
    },
    {
      Code: 'F87',
      Name: 'volt per litre minute',
    },
    {
      Code: 'F88',
      Name: 'newton centimetre',
    },
    {
      Code: 'F89',
      Name: 'newton metre per degree',
    },
    {
      Code: 'F90',
      Name: 'newton metre per ampere',
    },
    {
      Code: 'F91',
      Name: 'bar litre per second',
    },
    {
      Code: 'F92',
      Name: 'bar cubic metre per second',
    },
    {
      Code: 'F93',
      Name: 'hectopascal litre per second',
    },
    {
      Code: 'F94',
      Name: 'hectopascal cubic metre per second',
    },
    {
      Code: 'F95',
      Name: 'millibar litre per second',
    },
    {
      Code: 'F96',
      Name: 'millibar cubic metre per second',
    },
    {
      Code: 'F97',
      Name: 'megapascal litre per second',
    },
    {
      Code: 'F98',
      Name: 'megapascal cubic metre per second',
    },
    {
      Code: 'F99',
      Name: 'pascal litre per second',
    },
    {
      Code: 'FAH',
      Name: 'degree Fahrenheit',
    },
    {
      Code: 'FAR',
      Name: 'farad',
    },
    {
      Code: 'FBM',
      Name: 'fibre metre',
    },
    {
      Code: 'FC',
      Name: 'thousand cubic foot',
    },
    {
      Code: 'FF',
      Name: 'hundred cubic metre',
    },
    {
      Code: 'FH',
      Name: 'micromole',
    },
    {
      Code: 'FIT',
      Name: 'failures in time',
    },
    {
      Code: 'FL',
      Name: 'flake ton',
    },
    {
      Code: 'FNU',
      Name: 'Formazin nephelometric unit',
    },
    {
      Code: 'FOT',
      Name: 'foot',
    },
    {
      Code: 'FP',
      Name: 'pound per square foot',
    },
    {
      Code: 'FR',
      Name: 'foot per minute',
    },
    {
      Code: 'FS',
      Name: 'foot per second',
    },
    {
      Code: 'FTK',
      Name: 'square foot',
    },
    {
      Code: 'FTQ',
      Name: 'cubic foot',
    },
    {
      Code: 'G01',
      Name: 'pascal cubic metre per second',
    },
    {
      Code: 'G04',
      Name: 'centimetre per bar',
    },
    {
      Code: 'G05',
      Name: 'metre per bar',
    },
    {
      Code: 'G06',
      Name: 'millimetre per bar',
    },
    {
      Code: 'G08',
      Name: 'square inch per second',
    },
    {
      Code: 'G09',
      Name: 'square metre per second kelvin',
    },
    {
      Code: 'G10',
      Name: 'stokes per kelvin',
    },
    {
      Code: 'G11',
      Name: 'gram per cubic centimetre bar',
    },
    {
      Code: 'G12',
      Name: 'gram per cubic decimetre bar',
    },
    {
      Code: 'G13',
      Name: 'gram per litre bar',
    },
    {
      Code: 'G14',
      Name: 'gram per cubic metre bar',
    },
    {
      Code: 'G15',
      Name: 'gram per millilitre bar',
    },
    {
      Code: 'G16',
      Name: 'kilogram per cubic centimetre bar',
    },
    {
      Code: 'G17',
      Name: 'kilogram per litre bar',
    },
    {
      Code: 'G18',
      Name: 'kilogram per cubic metre bar',
    },
    {
      Code: 'G19',
      Name: 'newton metre per kilogram',
    },
    {
      Code: 'G2',
      Name: 'US gallon per minute',
    },
    {
      Code: 'G20',
      Name: 'pound-force foot per pound',
    },
    {
      Code: 'G21',
      Name: 'cup [unit of volume]',
    },
    {
      Code: 'G23',
      Name: 'peck',
    },
    {
      Code: 'G24',
      Name: 'tablespoon (US)',
    },
    {
      Code: 'G25',
      Name: 'teaspoon (US)',
    },
    {
      Code: 'G26',
      Name: 'stere',
    },
    {
      Code: 'G27',
      Name: 'cubic centimetre per kelvin',
    },
    {
      Code: 'G28',
      Name: 'litre per kelvin',
    },
    {
      Code: 'G29',
      Name: 'cubic metre per kelvin',
    },
    {
      Code: 'G3',
      Name: 'Imperial gallon per minute',
    },
    {
      Code: 'G30',
      Name: 'millilitre per kelvin',
    },
    {
      Code: 'G31',
      Name: 'kilogram per cubic centimetre',
    },
    {
      Code: 'G32',
      Name: 'ounce (avoirdupois) per cubic yard',
    },
    {
      Code: 'G33',
      Name: 'gram per cubic centimetre kelvin',
    },
    {
      Code: 'G34',
      Name: 'gram per cubic decimetre kelvin',
    },
    {
      Code: 'G35',
      Name: 'gram per litre kelvin',
    },
    {
      Code: 'G36',
      Name: 'gram per cubic metre kelvin',
    },
    {
      Code: 'G37',
      Name: 'gram per millilitre kelvin',
    },
    {
      Code: 'G38',
      Name: 'kilogram per cubic centimetre kelvin',
    },
    {
      Code: 'G39',
      Name: 'kilogram per litre kelvin',
    },
    {
      Code: 'G40',
      Name: 'kilogram per cubic metre kelvin',
    },
    {
      Code: 'G41',
      Name: 'square metre per second bar',
    },
    {
      Code: 'G42',
      Name: 'microsiemens per centimetre',
    },
    {
      Code: 'G43',
      Name: 'microsiemens per metre',
    },
    {
      Code: 'G44',
      Name: 'nanosiemens per centimetre',
    },
    {
      Code: 'G45',
      Name: 'nanosiemens per metre',
    },
    {
      Code: 'G46',
      Name: 'stokes per bar',
    },
    {
      Code: 'G47',
      Name: 'cubic centimetre per day',
    },
    {
      Code: 'G48',
      Name: 'cubic centimetre per hour',
    },
    {
      Code: 'G49',
      Name: 'cubic centimetre per minute',
    },
    {
      Code: 'G50',
      Name: 'gallon (US) per hour',
    },
    {
      Code: 'G51',
      Name: 'litre per second',
    },
    {
      Code: 'G52',
      Name: 'cubic metre per day',
    },
    {
      Code: 'G53',
      Name: 'cubic metre per minute',
    },
    {
      Code: 'G54',
      Name: 'millilitre per day',
    },
    {
      Code: 'G55',
      Name: 'millilitre per hour',
    },
    {
      Code: 'G56',
      Name: 'cubic inch per hour',
    },
    {
      Code: 'G57',
      Name: 'cubic inch per minute',
    },
    {
      Code: 'G58',
      Name: 'cubic inch per second',
    },
    {
      Code: 'G59',
      Name: 'milliampere per litre minute',
    },
    {
      Code: 'G60',
      Name: 'volt per bar',
    },
    {
      Code: 'G61',
      Name: 'cubic centimetre per day kelvin',
    },
    {
      Code: 'G62',
      Name: 'cubic centimetre per hour kelvin',
    },
    {
      Code: 'G63',
      Name: 'cubic centimetre per minute kelvin',
    },
    {
      Code: 'G64',
      Name: 'cubic centimetre per second kelvin',
    },
    {
      Code: 'G65',
      Name: 'litre per day kelvin',
    },
    {
      Code: 'G66',
      Name: 'litre per hour kelvin',
    },
    {
      Code: 'G67',
      Name: 'litre per minute kelvin',
    },
    {
      Code: 'G68',
      Name: 'litre per second kelvin',
    },
    {
      Code: 'G69',
      Name: 'cubic metre per day kelvin',
    },
    {
      Code: 'G70',
      Name: 'cubic metre per hour kelvin',
    },
    {
      Code: 'G71',
      Name: 'cubic metre per minute kelvin',
    },
    {
      Code: 'G72',
      Name: 'cubic metre per second kelvin',
    },
    {
      Code: 'G73',
      Name: 'millilitre per day kelvin',
    },
    {
      Code: 'G74',
      Name: 'millilitre per hour kelvin',
    },
    {
      Code: 'G75',
      Name: 'millilitre per minute kelvin',
    },
    {
      Code: 'G76',
      Name: 'millilitre per second kelvin',
    },
    {
      Code: 'G77',
      Name: 'millimetre to the fourth power',
    },
    {
      Code: 'G78',
      Name: 'cubic centimetre per day bar',
    },
    {
      Code: 'G79',
      Name: 'cubic centimetre per hour bar',
    },
    {
      Code: 'G80',
      Name: 'cubic centimetre per minute bar',
    },
    {
      Code: 'G81',
      Name: 'cubic centimetre per second bar',
    },
    {
      Code: 'G82',
      Name: 'litre per day bar',
    },
    {
      Code: 'G83',
      Name: 'litre per hour bar',
    },
    {
      Code: 'G84',
      Name: 'litre per minute bar',
    },
    {
      Code: 'G85',
      Name: 'litre per second bar',
    },
    {
      Code: 'G86',
      Name: 'cubic metre per day bar',
    },
    {
      Code: 'G87',
      Name: 'cubic metre per hour bar',
    },
    {
      Code: 'G88',
      Name: 'cubic metre per minute bar',
    },
    {
      Code: 'G89',
      Name: 'cubic metre per second bar',
    },
    {
      Code: 'G90',
      Name: 'millilitre per day bar',
    },
    {
      Code: 'G91',
      Name: 'millilitre per hour bar',
    },
    {
      Code: 'G92',
      Name: 'millilitre per minute bar',
    },
    {
      Code: 'G93',
      Name: 'millilitre per second bar',
    },
    {
      Code: 'G94',
      Name: 'cubic centimetre per bar',
    },
    {
      Code: 'G95',
      Name: 'litre per bar',
    },
    {
      Code: 'G96',
      Name: 'cubic metre per bar',
    },
    {
      Code: 'G97',
      Name: 'millilitre per bar',
    },
    {
      Code: 'G98',
      Name: 'microhenry per kiloohm',
    },
    {
      Code: 'G99',
      Name: 'microhenry per ohm',
    },
    {
      Code: 'GB',
      Name: 'gallon (US) per day',
    },
    {
      Code: 'GBQ',
      Name: 'gigabecquerel',
    },
    {
      Code: 'GDW',
      Name: 'gram, dry weight',
    },
    {
      Code: 'GE',
      Name: 'pound per gallon (US)',
    },
    {
      Code: 'GF',
      Name: 'gram per metre (gram per 100 centimetres)',
    },
    {
      Code: 'GFI',
      Name: 'gram of fissile isotope',
    },
    {
      Code: 'GGR',
      Name: 'great gross',
    },
    {
      Code: 'GIA',
      Name: 'gill (US)',
    },
    {
      Code: 'GIC',
      Name: 'gram, including container',
    },
    {
      Code: 'GII',
      Name: 'gill (UK)',
    },
    {
      Code: 'GIP',
      Name: 'gram, including inner packaging',
    },
    {
      Code: 'GJ',
      Name: 'gram per millilitre',
    },
    {
      Code: 'GL',
      Name: 'gram per litre',
    },
    {
      Code: 'GLD',
      Name: 'dry gallon (US)',
    },
    {
      Code: 'GLI',
      Name: 'gallon (UK)',
    },
    {
      Code: 'GLL',
      Name: 'gallon (US)',
    },
    {
      Code: 'GM',
      Name: 'gram per square metre',
    },
    {
      Code: 'GO',
      Name: 'milligram per square metre',
    },
    {
      Code: 'GP',
      Name: 'milligram per cubic metre',
    },
    {
      Code: 'GQ',
      Name: 'microgram per cubic metre',
    },
    {
      Code: 'GRM',
      Name: 'gram',
    },
    {
      Code: 'GRN',
      Name: 'grain',
    },
    {
      Code: 'GRO',
      Name: 'gross',
    },
    {
      Code: 'GV',
      Name: 'gigajoule',
    },
    {
      Code: 'GWH',
      Name: 'gigawatt hour',
    },
    {
      Code: 'H03',
      Name: 'henry per kiloohm',
    },
    {
      Code: 'H04',
      Name: 'henry per ohm',
    },
    {
      Code: 'H05',
      Name: 'millihenry per kiloohm',
    },
    {
      Code: 'H06',
      Name: 'millihenry per ohm',
    },
    {
      Code: 'H07',
      Name: 'pascal second per bar',
    },
    {
      Code: 'H08',
      Name: 'microbecquerel',
    },
    {
      Code: 'H09',
      Name: 'reciprocal year',
    },
    {
      Code: 'H10',
      Name: 'reciprocal hour',
    },
    {
      Code: 'H11',
      Name: 'reciprocal month',
    },
    {
      Code: 'H12',
      Name: 'degree Celsius per hour',
    },
    {
      Code: 'H13',
      Name: 'degree Celsius per minute',
    },
    {
      Code: 'H14',
      Name: 'degree Celsius per second',
    },
    {
      Code: 'H15',
      Name: 'square centimetre per gram',
    },
    {
      Code: 'H16',
      Name: 'square decametre',
    },
    {
      Code: 'H18',
      Name: 'square hectometre',
    },
    {
      Code: 'H19',
      Name: 'cubic hectometre',
    },
    {
      Code: 'H20',
      Name: 'cubic kilometre',
    },
    {
      Code: 'H21',
      Name: 'blank',
    },
    {
      Code: 'H22',
      Name: 'volt square inch per pound-force',
    },
    {
      Code: 'H23',
      Name: 'volt per inch',
    },
    {
      Code: 'H24',
      Name: 'volt per microsecond',
    },
    {
      Code: 'H25',
      Name: 'percent per kelvin',
    },
    {
      Code: 'H26',
      Name: 'ohm per metre',
    },
    {
      Code: 'H27',
      Name: 'degree per metre',
    },
    {
      Code: 'H28',
      Name: 'microfarad per kilometre',
    },
    {
      Code: 'H29',
      Name: 'microgram per litre',
    },
    {
      Code: 'H30',
      Name: 'square micrometre (square micron)',
    },
    {
      Code: 'H31',
      Name: 'ampere per kilogram',
    },
    {
      Code: 'H32',
      Name: 'ampere squared second',
    },
    {
      Code: 'H33',
      Name: 'farad per kilometre',
    },
    {
      Code: 'H34',
      Name: 'hertz metre',
    },
    {
      Code: 'H35',
      Name: 'kelvin metre per watt',
    },
    {
      Code: 'H36',
      Name: 'megaohm per kilometre',
    },
    {
      Code: 'H37',
      Name: 'megaohm per metre',
    },
    {
      Code: 'H38',
      Name: 'megaampere',
    },
    {
      Code: 'H39',
      Name: 'megahertz kilometre',
    },
    {
      Code: 'H40',
      Name: 'newton per ampere',
    },
    {
      Code: 'H41',
      Name: 'newton metre watt to the power minus 0,5',
    },
    {
      Code: 'H42',
      Name: 'pascal per metre',
    },
    {
      Code: 'H43',
      Name: 'siemens per centimetre',
    },
    {
      Code: 'H44',
      Name: 'teraohm',
    },
    {
      Code: 'H45',
      Name: 'volt second per metre',
    },
    {
      Code: 'H46',
      Name: 'volt per second',
    },
    {
      Code: 'H47',
      Name: 'watt per cubic metre',
    },
    {
      Code: 'H48',
      Name: 'attofarad',
    },
    {
      Code: 'H49',
      Name: 'centimetre per hour',
    },
    {
      Code: 'H50',
      Name: 'reciprocal cubic centimetre',
    },
    {
      Code: 'H51',
      Name: 'decibel per kilometre',
    },
    {
      Code: 'H52',
      Name: 'decibel per metre',
    },
    {
      Code: 'H53',
      Name: 'kilogram per bar',
    },
    {
      Code: 'H54',
      Name: 'kilogram per cubic decimetre kelvin',
    },
    {
      Code: 'H55',
      Name: 'kilogram per cubic decimetre bar',
    },
    {
      Code: 'H56',
      Name: 'kilogram per square metre second',
    },
    {
      Code: 'H57',
      Name: 'inch per two pi radiant',
    },
    {
      Code: 'H58',
      Name: 'metre per volt second',
    },
    {
      Code: 'H59',
      Name: 'square metre per newton',
    },
    {
      Code: 'H60',
      Name: 'cubic metre per cubic metre',
    },
    {
      Code: 'H61',
      Name: 'millisiemens per centimetre',
    },
    {
      Code: 'H62',
      Name: 'millivolt per minute',
    },
    {
      Code: 'H63',
      Name: 'milligram per square centimetre',
    },
    {
      Code: 'H64',
      Name: 'milligram per gram',
    },
    {
      Code: 'H65',
      Name: 'millilitre per cubic metre',
    },
    {
      Code: 'H66',
      Name: 'millimetre per year',
    },
    {
      Code: 'H67',
      Name: 'millimetre per hour',
    },
    {
      Code: 'H68',
      Name: 'millimole per gram',
    },
    {
      Code: 'H69',
      Name: 'picopascal per kilometre',
    },
    {
      Code: 'H70',
      Name: 'picosecond',
    },
    {
      Code: 'H71',
      Name: 'percent per month',
    },
    {
      Code: 'H72',
      Name: 'percent per hectobar',
    },
    {
      Code: 'H73',
      Name: 'percent per decakelvin',
    },
    {
      Code: 'H74',
      Name: 'watt per metre',
    },
    {
      Code: 'H75',
      Name: 'decapascal',
    },
    {
      Code: 'H76',
      Name: 'gram per millimetre',
    },
    {
      Code: 'H77',
      Name: 'module width',
    },
    {
      Code: 'H79',
      Name: 'French gauge',
    },
    {
      Code: 'H80',
      Name: 'rack unit',
    },
    {
      Code: 'H81',
      Name: 'millimetre per minute',
    },
    {
      Code: 'H82',
      Name: 'big point',
    },
    {
      Code: 'H83',
      Name: 'litre per kilogram',
    },
    {
      Code: 'H84',
      Name: 'gram millimetre',
    },
    {
      Code: 'H85',
      Name: 'reciprocal week',
    },
    {
      Code: 'H87',
      Name: 'piece',
    },
    {
      Code: 'H88',
      Name: 'megaohm kilometre',
    },
    {
      Code: 'H89',
      Name: 'percent per ohm',
    },
    {
      Code: 'H90',
      Name: 'percent per degree',
    },
    {
      Code: 'H91',
      Name: 'percent per ten thousand',
    },
    {
      Code: 'H92',
      Name: 'percent per one hundred thousand',
    },
    {
      Code: 'H93',
      Name: 'percent per hundred',
    },
    {
      Code: 'H94',
      Name: 'percent per thousand',
    },
    {
      Code: 'H95',
      Name: 'percent per volt',
    },
    {
      Code: 'H96',
      Name: 'percent per bar',
    },
    {
      Code: 'H98',
      Name: 'percent per inch',
    },
    {
      Code: 'H99',
      Name: 'percent per metre',
    },
    {
      Code: 'HA',
      Name: 'hank',
    },
    {
      Code: 'HAD',
      Name: 'Piece Day',
    },
    {
      Code: 'HBA',
      Name: 'hectobar',
    },
    {
      Code: 'HBX',
      Name: 'hundred boxes',
    },
    {
      Code: 'HC',
      Name: 'hundred count',
    },
    {
      Code: 'HDW',
      Name: 'hundred kilogram, dry weight',
    },
    {
      Code: 'HEA',
      Name: 'head',
    },
    {
      Code: 'HGM',
      Name: 'hectogram',
    },
    {
      Code: 'HH',
      Name: 'hundred cubic foot',
    },
    {
      Code: 'HIU',
      Name: 'hundred international unit',
    },
    {
      Code: 'HKM',
      Name: 'hundred kilogram, net mass',
    },
    {
      Code: 'HLT',
      Name: 'hectolitre',
    },
    {
      Code: 'HM',
      Name: 'mile per hour (statute mile)',
    },
    {
      Code: 'HMO',
      Name: 'Piece Month',
    },
    {
      Code: 'HMQ',
      Name: 'million cubic metre',
    },
    {
      Code: 'HMT',
      Name: 'hectometre',
    },
    {
      Code: 'HPA',
      Name: 'hectolitre of pure alcohol',
    },
    {
      Code: 'HTZ',
      Name: 'hertz',
    },
    {
      Code: 'HUR',
      Name: 'hour',
    },
    {
      Code: 'IA',
      Name: 'inch pound (pound inch)',
    },
    {
      Code: 'IE',
      Name: 'person',
    },
    {
      Code: 'INH',
      Name: 'inch',
    },
    {
      Code: 'INK',
      Name: 'square inch',
    },
    {
      Code: 'INQ',
      Name: 'cubic inch',
    },
    {
      Code: 'ISD',
      Name: 'international sugar degree',
    },
    {
      Code: 'IU',
      Name: 'inch per second',
    },
    {
      Code: 'IUG',
      Name: 'international unit per gram',
    },
    {
      Code: 'IV',
      Name: 'inch per second squared',
    },
    {
      Code: 'J10',
      Name: 'percent per millimetre',
    },
    {
      Code: 'J12',
      Name: 'per mille per psi',
    },
    {
      Code: 'J13',
      Name: 'degree API',
    },
    {
      Code: 'J14',
      Name: 'degree Baume (origin scale)',
    },
    {
      Code: 'J15',
      Name: 'degree Baume (US heavy)',
    },
    {
      Code: 'J16',
      Name: 'degree Baume (US light)',
    },
    {
      Code: 'J17',
      Name: 'degree Balling',
    },
    {
      Code: 'J18',
      Name: 'degree Brix',
    },
    {
      Code: 'J19',
      Name: 'degree Fahrenheit hour square foot per British thermal unit (thermochemical)',
    },
    {
      Code: 'J2',
      Name: 'joule per kilogram',
    },
    {
      Code: 'J20',
      Name: 'degree Fahrenheit per kelvin',
    },
    {
      Code: 'J21',
      Name: 'degree Fahrenheit per bar',
    },
    {
      Code: 'J22',
      Name: 'degree Fahrenheit hour square foot per British thermal unit (international table)',
    },
    {
      Code: 'J23',
      Name: 'degree Fahrenheit per hour',
    },
    {
      Code: 'J24',
      Name: 'degree Fahrenheit per minute',
    },
    {
      Code: 'J25',
      Name: 'degree Fahrenheit per second',
    },
    {
      Code: 'J26',
      Name: 'reciprocal degree Fahrenheit',
    },
    {
      Code: 'J27',
      Name: 'degree Oechsle',
    },
    {
      Code: 'J28',
      Name: 'degree Rankine per hour',
    },
    {
      Code: 'J29',
      Name: 'degree Rankine per minute',
    },
    {
      Code: 'J30',
      Name: 'degree Rankine per second',
    },
    {
      Code: 'J31',
      Name: 'degree Twaddell',
    },
    {
      Code: 'J32',
      Name: 'micropoise',
    },
    {
      Code: 'J33',
      Name: 'microgram per kilogram',
    },
    {
      Code: 'J34',
      Name: 'microgram per cubic metre kelvin',
    },
    {
      Code: 'J35',
      Name: 'microgram per cubic metre bar',
    },
    {
      Code: 'J36',
      Name: 'microlitre per litre',
    },
    {
      Code: 'J38',
      Name: 'baud',
    },
    {
      Code: 'J39',
      Name: 'British thermal unit (mean)',
    },
    {
      Code: 'J40',
      Name: 'British thermal unit (international table) foot per hourÂ square foot degree Fahrenheit',
    },
    {
      Code: 'J41',
      Name: 'British thermal unit (international table) inch per hour squareÂ foot degree Fahrenheit',
    },
    {
      Code: 'J42',
      Name: 'British thermal unit (international table) inch per second squareÂ foot degree Fahrenheit',
    },
    {
      Code: 'J43',
      Name: 'British thermal unit (international table) per pound degree Fahrenheit',
    },
    {
      Code: 'J44',
      Name: 'British thermal unit (international table) per minute',
    },
    {
      Code: 'J45',
      Name: 'British thermal unit (international table) per second',
    },
    {
      Code: 'J46',
      Name: 'British thermal unit (thermochemical) foot per hour squareÂ foot degree Fahrenheit',
    },
    {
      Code: 'J47',
      Name: 'British thermal unit (thermochemical) per hour',
    },
    {
      Code: 'J48',
      Name: 'British thermal unit (thermochemical) inch per hour squareÂ foot degree Fahrenheit',
    },
    {
      Code: 'J49',
      Name: 'British thermal unit (thermochemical) inch per secondÂ square foot degree Fahrenheit',
    },
    {
      Code: 'J50',
      Name: 'British thermal unit (thermochemical) per pound degree Fahrenheit',
    },
    {
      Code: 'J51',
      Name: 'British thermal unit (thermochemical) per minute',
    },
    {
      Code: 'J52',
      Name: 'British thermal unit (thermochemical) per second',
    },
    {
      Code: 'J53',
      Name: 'coulomb square metre per kilogram',
    },
    {
      Code: 'J54',
      Name: 'megabaud',
    },
    {
      Code: 'J55',
      Name: 'watt second',
    },
    {
      Code: 'J56',
      Name: 'bar per bar',
    },
    {
      Code: 'J57',
      Name: 'barrel (UK petroleum)',
    },
    {
      Code: 'J58',
      Name: 'barrel (UK petroleum) per minute',
    },
    {
      Code: 'J59',
      Name: 'barrel (UK petroleum) per day',
    },
    {
      Code: 'J60',
      Name: 'barrel (UK petroleum) per hour',
    },
    {
      Code: 'J61',
      Name: 'barrel (UK petroleum) per second',
    },
    {
      Code: 'J62',
      Name: 'barrel (US petroleum) per hour',
    },
    {
      Code: 'J63',
      Name: 'barrel (US petroleum) per second',
    },
    {
      Code: 'J64',
      Name: 'bushel (UK) per day',
    },
    {
      Code: 'J65',
      Name: 'bushel (UK) per hour',
    },
    {
      Code: 'J66',
      Name: 'bushel (UK) per minute',
    },
    {
      Code: 'J67',
      Name: 'bushel (UK) per second',
    },
    {
      Code: 'J68',
      Name: 'bushel (US dry) per day',
    },
    {
      Code: 'J69',
      Name: 'bushel (US dry) per hour',
    },
    {
      Code: 'J70',
      Name: 'bushel (US dry) per minute',
    },
    {
      Code: 'J71',
      Name: 'bushel (US dry) per second',
    },
    {
      Code: 'J72',
      Name: 'centinewton metre',
    },
    {
      Code: 'J73',
      Name: 'centipoise per kelvin',
    },
    {
      Code: 'J74',
      Name: 'centipoise per bar',
    },
    {
      Code: 'J75',
      Name: 'calorie (mean)',
    },
    {
      Code: 'J76',
      Name: 'calorie (international table) per gram degree Celsius',
    },
    {
      Code: 'J78',
      Name: 'calorie (thermochemical) per centimetre second degree Celsius',
    },
    {
      Code: 'J79',
      Name: 'calorie (thermochemical) per gram degree Celsius',
    },
    {
      Code: 'J81',
      Name: 'calorie (thermochemical) per minute',
    },
    {
      Code: 'J82',
      Name: 'calorie (thermochemical) per second',
    },
    {
      Code: 'J83',
      Name: 'clo',
    },
    {
      Code: 'J84',
      Name: 'centimetre per second kelvin',
    },
    {
      Code: 'J85',
      Name: 'centimetre per second bar',
    },
    {
      Code: 'J87',
      Name: 'cubic centimetre per cubic metre',
    },
    {
      Code: 'J90',
      Name: 'cubic decimetre per day',
    },
    {
      Code: 'J91',
      Name: 'cubic decimetre per cubic metre',
    },
    {
      Code: 'J92',
      Name: 'cubic decimetre per minute',
    },
    {
      Code: 'J93',
      Name: 'cubic decimetre per second',
    },
    {
      Code: 'J95',
      Name: 'ounce (UK fluid) per day',
    },
    {
      Code: 'J96',
      Name: 'ounce (UK fluid) per hour',
    },
    {
      Code: 'J97',
      Name: 'ounce (UK fluid) per minute',
    },
    {
      Code: 'J98',
      Name: 'ounce (UK fluid) per second',
    },
    {
      Code: 'J99',
      Name: 'ounce (US fluid) per day',
    },
    {
      Code: 'JE',
      Name: 'joule per kelvin',
    },
    {
      Code: 'JK',
      Name: 'megajoule per kilogram',
    },
    {
      Code: 'JM',
      Name: 'megajoule per cubic metre',
    },
    {
      Code: 'JNT',
      Name: 'pipeline joint',
    },
    {
      Code: 'JOU',
      Name: 'joule',
    },
    {
      Code: 'JPS',
      Name: 'hundred metre',
    },
    {
      Code: 'JWL',
      Name: 'number of jewels',
    },
    {
      Code: 'K1',
      Name: 'kilowatt demand',
    },
    {
      Code: 'K10',
      Name: 'ounce (US fluid) per hour',
    },
    {
      Code: 'K11',
      Name: 'ounce (US fluid) per minute',
    },
    {
      Code: 'K12',
      Name: 'ounce (US fluid) per second',
    },
    {
      Code: 'K13',
      Name: 'foot per degree Fahrenheit',
    },
    {
      Code: 'K14',
      Name: 'foot per hour',
    },
    {
      Code: 'K15',
      Name: 'foot pound-force per hour',
    },
    {
      Code: 'K16',
      Name: 'foot pound-force per minute',
    },
    {
      Code: 'K17',
      Name: 'foot per psi',
    },
    {
      Code: 'K18',
      Name: 'foot per second degree Fahrenheit',
    },
    {
      Code: 'K19',
      Name: 'foot per second psi',
    },
    {
      Code: 'K2',
      Name: 'kilovolt ampere reactive demand',
    },
    {
      Code: 'K20',
      Name: 'reciprocal cubic foot',
    },
    {
      Code: 'K21',
      Name: 'cubic foot per degree Fahrenheit',
    },
    {
      Code: 'K22',
      Name: 'cubic foot per day',
    },
    {
      Code: 'K23',
      Name: 'cubic foot per psi',
    },
    {
      Code: 'K26',
      Name: 'gallon (UK) per day',
    },
    {
      Code: 'K27',
      Name: 'gallon (UK) per hour',
    },
    {
      Code: 'K28',
      Name: 'gallon (UK) per second',
    },
    {
      Code: 'K3',
      Name: 'kilovolt ampere reactive hour',
    },
    {
      Code: 'K30',
      Name: 'gallon (US liquid) per second',
    },
    {
      Code: 'K31',
      Name: 'gram-force per square centimetre',
    },
    {
      Code: 'K32',
      Name: 'gill (UK) per day',
    },
    {
      Code: 'K33',
      Name: 'gill (UK) per hour',
    },
    {
      Code: 'K34',
      Name: 'gill (UK) per minute',
    },
    {
      Code: 'K35',
      Name: 'gill (UK) per second',
    },
    {
      Code: 'K36',
      Name: 'gill (US) per day',
    },
    {
      Code: 'K37',
      Name: 'gill (US) per hour',
    },
    {
      Code: 'K38',
      Name: 'gill (US) per minute',
    },
    {
      Code: 'K39',
      Name: 'gill (US) per second',
    },
    {
      Code: 'K40',
      Name: 'standard acceleration of free fall',
    },
    {
      Code: 'K41',
      Name: 'grain per gallon (US)',
    },
    {
      Code: 'K42',
      Name: 'horsepower (boiler)',
    },
    {
      Code: 'K43',
      Name: 'horsepower (electric)',
    },
    {
      Code: 'K45',
      Name: 'inch per degree Fahrenheit',
    },
    {
      Code: 'K46',
      Name: 'inch per psi',
    },
    {
      Code: 'K47',
      Name: 'inch per second degree Fahrenheit',
    },
    {
      Code: 'K48',
      Name: 'inch per second psi',
    },
    {
      Code: 'K49',
      Name: 'reciprocal cubic inch',
    },
    {
      Code: 'K50',
      Name: 'kilobaud',
    },
    {
      Code: 'K51',
      Name: 'kilocalorie (mean)',
    },
    {
      Code: 'K52',
      Name: 'kilocalorie (international table) per hour metre degree Celsius',
    },
    {
      Code: 'K53',
      Name: 'kilocalorie (thermochemical)',
    },
    {
      Code: 'K54',
      Name: 'kilocalorie (thermochemical) per minute',
    },
    {
      Code: 'K55',
      Name: 'kilocalorie (thermochemical) per second',
    },
    {
      Code: 'K58',
      Name: 'kilomole per hour',
    },
    {
      Code: 'K59',
      Name: 'kilomole per cubic metre kelvin',
    },
    {
      Code: 'K6',
      Name: 'kilolitre',
    },
    {
      Code: 'K60',
      Name: 'kilomole per cubic metre bar',
    },
    {
      Code: 'K61',
      Name: 'kilomole per minute',
    },
    {
      Code: 'K62',
      Name: 'litre per litre',
    },
    {
      Code: 'K63',
      Name: 'reciprocal litre',
    },
    {
      Code: 'K64',
      Name: 'pound (avoirdupois) per degree Fahrenheit',
    },
    {
      Code: 'K65',
      Name: 'pound (avoirdupois) square foot',
    },
    {
      Code: 'K66',
      Name: 'pound (avoirdupois) per day',
    },
    {
      Code: 'K67',
      Name: 'pound per foot hour',
    },
    {
      Code: 'K68',
      Name: 'pound per foot second',
    },
    {
      Code: 'K69',
      Name: 'pound (avoirdupois) per cubic foot degree Fahrenheit',
    },
    {
      Code: 'K70',
      Name: 'pound (avoirdupois) per cubic foot psi',
    },
    {
      Code: 'K71',
      Name: 'pound (avoirdupois) per gallon (UK)',
    },
    {
      Code: 'K73',
      Name: 'pound (avoirdupois) per hour degree Fahrenheit',
    },
    {
      Code: 'K74',
      Name: 'pound (avoirdupois) per hour psi',
    },
    {
      Code: 'K75',
      Name: 'pound (avoirdupois) per cubic inch degree Fahrenheit',
    },
    {
      Code: 'K76',
      Name: 'pound (avoirdupois) per cubic inch psi',
    },
    {
      Code: 'K77',
      Name: 'pound (avoirdupois) per psi',
    },
    {
      Code: 'K78',
      Name: 'pound (avoirdupois) per minute',
    },
    {
      Code: 'K79',
      Name: 'pound (avoirdupois) per minute degree Fahrenheit',
    },
    {
      Code: 'K80',
      Name: 'pound (avoirdupois) per minute psi',
    },
    {
      Code: 'K81',
      Name: 'pound (avoirdupois) per second',
    },
    {
      Code: 'K82',
      Name: 'pound (avoirdupois) per second degree Fahrenheit',
    },
    {
      Code: 'K83',
      Name: 'pound (avoirdupois) per second psi',
    },
    {
      Code: 'K84',
      Name: 'pound per cubic yard',
    },
    {
      Code: 'K85',
      Name: 'pound-force per square foot',
    },
    {
      Code: 'K86',
      Name: 'pound-force per square inch degree Fahrenheit',
    },
    {
      Code: 'K87',
      Name: 'psi cubic inch per second',
    },
    {
      Code: 'K88',
      Name: 'psi litre per second',
    },
    {
      Code: 'K89',
      Name: 'psi cubic metre per second',
    },
    {
      Code: 'K90',
      Name: 'psi cubic yard per second',
    },
    {
      Code: 'K91',
      Name: 'pound-force second per square foot',
    },
    {
      Code: 'K92',
      Name: 'pound-force second per square inch',
    },
    {
      Code: 'K93',
      Name: 'reciprocal psi',
    },
    {
      Code: 'K94',
      Name: 'quart (UK liquid) per day',
    },
    {
      Code: 'K95',
      Name: 'quart (UK liquid) per hour',
    },
    {
      Code: 'K96',
      Name: 'quart (UK liquid) per minute',
    },
    {
      Code: 'K97',
      Name: 'quart (UK liquid) per second',
    },
    {
      Code: 'K98',
      Name: 'quart (US liquid) per day',
    },
    {
      Code: 'K99',
      Name: 'quart (US liquid) per hour',
    },
    {
      Code: 'KA',
      Name: 'cake',
    },
    {
      Code: 'KAT',
      Name: 'katal',
    },
    {
      Code: 'KB',
      Name: 'kilocharacter',
    },
    {
      Code: 'KBA',
      Name: 'kilobar',
    },
    {
      Code: 'KCC',
      Name: 'kilogram of choline chloride',
    },
    {
      Code: 'KDW',
      Name: 'kilogram drained net weight',
    },
    {
      Code: 'KEL',
      Name: 'kelvin',
    },
    {
      Code: 'KGM',
      Name: 'kilogram',
    },
    {
      Code: 'KGS',
      Name: 'kilogram per second',
    },
    {
      Code: 'KHY',
      Name: 'kilogram of hydrogen peroxide',
    },
    {
      Code: 'KHZ',
      Name: 'kilohertz',
    },
    {
      Code: 'KI',
      Name: 'kilogram per millimetre width',
    },
    {
      Code: 'KIC',
      Name: 'kilogram, including container',
    },
    {
      Code: 'KIP',
      Name: 'kilogram, including inner packaging',
    },
    {
      Code: 'KJ',
      Name: 'kilosegment',
    },
    {
      Code: 'KJO',
      Name: 'kilojoule',
    },
    {
      Code: 'KL',
      Name: 'kilogram per metre',
    },
    {
      Code: 'KLK',
      Name: 'lactic dry material percentage',
    },
    {
      Code: 'KLX',
      Name: 'kilolux',
    },
    {
      Code: 'KMA',
      Name: 'kilogram of methylamine',
    },
    {
      Code: 'KMH',
      Name: 'kilometre per hour',
    },
    {
      Code: 'KMK',
      Name: 'square kilometre',
    },
    {
      Code: 'KMQ',
      Name: 'kilogram per cubic metre',
    },
    {
      Code: 'KMT',
      Name: 'kilometre',
    },
    {
      Code: 'KNI',
      Name: 'kilogram of nitrogen',
    },
    {
      Code: 'KNM',
      Name: 'kilonewton per square metre',
    },
    {
      Code: 'KNS',
      Name: 'kilogram named substance',
    },
    {
      Code: 'KNT',
      Name: 'knot',
    },
    {
      Code: 'KO',
      Name: 'milliequivalence caustic potash per gram of product',
    },
    {
      Code: 'KPA',
      Name: 'kilopascal',
    },
    {
      Code: 'KPH',
      Name: 'kilogram of potassium hydroxide (caustic potash)',
    },
    {
      Code: 'KPO',
      Name: 'kilogram of potassium oxide',
    },
    {
      Code: 'KPP',
      Name: 'kilogram of phosphorus pentoxide (phosphoric anhydride)',
    },
    {
      Code: 'KR',
      Name: 'kiloroentgen',
    },
    {
      Code: 'KSD',
      Name: 'kilogram of substance 90 % dry',
    },
    {
      Code: 'KSH',
      Name: 'kilogram of sodium hydroxide (caustic soda)',
    },
    {
      Code: 'KT',
      Name: 'kit',
    },
    {
      Code: 'KTN',
      Name: 'kilotonne',
    },
    {
      Code: 'KUR',
      Name: 'kilogram of uranium',
    },
    {
      Code: 'KVA',
      Name: 'kilovolt - ampere',
    },
    {
      Code: 'KVR',
      Name: 'kilovar',
    },
    {
      Code: 'KVT',
      Name: 'kilovolt',
    },
    {
      Code: 'KW',
      Name: 'kilogram per millimetre',
    },
    {
      Code: 'KWH',
      Name: 'kilowatt hour',
    },
    {
      Code: 'KWN',
      Name: 'Kilowatt hour per normalized cubic metre',
    },
    {
      Code: 'KWO',
      Name: 'kilogram of tungsten trioxide',
    },
    {
      Code: 'KWS',
      Name: 'Kilowatt hour per standard cubic metre',
    },
    {
      Code: 'KWT',
      Name: 'kilowatt',
    },
    {
      Code: 'KWY',
      Name: 'kilowatt year',
    },
    {
      Code: 'KX',
      Name: 'millilitre per kilogram',
    },
    {
      Code: 'L10',
      Name: 'quart (US liquid) per minute',
    },
    {
      Code: 'L11',
      Name: 'quart (US liquid) per second',
    },
    {
      Code: 'L12',
      Name: 'metre per second kelvin',
    },
    {
      Code: 'L13',
      Name: 'metre per second bar',
    },
    {
      Code: 'L14',
      Name: 'square metre hour degree Celsius per kilocalorie (international table)',
    },
    {
      Code: 'L15',
      Name: 'millipascal second per kelvin',
    },
    {
      Code: 'L16',
      Name: 'millipascal second per bar',
    },
    {
      Code: 'L17',
      Name: 'milligram per cubic metre kelvin',
    },
    {
      Code: 'L18',
      Name: 'milligram per cubic metre bar',
    },
    {
      Code: 'L19',
      Name: 'millilitre per litre',
    },
    {
      Code: 'L2',
      Name: 'litre per minute',
    },
    {
      Code: 'L20',
      Name: 'reciprocal cubic millimetre',
    },
    {
      Code: 'L21',
      Name: 'cubic millimetre per cubic metre',
    },
    {
      Code: 'L23',
      Name: 'mole per hour',
    },
    {
      Code: 'L24',
      Name: 'mole per kilogram kelvin',
    },
    {
      Code: 'L25',
      Name: 'mole per kilogram bar',
    },
    {
      Code: 'L26',
      Name: 'mole per litre kelvin',
    },
    {
      Code: 'L27',
      Name: 'mole per litre bar',
    },
    {
      Code: 'L28',
      Name: 'mole per cubic metre kelvin',
    },
    {
      Code: 'L29',
      Name: 'mole per cubic metre bar',
    },
    {
      Code: 'L30',
      Name: 'mole per minute',
    },
    {
      Code: 'L31',
      Name: 'milliroentgen aequivalent men',
    },
    {
      Code: 'L32',
      Name: 'nanogram per kilogram',
    },
    {
      Code: 'L33',
      Name: 'ounce (avoirdupois) per day',
    },
    {
      Code: 'L34',
      Name: 'ounce (avoirdupois) per hour',
    },
    {
      Code: 'L35',
      Name: 'ounce (avoirdupois) per minute',
    },
    {
      Code: 'L36',
      Name: 'ounce (avoirdupois) per second',
    },
    {
      Code: 'L37',
      Name: 'ounce (avoirdupois) per gallon (UK)',
    },
    {
      Code: 'L38',
      Name: 'ounce (avoirdupois) per gallon (US)',
    },
    {
      Code: 'L39',
      Name: 'ounce (avoirdupois) per cubic inch',
    },
    {
      Code: 'L40',
      Name: 'ounce (avoirdupois)-force',
    },
    {
      Code: 'L41',
      Name: 'ounce (avoirdupois)-force inch',
    },
    {
      Code: 'L42',
      Name: 'picosiemens per metre',
    },
    {
      Code: 'L43',
      Name: 'peck (UK)',
    },
    {
      Code: 'L44',
      Name: 'peck (UK) per day',
    },
    {
      Code: 'L45',
      Name: 'peck (UK) per hour',
    },
    {
      Code: 'L46',
      Name: 'peck (UK) per minute',
    },
    {
      Code: 'L47',
      Name: 'peck (UK) per second',
    },
    {
      Code: 'L48',
      Name: 'peck (US dry) per day',
    },
    {
      Code: 'L49',
      Name: 'peck (US dry) per hour',
    },
    {
      Code: 'L50',
      Name: 'peck (US dry) per minute',
    },
    {
      Code: 'L51',
      Name: 'peck (US dry) per second',
    },
    {
      Code: 'L52',
      Name: 'psi per psi',
    },
    {
      Code: 'L53',
      Name: 'pint (UK) per day',
    },
    {
      Code: 'L54',
      Name: 'pint (UK) per hour',
    },
    {
      Code: 'L55',
      Name: 'pint (UK) per minute',
    },
    {
      Code: 'L56',
      Name: 'pint (UK) per second',
    },
    {
      Code: 'L57',
      Name: 'pint (US liquid) per day',
    },
    {
      Code: 'L58',
      Name: 'pint (US liquid) per hour',
    },
    {
      Code: 'L59',
      Name: 'pint (US liquid) per minute',
    },
    {
      Code: 'L60',
      Name: 'pint (US liquid) per second',
    },
    {
      Code: 'L63',
      Name: 'slug per day',
    },
    {
      Code: 'L64',
      Name: 'slug per foot second',
    },
    {
      Code: 'L65',
      Name: 'slug per cubic foot',
    },
    {
      Code: 'L66',
      Name: 'slug per hour',
    },
    {
      Code: 'L67',
      Name: 'slug per minute',
    },
    {
      Code: 'L68',
      Name: 'slug per second',
    },
    {
      Code: 'L69',
      Name: 'tonne per kelvin',
    },
    {
      Code: 'L70',
      Name: 'tonne per bar',
    },
    {
      Code: 'L71',
      Name: 'tonne per day',
    },
    {
      Code: 'L72',
      Name: 'tonne per day kelvin',
    },
    {
      Code: 'L73',
      Name: 'tonne per day bar',
    },
    {
      Code: 'L74',
      Name: 'tonne per hour kelvin',
    },
    {
      Code: 'L75',
      Name: 'tonne per hour bar',
    },
    {
      Code: 'L76',
      Name: 'tonne per cubic metre kelvin',
    },
    {
      Code: 'L77',
      Name: 'tonne per cubic metre bar',
    },
    {
      Code: 'L78',
      Name: 'tonne per minute',
    },
    {
      Code: 'L79',
      Name: 'tonne per minute kelvin',
    },
    {
      Code: 'L80',
      Name: 'tonne per minute bar',
    },
    {
      Code: 'L81',
      Name: 'tonne per second',
    },
    {
      Code: 'L82',
      Name: 'tonne per second kelvin',
    },
    {
      Code: 'L83',
      Name: 'tonne per second bar',
    },
    {
      Code: 'L84',
      Name: 'ton (UK shipping)',
    },
    {
      Code: 'L85',
      Name: 'ton long per day',
    },
    {
      Code: 'L86',
      Name: 'ton (US shipping)',
    },
    {
      Code: 'L87',
      Name: 'ton short per degree Fahrenheit',
    },
    {
      Code: 'L88',
      Name: 'ton short per day',
    },
    {
      Code: 'L89',
      Name: 'ton short per hour degree Fahrenheit',
    },
    {
      Code: 'L90',
      Name: 'ton short per hour psi',
    },
    {
      Code: 'L91',
      Name: 'ton short per psi',
    },
    {
      Code: 'L92',
      Name: 'ton (UK long) per cubic yard',
    },
    {
      Code: 'L93',
      Name: 'ton (US short) per cubic yard',
    },
    {
      Code: 'L94',
      Name: 'ton-force (US short)',
    },
    {
      Code: 'L95',
      Name: 'common year',
    },
    {
      Code: 'L96',
      Name: 'sidereal year',
    },
    {
      Code: 'L98',
      Name: 'yard per degree Fahrenheit',
    },
    {
      Code: 'L99',
      Name: 'yard per psi',
    },
    {
      Code: 'LA',
      Name: 'pound per cubic inch',
    },
    {
      Code: 'LAC',
      Name: 'lactose excess percentage',
    },
    {
      Code: 'LBR',
      Name: 'pound',
    },
    {
      Code: 'LBT',
      Name: 'troy pound (US)',
    },
    {
      Code: 'LD',
      Name: 'litre per day',
    },
    {
      Code: 'LEF',
      Name: 'leaf',
    },
    {
      Code: 'LF',
      Name: 'linear foot',
    },
    {
      Code: 'LH',
      Name: 'labour hour',
    },
    {
      Code: 'LK',
      Name: 'link',
    },
    {
      Code: 'LM',
      Name: 'linear metre',
    },
    {
      Code: 'LN',
      Name: 'length',
    },
    {
      Code: 'LO',
      Name: 'lot [unit of procurement]',
    },
    {
      Code: 'LP',
      Name: 'liquid pound',
    },
    {
      Code: 'LPA',
      Name: 'litre of pure alcohol',
    },
    {
      Code: 'LR',
      Name: 'layer',
    },
    {
      Code: 'LS',
      Name: 'lump sum',
    },
    {
      Code: 'LTN',
      Name: 'ton (UK) or long ton (US)',
    },
    {
      Code: 'LTR',
      Name: 'litre',
    },
    {
      Code: 'LUB',
      Name: 'metric ton, lubricating oil',
    },
    {
      Code: 'LUM',
      Name: 'lumen',
    },
    {
      Code: 'LUX',
      Name: 'lux',
    },
    {
      Code: 'LY',
      Name: 'linear yard',
    },
    {
      Code: 'M1',
      Name: 'milligram per litre',
    },
    {
      Code: 'M10',
      Name: 'reciprocal cubic yard',
    },
    {
      Code: 'M11',
      Name: 'cubic yard per degree Fahrenheit',
    },
    {
      Code: 'M12',
      Name: 'cubic yard per day',
    },
    {
      Code: 'M13',
      Name: 'cubic yard per hour',
    },
    {
      Code: 'M14',
      Name: 'cubic yard per psi',
    },
    {
      Code: 'M15',
      Name: 'cubic yard per minute',
    },
    {
      Code: 'M16',
      Name: 'cubic yard per second',
    },
    {
      Code: 'M17',
      Name: 'kilohertz metre',
    },
    {
      Code: 'M18',
      Name: 'gigahertz metre',
    },
    {
      Code: 'M19',
      Name: 'Beaufort',
    },
    {
      Code: 'M20',
      Name: 'reciprocal megakelvin or megakelvin to the power minus one',
    },
    {
      Code: 'M21',
      Name: 'reciprocal kilovolt - ampere reciprocal hour',
    },
    {
      Code: 'M22',
      Name: 'millilitre per square centimetre minute',
    },
    {
      Code: 'M23',
      Name: 'newton per centimetre',
    },
    {
      Code: 'M24',
      Name: 'ohm kilometre',
    },
    {
      Code: 'M25',
      Name: 'percent per degree Celsius',
    },
    {
      Code: 'M26',
      Name: 'gigaohm per metre',
    },
    {
      Code: 'M27',
      Name: 'megahertz metre',
    },
    {
      Code: 'M29',
      Name: 'kilogram per kilogram',
    },
    {
      Code: 'M30',
      Name: 'reciprocal volt - ampere reciprocal second',
    },
    {
      Code: 'M31',
      Name: 'kilogram per kilometre',
    },
    {
      Code: 'M32',
      Name: 'pascal second per litre',
    },
    {
      Code: 'M33',
      Name: 'millimole per litre',
    },
    {
      Code: 'M34',
      Name: 'newton metre per square metre',
    },
    {
      Code: 'M35',
      Name: 'millivolt - ampere',
    },
    {
      Code: 'M36',
      Name: '30-day month',
    },
    {
      Code: 'M37',
      Name: 'actual/360',
    },
    {
      Code: 'M38',
      Name: 'kilometre per second squared',
    },
    {
      Code: 'M39',
      Name: 'centimetre per second squared',
    },
    {
      Code: 'M4',
      Name: 'monetary value',
    },
    {
      Code: 'M40',
      Name: 'yard per second squared',
    },
    {
      Code: 'M41',
      Name: 'millimetre per second squared',
    },
    {
      Code: 'M42',
      Name: 'mile (statute mile) per second squared',
    },
    {
      Code: 'M43',
      Name: 'mil',
    },
    {
      Code: 'M44',
      Name: 'revolution',
    },
    {
      Code: 'M45',
      Name: 'degree [unit of angle] per second squared',
    },
    {
      Code: 'M46',
      Name: 'revolution per minute',
    },
    {
      Code: 'M47',
      Name: 'circular mil',
    },
    {
      Code: 'M48',
      Name: 'square mile (based on U.S. survey foot)',
    },
    {
      Code: 'M49',
      Name: 'chain (based on U.S. survey foot)',
    },
    {
      Code: 'M5',
      Name: 'microcurie',
    },
    {
      Code: 'M50',
      Name: 'furlong',
    },
    {
      Code: 'M51',
      Name: 'foot (U.S. survey)',
    },
    {
      Code: 'M52',
      Name: 'mile (based on U.S. survey foot)',
    },
    {
      Code: 'M53',
      Name: 'metre per pascal',
    },
    {
      Code: 'M55',
      Name: 'metre per radiant',
    },
    {
      Code: 'M56',
      Name: 'shake',
    },
    {
      Code: 'M57',
      Name: 'mile per minute',
    },
    {
      Code: 'M58',
      Name: 'mile per second',
    },
    {
      Code: 'M59',
      Name: 'metre per second pascal',
    },
    {
      Code: 'M60',
      Name: 'metre per hour',
    },
    {
      Code: 'M61',
      Name: 'inch per year',
    },
    {
      Code: 'M62',
      Name: 'kilometre per second',
    },
    {
      Code: 'M63',
      Name: 'inch per minute',
    },
    {
      Code: 'M64',
      Name: 'yard per second',
    },
    {
      Code: 'M65',
      Name: 'yard per minute',
    },
    {
      Code: 'M66',
      Name: 'yard per hour',
    },
    {
      Code: 'M67',
      Name: 'acre-foot (based on U.S. survey foot)',
    },
    {
      Code: 'M68',
      Name: 'cord (128 ft3)',
    },
    {
      Code: 'M69',
      Name: 'cubic mile (UK statute)',
    },
    {
      Code: 'M7',
      Name: 'micro-inch',
    },
    {
      Code: 'M70',
      Name: 'ton, register',
    },
    {
      Code: 'M71',
      Name: 'cubic metre per pascal',
    },
    {
      Code: 'M72',
      Name: 'bel',
    },
    {
      Code: 'M73',
      Name: 'kilogram per cubic metre pascal',
    },
    {
      Code: 'M74',
      Name: 'kilogram per pascal',
    },
    {
      Code: 'M75',
      Name: 'kilopound-force',
    },
    {
      Code: 'M76',
      Name: 'poundal',
    },
    {
      Code: 'M77',
      Name: 'kilogram metre per second squared',
    },
    {
      Code: 'M78',
      Name: 'pond',
    },
    {
      Code: 'M79',
      Name: 'square foot per hour',
    },
    {
      Code: 'M80',
      Name: 'stokes per pascal',
    },
    {
      Code: 'M81',
      Name: 'square centimetre per second',
    },
    {
      Code: 'M82',
      Name: 'square metre per second pascal',
    },
    {
      Code: 'M83',
      Name: 'denier',
    },
    {
      Code: 'M84',
      Name: 'pound per yard',
    },
    {
      Code: 'M85',
      Name: 'ton, assay',
    },
    {
      Code: 'M86',
      Name: 'pfund',
    },
    {
      Code: 'M87',
      Name: 'kilogram per second pascal',
    },
    {
      Code: 'M88',
      Name: 'tonne per month',
    },
    {
      Code: 'M89',
      Name: 'tonne per year',
    },
    {
      Code: 'M9',
      Name: 'million Btu per 1000 cubic foot',
    },
    {
      Code: 'M90',
      Name: 'kilopound per hour',
    },
    {
      Code: 'M91',
      Name: 'pound per pound',
    },
    {
      Code: 'M92',
      Name: 'pound-force foot',
    },
    {
      Code: 'M93',
      Name: 'newton metre per radian',
    },
    {
      Code: 'M94',
      Name: 'kilogram metre',
    },
    {
      Code: 'M95',
      Name: 'poundal foot',
    },
    {
      Code: 'M96',
      Name: 'poundal inch',
    },
    {
      Code: 'M97',
      Name: 'dyne metre',
    },
    {
      Code: 'M98',
      Name: 'kilogram centimetre per second',
    },
    {
      Code: 'M99',
      Name: 'gram centimetre per second',
    },
    {
      Code: 'MAH',
      Name: 'megavolt ampere reactive hour',
    },
    {
      Code: 'MAL',
      Name: 'megalitre',
    },
    {
      Code: 'MAM',
      Name: 'megametre',
    },
    {
      Code: 'MAR',
      Name: 'megavar',
    },
    {
      Code: 'MAW',
      Name: 'megawatt',
    },
    {
      Code: 'MBE',
      Name: 'thousand standard brick equivalent',
    },
    {
      Code: 'MBF',
      Name: 'thousand board foot',
    },
    {
      Code: 'MBR',
      Name: 'millibar',
    },
    {
      Code: 'MC',
      Name: 'microgram',
    },
    {
      Code: 'MCU',
      Name: 'millicurie',
    },
    {
      Code: 'MD',
      Name: 'air dry metric ton',
    },
    {
      Code: 'MGM',
      Name: 'milligram',
    },
    {
      Code: 'MHZ',
      Name: 'megahertz',
    },
    {
      Code: 'MIK',
      Name: 'square mile (statute mile)',
    },
    {
      Code: 'MIL',
      Name: 'thousand',
    },
    {
      Code: 'MIN',
      Name: 'minute [unit of time]',
    },
    {
      Code: 'MIO',
      Name: 'million',
    },
    {
      Code: 'MIU',
      Name: 'million international unit',
    },
    {
      Code: 'MKD',
      Name: 'Square Metre Day',
    },
    {
      Code: 'MKM',
      Name: 'Square Metre Month',
    },
    {
      Code: 'MKW',
      Name: 'Square Metre Week',
    },
    {
      Code: 'MLD',
      Name: 'milliard',
    },
    {
      Code: 'MLT',
      Name: 'millilitre',
    },
    {
      Code: 'MMK',
      Name: 'square millimetre',
    },
    {
      Code: 'MMQ',
      Name: 'cubic millimetre',
    },
    {
      Code: 'MMT',
      Name: 'millimetre',
    },
    {
      Code: 'MND',
      Name: 'kilogram, dry weight',
    },
    {
      Code: 'MNJ',
      Name: 'Mega Joule per Normalised cubic Metre',
    },
    {
      Code: 'MON',
      Name: 'month',
    },
    {
      Code: 'MPA',
      Name: 'megapascal',
    },
    {
      Code: 'MQD',
      Name: 'Cubic Metre Day',
    },
    {
      Code: 'MQH',
      Name: 'cubic metre per hour',
    },
    {
      Code: 'MQM',
      Name: 'Cubic Metre Month',
    },
    {
      Code: 'MQS',
      Name: 'cubic metre per second',
    },
    {
      Code: 'MQW',
      Name: 'Cubic Metre Week',
    },
    {
      Code: 'MRD',
      Name: 'Metre Day',
    },
    {
      Code: 'MRM',
      Name: 'Metre Month',
    },
    {
      Code: 'MRW',
      Name: 'Metre Week',
    },
    {
      Code: 'MSK',
      Name: 'metre per second squared',
    },
    {
      Code: 'MTK',
      Name: 'square metre',
    },
    {
      Code: 'MTQ',
      Name: 'cubic metre',
    },
    {
      Code: 'MTR',
      Name: 'metre',
    },
    {
      Code: 'MTS',
      Name: 'metre per second',
    },
    {
      Code: 'MTZ',
      Name: 'milihertz',
    },
    {
      Code: 'MVA',
      Name: 'megavolt - ampere',
    },
    {
      Code: 'MWH',
      Name: 'megawatt hour (1000Â kW.h)',
    },
    {
      Code: 'N1',
      Name: 'pen calorie',
    },
    {
      Code: 'N10',
      Name: 'pound foot per second',
    },
    {
      Code: 'N11',
      Name: 'pound inch per second',
    },
    {
      Code: 'N12',
      Name: 'Pferdestaerke',
    },
    {
      Code: 'N13',
      Name: 'centimetre of mercury (0 ÂºC)',
    },
    {
      Code: 'N14',
      Name: 'centimetre of water (4 ÂºC)',
    },
    {
      Code: 'N15',
      Name: 'foot of water (39.2 ÂºF)',
    },
    {
      Code: 'N16',
      Name: 'inch of mercury (32 ÂºF)',
    },
    {
      Code: 'N17',
      Name: 'inch of mercury (60 ÂºF)',
    },
    {
      Code: 'N18',
      Name: 'inch of water (39.2 ÂºF)',
    },
    {
      Code: 'N19',
      Name: 'inch of water (60 ÂºF)',
    },
    {
      Code: 'N20',
      Name: 'kip per square inch',
    },
    {
      Code: 'N21',
      Name: 'poundal per square foot',
    },
    {
      Code: 'N22',
      Name: 'ounce (avoirdupois) per square inch',
    },
    {
      Code: 'N23',
      Name: 'conventional metre of water',
    },
    {
      Code: 'N24',
      Name: 'gram per square millimetre',
    },
    {
      Code: 'N25',
      Name: 'pound per square yard',
    },
    {
      Code: 'N26',
      Name: 'poundal per square inch',
    },
    {
      Code: 'N27',
      Name: 'foot to the fourth power',
    },
    {
      Code: 'N28',
      Name: 'cubic decimetre per kilogram',
    },
    {
      Code: 'N29',
      Name: 'cubic foot per pound',
    },
    {
      Code: 'N3',
      Name: 'print point',
    },
    {
      Code: 'N30',
      Name: 'cubic inch per pound',
    },
    {
      Code: 'N31',
      Name: 'kilonewton per metre',
    },
    {
      Code: 'N32',
      Name: 'poundal per inch',
    },
    {
      Code: 'N33',
      Name: 'pound-force per yard',
    },
    {
      Code: 'N34',
      Name: 'poundal second per square foot',
    },
    {
      Code: 'N35',
      Name: 'poise per pascal',
    },
    {
      Code: 'N36',
      Name: 'newton second per square metre',
    },
    {
      Code: 'N37',
      Name: 'kilogram per metre second',
    },
    {
      Code: 'N38',
      Name: 'kilogram per metre minute',
    },
    {
      Code: 'N39',
      Name: 'kilogram per metre day',
    },
    {
      Code: 'N40',
      Name: 'kilogram per metre hour',
    },
    {
      Code: 'N41',
      Name: 'gram per centimetre second',
    },
    {
      Code: 'N42',
      Name: 'poundal second per square inch',
    },
    {
      Code: 'N43',
      Name: 'pound per foot minute',
    },
    {
      Code: 'N44',
      Name: 'pound per foot day',
    },
    {
      Code: 'N45',
      Name: 'cubic metre per second pascal',
    },
    {
      Code: 'N46',
      Name: 'foot poundal',
    },
    {
      Code: 'N47',
      Name: 'inch poundal',
    },
    {
      Code: 'N48',
      Name: 'watt per square centimetre',
    },
    {
      Code: 'N49',
      Name: 'watt per square inch',
    },
    {
      Code: 'N50',
      Name: 'British thermal unit (international table) per square foot hour',
    },
    {
      Code: 'N51',
      Name: 'British thermal unit (thermochemical) per square foot hour',
    },
    {
      Code: 'N52',
      Name: 'British thermal unit (thermochemical) per square foot minute',
    },
    {
      Code: 'N53',
      Name: 'British thermal unit (international table) per square foot second',
    },
    {
      Code: 'N54',
      Name: 'British thermal unit (thermochemical) per square foot second',
    },
    {
      Code: 'N55',
      Name: 'British thermal unit (international table) per square inch second',
    },
    {
      Code: 'N56',
      Name: 'calorie (thermochemical) per square centimetre minute',
    },
    {
      Code: 'N57',
      Name: 'calorie (thermochemical) per square centimetre second',
    },
    {
      Code: 'N58',
      Name: 'British thermal unit (international table) per cubic foot',
    },
    {
      Code: 'N59',
      Name: 'British thermal unit (thermochemical) per cubic foot',
    },
    {
      Code: 'N60',
      Name: 'British thermal unit (international table) per degree Fahrenheit',
    },
    {
      Code: 'N61',
      Name: 'British thermal unit (thermochemical) per degree Fahrenheit',
    },
    {
      Code: 'N62',
      Name: 'British thermal unit (international table) per degree Rankine',
    },
    {
      Code: 'N63',
      Name: 'British thermal unit (thermochemical) per degree Rankine',
    },
    {
      Code: 'N64',
      Name: 'British thermal unit (thermochemical) per pound degree Rankine',
    },
    {
      Code: 'N65',
      Name: 'kilocalorie (international table) per gram kelvin',
    },
    {
      Code: 'N66',
      Name: 'British thermal unit (39 ÂºF)',
    },
    {
      Code: 'N67',
      Name: 'British thermal unit (59 ÂºF)',
    },
    {
      Code: 'N68',
      Name: 'British thermal unit (60 ÂºF)',
    },
    {
      Code: 'N69',
      Name: 'calorie (20 ÂºC)',
    },
    {
      Code: 'N70',
      Name: 'quad (1015 BtuIT)',
    },
    {
      Code: 'N71',
      Name: 'therm (EC)',
    },
    {
      Code: 'N72',
      Name: 'therm (U.S.)',
    },
    {
      Code: 'N73',
      Name: 'British thermal unit (thermochemical) per pound',
    },
    {
      Code: 'N74',
      Name: 'British thermal unit (international table) per hour square foot degree Fahrenheit',
    },
    {
      Code: 'N75',
      Name: 'British thermal unit (thermochemical) per hour square foot degree Fahrenheit',
    },
    {
      Code: 'N76',
      Name: 'British thermal unit (international table) per second square foot degree Fahrenheit',
    },
    {
      Code: 'N77',
      Name: 'British thermal unit (thermochemical) per second square foot degree Fahrenheit',
    },
    {
      Code: 'N78',
      Name: 'kilowatt per square metre kelvin',
    },
    {
      Code: 'N79',
      Name: 'kelvin per pascal',
    },
    {
      Code: 'N80',
      Name: 'watt per metre degree Celsius',
    },
    {
      Code: 'N81',
      Name: 'kilowatt per metre kelvin',
    },
    {
      Code: 'N82',
      Name: 'kilowatt per metre degree Celsius',
    },
    {
      Code: 'N83',
      Name: 'metre per degree Celcius metre',
    },
    {
      Code: 'N84',
      Name: 'degree Fahrenheit hour per British thermal unit (international table)',
    },
    {
      Code: 'N85',
      Name: 'degree Fahrenheit hour per British thermal unit (thermochemical)',
    },
    {
      Code: 'N86',
      Name: 'degree Fahrenheit second per British thermal unit (international table)',
    },
    {
      Code: 'N87',
      Name: 'degree Fahrenheit second per British thermal unit (thermochemical)',
    },
    {
      Code: 'N88',
      Name: 'degree Fahrenheit hour square foot per British thermal unit (international table) inch',
    },
    {
      Code: 'N89',
      Name: 'degree Fahrenheit hour square foot per British thermal unit (thermochemical) inch',
    },
    {
      Code: 'N90',
      Name: 'kilofarad',
    },
    {
      Code: 'N91',
      Name: 'reciprocal joule',
    },
    {
      Code: 'N92',
      Name: 'picosiemens',
    },
    {
      Code: 'N93',
      Name: 'ampere per pascal',
    },
    {
      Code: 'N94',
      Name: 'franklin',
    },
    {
      Code: 'N95',
      Name: 'ampere minute',
    },
    {
      Code: 'N96',
      Name: 'biot',
    },
    {
      Code: 'N97',
      Name: 'gilbert',
    },
    {
      Code: 'N98',
      Name: 'volt per pascal',
    },
    {
      Code: 'N99',
      Name: 'picovolt',
    },
    {
      Code: 'NA',
      Name: 'milligram per kilogram',
    },
    {
      Code: 'NAR',
      Name: 'number of articles',
    },
    {
      Code: 'NCL',
      Name: 'number of cells',
    },
    {
      Code: 'NEW',
      Name: 'newton',
    },
    {
      Code: 'NF',
      Name: 'message',
    },
    {
      Code: 'NIL',
      Name: 'nil',
    },
    {
      Code: 'NIU',
      Name: 'number of international units',
    },
    {
      Code: 'NL',
      Name: 'load',
    },
    {
      Code: 'NM3',
      Name: 'Normalised cubic metre',
    },
    {
      Code: 'NMI',
      Name: 'nautical mile',
    },
    {
      Code: 'NMP',
      Name: 'number of packs',
    },
    {
      Code: 'NPT',
      Name: 'number of parts',
    },
    {
      Code: 'NT',
      Name: 'net ton',
    },
    {
      Code: 'NTU',
      Name: 'Nephelometric turbidity unit',
    },
    {
      Code: 'NU',
      Name: 'newton metre',
    },
    {
      Code: 'NX',
      Name: 'part per thousand',
    },
    {
      Code: 'OA',
      Name: 'panel',
    },
    {
      Code: 'ODE',
      Name: 'ozone depletion equivalent',
    },
    {
      Code: 'ODG',
      Name: 'ODS Grams',
    },
    {
      Code: 'ODK',
      Name: 'ODS Kilograms',
    },
    {
      Code: 'ODM',
      Name: 'ODS Milligrams',
    },
    {
      Code: 'OHM',
      Name: 'ohm',
    },
    {
      Code: 'ON',
      Name: 'ounce per square yard',
    },
    {
      Code: 'ONZ',
      Name: 'ounce (avoirdupois)',
    },
    {
      Code: 'OPM',
      Name: 'oscillations per minute',
    },
    {
      Code: 'OT',
      Name: 'overtime hour',
    },
    {
      Code: 'OZA',
      Name: 'fluid ounce (US)',
    },
    {
      Code: 'OZI',
      Name: 'fluid ounce (UK)',
    },
    {
      Code: 'P1',
      Name: 'percent',
    },
    {
      Code: 'P10',
      Name: 'coulomb per metre',
    },
    {
      Code: 'P11',
      Name: 'kiloweber',
    },
    {
      Code: 'P12',
      Name: 'gamma',
    },
    {
      Code: 'P13',
      Name: 'kilotesla',
    },
    {
      Code: 'P14',
      Name: 'joule per second',
    },
    {
      Code: 'P15',
      Name: 'joule per minute',
    },
    {
      Code: 'P16',
      Name: 'joule per hour',
    },
    {
      Code: 'P17',
      Name: 'joule per day',
    },
    {
      Code: 'P18',
      Name: 'kilojoule per second',
    },
    {
      Code: 'P19',
      Name: 'kilojoule per minute',
    },
    {
      Code: 'P2',
      Name: 'pound per foot',
    },
    {
      Code: 'P20',
      Name: 'kilojoule per hour',
    },
    {
      Code: 'P21',
      Name: 'kilojoule per day',
    },
    {
      Code: 'P22',
      Name: 'nanoohm',
    },
    {
      Code: 'P23',
      Name: 'ohm circular-mil per foot',
    },
    {
      Code: 'P24',
      Name: 'kilohenry',
    },
    {
      Code: 'P25',
      Name: 'lumen per square foot',
    },
    {
      Code: 'P26',
      Name: 'phot',
    },
    {
      Code: 'P27',
      Name: 'footcandle',
    },
    {
      Code: 'P28',
      Name: 'candela per square inch',
    },
    {
      Code: 'P29',
      Name: 'footlambert',
    },
    {
      Code: 'P30',
      Name: 'lambert',
    },
    {
      Code: 'P31',
      Name: 'stilb',
    },
    {
      Code: 'P32',
      Name: 'candela per square foot',
    },
    {
      Code: 'P33',
      Name: 'kilocandela',
    },
    {
      Code: 'P34',
      Name: 'millicandela',
    },
    {
      Code: 'P35',
      Name: 'Hefner-Kerze',
    },
    {
      Code: 'P36',
      Name: 'international candle',
    },
    {
      Code: 'P37',
      Name: 'British thermal unit (international table) per square foot',
    },
    {
      Code: 'P38',
      Name: 'British thermal unit (thermochemical) per square foot',
    },
    {
      Code: 'P39',
      Name: 'calorie (thermochemical) per square centimetre',
    },
    {
      Code: 'P40',
      Name: 'langley',
    },
    {
      Code: 'P41',
      Name: 'decade (logarithmic)',
    },
    {
      Code: 'P42',
      Name: 'pascal squared second',
    },
    {
      Code: 'P43',
      Name: 'bel per metre',
    },
    {
      Code: 'P44',
      Name: 'pound mole',
    },
    {
      Code: 'P45',
      Name: 'pound mole per second',
    },
    {
      Code: 'P46',
      Name: 'pound mole per minute',
    },
    {
      Code: 'P47',
      Name: 'kilomole per kilogram',
    },
    {
      Code: 'P48',
      Name: 'pound mole per pound',
    },
    {
      Code: 'P49',
      Name: 'newton square metre per ampere',
    },
    {
      Code: 'P5',
      Name: 'five pack',
    },
    {
      Code: 'P50',
      Name: 'weber metre',
    },
    {
      Code: 'P51',
      Name: 'mol per kilogram pascal',
    },
    {
      Code: 'P52',
      Name: 'mol per cubic metre pascal',
    },
    {
      Code: 'P53',
      Name: 'unit pole',
    },
    {
      Code: 'P54',
      Name: 'milligray per second',
    },
    {
      Code: 'P55',
      Name: 'microgray per second',
    },
    {
      Code: 'P56',
      Name: 'nanogray per second',
    },
    {
      Code: 'P57',
      Name: 'gray per minute',
    },
    {
      Code: 'P58',
      Name: 'milligray per minute',
    },
    {
      Code: 'P59',
      Name: 'microgray per minute',
    },
    {
      Code: 'P60',
      Name: 'nanogray per minute',
    },
    {
      Code: 'P61',
      Name: 'gray per hour',
    },
    {
      Code: 'P62',
      Name: 'milligray per hour',
    },
    {
      Code: 'P63',
      Name: 'microgray per hour',
    },
    {
      Code: 'P64',
      Name: 'nanogray per hour',
    },
    {
      Code: 'P65',
      Name: 'sievert per second',
    },
    {
      Code: 'P66',
      Name: 'millisievert per second',
    },
    {
      Code: 'P67',
      Name: 'microsievert per second',
    },
    {
      Code: 'P68',
      Name: 'nanosievert per second',
    },
    {
      Code: 'P69',
      Name: 'rem per second',
    },
    {
      Code: 'P70',
      Name: 'sievert per hour',
    },
    {
      Code: 'P71',
      Name: 'millisievert per hour',
    },
    {
      Code: 'P72',
      Name: 'microsievert per hour',
    },
    {
      Code: 'P73',
      Name: 'nanosievert per hour',
    },
    {
      Code: 'P74',
      Name: 'sievert per minute',
    },
    {
      Code: 'P75',
      Name: 'millisievert per minute',
    },
    {
      Code: 'P76',
      Name: 'microsievert per minute',
    },
    {
      Code: 'P77',
      Name: 'nanosievert per minute',
    },
    {
      Code: 'P78',
      Name: 'reciprocal square inch',
    },
    {
      Code: 'P79',
      Name: 'pascal square metre per kilogram',
    },
    {
      Code: 'P80',
      Name: 'millipascal per metre',
    },
    {
      Code: 'P81',
      Name: 'kilopascal per metre',
    },
    {
      Code: 'P82',
      Name: 'hectopascal per metre',
    },
    {
      Code: 'P83',
      Name: 'standard atmosphere per metre',
    },
    {
      Code: 'P84',
      Name: 'technical atmosphere per metre',
    },
    {
      Code: 'P85',
      Name: 'torr per metre',
    },
    {
      Code: 'P86',
      Name: 'psi per inch',
    },
    {
      Code: 'P87',
      Name: 'cubic metre per second square metre',
    },
    {
      Code: 'P88',
      Name: 'rhe',
    },
    {
      Code: 'P89',
      Name: 'pound-force foot per inch',
    },
    {
      Code: 'P90',
      Name: 'pound-force inch per inch',
    },
    {
      Code: 'P91',
      Name: 'perm (0 ÂºC)',
    },
    {
      Code: 'P92',
      Name: 'perm (23 ÂºC)',
    },
    {
      Code: 'P93',
      Name: 'byte per second',
    },
    {
      Code: 'P94',
      Name: 'kilobyte per second',
    },
    {
      Code: 'P95',
      Name: 'megabyte per second',
    },
    {
      Code: 'P96',
      Name: 'reciprocal volt',
    },
    {
      Code: 'P97',
      Name: 'reciprocal radian',
    },
    {
      Code: 'P98',
      Name: 'pascal to the power sum of stoichiometric numbers',
    },
    {
      Code: 'P99',
      Name: 'mole per cubiv metre to the power sum of stoichiometric numbers',
    },
    {
      Code: 'PAL',
      Name: 'pascal',
    },
    {
      Code: 'PD',
      Name: 'pad',
    },
    {
      Code: 'PFL',
      Name: 'proof litre',
    },
    {
      Code: 'PGL',
      Name: 'proof gallon',
    },
    {
      Code: 'PI',
      Name: 'pitch',
    },
    {
      Code: 'PLA',
      Name: 'degree Plato',
    },
    {
      Code: 'PO',
      Name: 'pound per inch of length',
    },
    {
      Code: 'PQ',
      Name: 'page per inch',
    },
    {
      Code: 'PR',
      Name: 'pair',
    },
    {
      Code: 'PS',
      Name: 'pound-force per square inch',
    },
    {
      Code: 'PTD',
      Name: 'dry pint (US)',
    },
    {
      Code: 'PTI',
      Name: 'pint (UK)',
    },
    {
      Code: 'PTL',
      Name: 'liquid pint (US)',
    },
    {
      Code: 'PTN',
      Name: 'portion',
    },
    {
      Code: 'Q10',
      Name: 'joule per tesla',
    },
    {
      Code: 'Q11',
      Name: 'erlang',
    },
    {
      Code: 'Q12',
      Name: 'octet',
    },
    {
      Code: 'Q13',
      Name: 'octet per second',
    },
    {
      Code: 'Q14',
      Name: 'shannon',
    },
    {
      Code: 'Q15',
      Name: 'hartley',
    },
    {
      Code: 'Q16',
      Name: 'natural unit of information',
    },
    {
      Code: 'Q17',
      Name: 'shannon per second',
    },
    {
      Code: 'Q18',
      Name: 'hartley per second',
    },
    {
      Code: 'Q19',
      Name: 'natural unit of information per second',
    },
    {
      Code: 'Q20',
      Name: 'second per kilogramm',
    },
    {
      Code: 'Q21',
      Name: 'watt square metre',
    },
    {
      Code: 'Q22',
      Name: 'second per radian cubic metre',
    },
    {
      Code: 'Q23',
      Name: 'weber to the power minus one',
    },
    {
      Code: 'Q24',
      Name: 'reciprocal inch',
    },
    {
      Code: 'Q25',
      Name: 'dioptre',
    },
    {
      Code: 'Q26',
      Name: 'one per one',
    },
    {
      Code: 'Q27',
      Name: 'newton metre per metre',
    },
    {
      Code: 'Q28',
      Name: 'kilogram per square metre pascal second',
    },
    {
      Code: 'Q29',
      Name: 'microgram per hectogram',
    },
    {
      Code: 'Q3',
      Name: 'meal',
    },
    {
      Code: 'Q30',
      Name: 'pH (potential of Hydrogen)',
    },
    {
      Code: 'Q31',
      Name: 'kilojoule per gram',
    },
    {
      Code: 'Q32',
      Name: 'femtolitre',
    },
    {
      Code: 'Q33',
      Name: 'picolitre',
    },
    {
      Code: 'Q34',
      Name: 'nanolitre',
    },
    {
      Code: 'Q35',
      Name: 'megawatts per minute',
    },
    {
      Code: 'Q36',
      Name: 'square metre per cubic metre',
    },
    {
      Code: 'Q37',
      Name: 'Standard cubic metre per day',
    },
    {
      Code: 'Q38',
      Name: 'Standard cubic metre per hour',
    },
    {
      Code: 'Q39',
      Name: 'Normalized cubic metre per day',
    },
    {
      Code: 'Q40',
      Name: 'Normalized cubic metre per hour',
    },
    {
      Code: 'Q41',
      Name: 'Joule per normalised cubic metre',
    },
    {
      Code: 'Q42',
      Name: 'Joule per standard cubic metre',
    },
    {
      Code: 'QA',
      Name: 'page - facsimile',
    },
    {
      Code: 'QAN',
      Name: 'quarter (of a year)',
    },
    {
      Code: 'QB',
      Name: 'page - hardcopy',
    },
    {
      Code: 'QR',
      Name: 'quire',
    },
    {
      Code: 'QTD',
      Name: 'dry quart (US)',
    },
    {
      Code: 'QTI',
      Name: 'quart (UK)',
    },
    {
      Code: 'QTL',
      Name: 'liquid quart (US)',
    },
    {
      Code: 'QTR',
      Name: 'quarter (UK)',
    },
    {
      Code: 'R1',
      Name: 'pica',
    },
    {
      Code: 'R9',
      Name: 'thousand cubic metre',
    },
    {
      Code: 'RH',
      Name: 'running or operating hour',
    },
    {
      Code: 'RM',
      Name: 'ream',
    },
    {
      Code: 'ROM',
      Name: 'room',
    },
    {
      Code: 'RP',
      Name: 'pound per ream',
    },
    {
      Code: 'RPM',
      Name: 'revolutions per minute',
    },
    {
      Code: 'RPS',
      Name: 'revolutions per second',
    },
    {
      Code: 'RT',
      Name: 'revenue ton mile',
    },
    {
      Code: 'S3',
      Name: 'square foot per second',
    },
    {
      Code: 'S4',
      Name: 'square metre per second',
    },
    {
      Code: 'SAN',
      Name: 'half year (6 months)',
    },
    {
      Code: 'SCO',
      Name: 'score',
    },
    {
      Code: 'SCR',
      Name: 'scruple',
    },
    {
      Code: 'SEC',
      Name: 'second [unit of time]',
    },
    {
      Code: 'SET',
      Name: 'set',
    },
    {
      Code: 'SG',
      Name: 'segment',
    },
    {
      Code: 'SIE',
      Name: 'siemens',
    },
    {
      Code: 'SM3',
      Name: 'Standard cubic metre',
    },
    {
      Code: 'SMI',
      Name: 'mile (statute mile)',
    },
    {
      Code: 'SQ',
      Name: 'square',
    },
    {
      Code: 'SQR',
      Name: 'square, roofing',
    },
    {
      Code: 'SR',
      Name: 'strip',
    },
    {
      Code: 'STC',
      Name: 'stick',
    },
    {
      Code: 'STI',
      Name: 'stone (UK)',
    },
    {
      Code: 'STK',
      Name: 'stick, cigarette',
    },
    {
      Code: 'STL',
      Name: 'standard litre',
    },
    {
      Code: 'STN',
      Name: 'ton (US) or short ton (UK/US)',
    },
    {
      Code: 'STW',
      Name: 'straw',
    },
    {
      Code: 'SW',
      Name: 'skein',
    },
    {
      Code: 'SX',
      Name: 'shipment',
    },
    {
      Code: 'SYR',
      Name: 'syringe',
    },
    {
      Code: 'T0',
      Name: 'telecommunication line in service',
    },
    {
      Code: 'T3',
      Name: 'thousand piece',
    },
    {
      Code: 'TAH',
      Name: 'kiloampere hour (thousand ampere hour)',
    },
    {
      Code: 'TAN',
      Name: 'total acid number',
    },
    {
      Code: 'TI',
      Name: 'thousand square inch',
    },
    {
      Code: 'TIC',
      Name: 'metric ton, including container',
    },
    {
      Code: 'TIP',
      Name: 'metric ton, including inner packaging',
    },
    {
      Code: 'TKM',
      Name: 'tonne kilometre',
    },
    {
      Code: 'TMS',
      Name: 'kilogram of imported meat, less offal',
    },
    {
      Code: 'TNE',
      Name: 'tonne (metric ton)',
    },
    {
      Code: 'TP',
      Name: 'ten pack',
    },
    {
      Code: 'TPI',
      Name: 'teeth per inch',
    },
    {
      Code: 'TPR',
      Name: 'ten pair',
    },
    {
      Code: 'TQD',
      Name: 'thousand cubic metre per day',
    },
    {
      Code: 'TRL',
      Name: 'trillion (EUR)',
    },
    {
      Code: 'TST',
      Name: 'ten set',
    },
    {
      Code: 'TTS',
      Name: 'ten thousand sticks',
    },
    {
      Code: 'U1',
      Name: 'treatment',
    },
    {
      Code: 'U2',
      Name: 'tablet',
    },
    {
      Code: 'UB',
      Name: 'telecommunication line in service average',
    },
    {
      Code: 'UC',
      Name: 'telecommunication port',
    },
    {
      Code: 'VA',
      Name: 'volt - ampere per kilogram',
    },
    {
      Code: 'VLT',
      Name: 'volt',
    },
    {
      Code: 'VP',
      Name: 'percent volume',
    },
    {
      Code: 'W2',
      Name: 'wet kilo',
    },
    {
      Code: 'WA',
      Name: 'watt per kilogram',
    },
    {
      Code: 'WB',
      Name: 'wet pound',
    },
    {
      Code: 'WCD',
      Name: 'cord',
    },
    {
      Code: 'WE',
      Name: 'wet ton',
    },
    {
      Code: 'WEB',
      Name: 'weber',
    },
    {
      Code: 'WEE',
      Name: 'week',
    },
    {
      Code: 'WG',
      Name: 'wine gallon',
    },
    {
      Code: 'WHR',
      Name: 'watt hour',
    },
    {
      Code: 'WM',
      Name: 'working month',
    },
    {
      Code: 'WSD',
      Name: 'standard',
    },
    {
      Code: 'WTT',
      Name: 'watt',
    },
    {
      Code: 'X1',
      Name: "Gunter's chain",
    },
    {
      Code: 'YDK',
      Name: 'square yard',
    },
    {
      Code: 'YDQ',
      Name: 'cubic yard',
    },
    {
      Code: 'YRD',
      Name: 'yard',
    },
    {
      Code: 'Z11',
      Name: 'hanging container',
    },
    {
      Code: 'Z9',
      Name: 'nanomole',
    },
    {
      Code: 'ZP',
      Name: 'page',
    },
    {
      Code: 'ZZ',
      Name: 'mutually defined',
    },
    {
      Code: 'X1A',
      Name: 'Drum, steel',
    },
    {
      Code: 'X1B',
      Name: 'Drum, aluminium',
    },
    {
      Code: 'X1D',
      Name: 'Drum, plywood',
    },
    {
      Code: 'X1F',
      Name: 'Container, flexible',
    },
    {
      Code: 'X1G',
      Name: 'Drum, fibre',
    },
    {
      Code: 'X1W',
      Name: 'Drum, wooden',
    },
    {
      Code: 'X2C',
      Name: 'Barrel, wooden',
    },
    {
      Code: 'X3A',
      Name: 'Jerrican, steel',
    },
    {
      Code: 'X3H',
      Name: 'Jerrican, plastic',
    },
    {
      Code: 'X43',
      Name: 'Bag, super bulk',
    },
    {
      Code: 'X44',
      Name: 'Bag, polybag',
    },
    {
      Code: 'X4A',
      Name: 'Box, steel',
    },
    {
      Code: 'X4B',
      Name: 'Box, aluminium',
    },
    {
      Code: 'X4C',
      Name: 'Box, natural wood',
    },
    {
      Code: 'X4D',
      Name: 'Box, plywood',
    },
    {
      Code: 'X4F',
      Name: 'Box, reconstituted wood',
    },
    {
      Code: 'X4G',
      Name: 'Box, fibreboard',
    },
    {
      Code: 'X4H',
      Name: 'Box, plastic',
    },
    {
      Code: 'X5H',
      Name: 'Bag, woven plastic',
    },
    {
      Code: 'X5L',
      Name: 'Bag, textile',
    },
    {
      Code: 'X5M',
      Name: 'Bag, paper',
    },
    {
      Code: 'X6H',
      Name: 'Composite packaging, plastic receptacle',
    },
    {
      Code: 'X6P',
      Name: 'Composite packaging, glass receptacle',
    },
    {
      Code: 'X7A',
      Name: 'Case, car',
    },
    {
      Code: 'X7B',
      Name: 'Case, wooden',
    },
    {
      Code: 'X8A',
      Name: 'Pallet, wooden',
    },
    {
      Code: 'X8B',
      Name: 'Crate, wooden',
    },
    {
      Code: 'X8C',
      Name: 'Bundle, wooden',
    },
    {
      Code: 'XAA',
      Name: 'Intermediate bulk container, rigid plastic',
    },
    {
      Code: 'XAB',
      Name: 'Receptacle, fibre',
    },
    {
      Code: 'XAC',
      Name: 'Receptacle, paper',
    },
    {
      Code: 'XAD',
      Name: 'Receptacle, wooden',
    },
    {
      Code: 'XAE',
      Name: 'Aerosol',
    },
    {
      Code: 'XAF',
      Name: 'Pallet, modular, collars 80cms * 60cms',
    },
    {
      Code: 'XAG',
      Name: 'Pallet, shrinkwrapped',
    },
    {
      Code: 'XAH',
      Name: 'Pallet, 100cms * 110cms',
    },
    {
      Code: 'XAI',
      Name: 'Clamshell',
    },
    {
      Code: 'XAJ',
      Name: 'Cone',
    },
    {
      Code: 'XAL',
      Name: 'Ball',
    },
    {
      Code: 'XAM',
      Name: 'Ampoule, non-protected',
    },
    {
      Code: 'XAP',
      Name: 'Ampoule, protected',
    },
    {
      Code: 'XAT',
      Name: 'Atomizer',
    },
    {
      Code: 'XAV',
      Name: 'Capsule',
    },
    {
      Code: 'XB4',
      Name: 'Belt',
    },
    {
      Code: 'XBA',
      Name: 'Barrel',
    },
    {
      Code: 'XBB',
      Name: 'Bobbin',
    },
    {
      Code: 'XBC',
      Name: 'Bottlecrate / bottlerack',
    },
    {
      Code: 'XBD',
      Name: 'Board',
    },
    {
      Code: 'XBE',
      Name: 'Bundle',
    },
    {
      Code: 'XBF',
      Name: 'Balloon, non-protected',
    },
    {
      Code: 'XBG',
      Name: 'Bag',
    },
    {
      Code: 'XBH',
      Name: 'Bunch',
    },
    {
      Code: 'XBI',
      Name: 'Bin',
    },
    {
      Code: 'XBJ',
      Name: 'Bucket',
    },
    {
      Code: 'XBK',
      Name: 'Basket',
    },
    {
      Code: 'XBL',
      Name: 'Bale, compressed',
    },
    {
      Code: 'XBM',
      Name: 'Basin',
    },
    {
      Code: 'XBN',
      Name: 'Bale, non-compressed',
    },
    {
      Code: 'XBO',
      Name: 'Bottle, non-protected, cylindrical',
    },
    {
      Code: 'XBP',
      Name: 'Balloon, protected',
    },
    {
      Code: 'XBQ',
      Name: 'Bottle, protected cylindrical',
    },
    {
      Code: 'XBR',
      Name: 'Bar',
    },
    {
      Code: 'XBS',
      Name: 'Bottle, non-protected, bulbous',
    },
    {
      Code: 'XBT',
      Name: 'Bolt',
    },
    {
      Code: 'XBU',
      Name: 'Butt',
    },
    {
      Code: 'XBV',
      Name: 'Bottle, protected bulbous',
    },
    {
      Code: 'XBW',
      Name: 'Box, for liquids',
    },
    {
      Code: 'XBX',
      Name: 'Box',
    },
    {
      Code: 'XBY',
      Name: 'Board, in bundle/bunch/truss',
    },
    {
      Code: 'XBZ',
      Name: 'Bars, in bundle/bunch/truss',
    },
    {
      Code: 'XCA',
      Name: 'Can, rectangular',
    },
    {
      Code: 'XCB',
      Name: 'Crate, beer',
    },
    {
      Code: 'XCC',
      Name: 'Churn',
    },
    {
      Code: 'XCD',
      Name: 'Can, with handle and spout',
    },
    {
      Code: 'XCE',
      Name: 'Creel',
    },
    {
      Code: 'XCF',
      Name: 'Coffer',
    },
    {
      Code: 'XCG',
      Name: 'Cage',
    },
    {
      Code: 'XCH',
      Name: 'Chest',
    },
    {
      Code: 'XCI',
      Name: 'Canister',
    },
    {
      Code: 'XCJ',
      Name: 'Coffin',
    },
    {
      Code: 'XCK',
      Name: 'Cask',
    },
    {
      Code: 'XCL',
      Name: 'Coil',
    },
    {
      Code: 'XCM',
      Name: 'Card',
    },
    {
      Code: 'XCN',
      Name: 'Container, not otherwise specified as transport equipment',
    },
    {
      Code: 'XCO',
      Name: 'Carboy, non-protected',
    },
    {
      Code: 'XCP',
      Name: 'Carboy, protected',
    },
    {
      Code: 'XCQ',
      Name: 'Cartridge',
    },
    {
      Code: 'XCR',
      Name: 'Crate',
    },
    {
      Code: 'XCS',
      Name: 'Case',
    },
    {
      Code: 'XCT',
      Name: 'Carton',
    },
    {
      Code: 'XCU',
      Name: 'Cup',
    },
    {
      Code: 'XCV',
      Name: 'Cover',
    },
    {
      Code: 'XCW',
      Name: 'Cage, roll',
    },
    {
      Code: 'XCX',
      Name: 'Can, cylindrical',
    },
    {
      Code: 'XCY',
      Name: 'Cylinder',
    },
    {
      Code: 'XCZ',
      Name: 'Canvas',
    },
    {
      Code: 'XDA',
      Name: 'Crate, multiple layer, plastic',
    },
    {
      Code: 'XDB',
      Name: 'Crate, multiple layer, wooden',
    },
    {
      Code: 'XDC',
      Name: 'Crate, multiple layer, cardboard',
    },
    {
      Code: 'XDG',
      Name: 'Cage, Commonwealth Handling Equipment Pool (CHEP)',
    },
    {
      Code: 'XDH',
      Name: 'Box, Commonwealth Handling Equipment Pool (CHEP), Eurobox',
    },
    {
      Code: 'XDI',
      Name: 'Drum, iron',
    },
    {
      Code: 'XDJ',
      Name: 'Demijohn, non-protected',
    },
    {
      Code: 'XDK',
      Name: 'Crate, bulk, cardboard',
    },
    {
      Code: 'XDL',
      Name: 'Crate, bulk, plastic',
    },
    {
      Code: 'XDM',
      Name: 'Crate, bulk, wooden',
    },
    {
      Code: 'XDN',
      Name: 'Dispenser',
    },
    {
      Code: 'XDP',
      Name: 'Demijohn, protected',
    },
    {
      Code: 'XDR',
      Name: 'Drum',
    },
    {
      Code: 'XDS',
      Name: 'Tray, one layer no cover, plastic',
    },
    {
      Code: 'XDT',
      Name: 'Tray, one layer no cover, wooden',
    },
    {
      Code: 'XDU',
      Name: 'Tray, one layer no cover, polystyrene',
    },
    {
      Code: 'XDV',
      Name: 'Tray, one layer no cover, cardboard',
    },
    {
      Code: 'XDW',
      Name: 'Tray, two layers no cover, plastic tray',
    },
    {
      Code: 'XDX',
      Name: 'Tray, two layers no cover, wooden',
    },
    {
      Code: 'XDY',
      Name: 'Tray, two layers no cover, cardboard',
    },
    {
      Code: 'XEC',
      Name: 'Bag, plastic',
    },
    {
      Code: 'XED',
      Name: 'Case, with pallet base',
    },
    {
      Code: 'XEE',
      Name: 'Case, with pallet base, wooden',
    },
    {
      Code: 'XEF',
      Name: 'Case, with pallet base, cardboard',
    },
    {
      Code: 'XEG',
      Name: 'Case, with pallet base, plastic',
    },
    {
      Code: 'XEH',
      Name: 'Case, with pallet base, metal',
    },
    {
      Code: 'XEI',
      Name: 'Case, isothermic',
    },
    {
      Code: 'XEN',
      Name: 'Envelope',
    },
    {
      Code: 'XFB',
      Name: 'Flexibag',
    },
    {
      Code: 'XFC',
      Name: 'Crate, fruit',
    },
    {
      Code: 'XFD',
      Name: 'Crate, framed',
    },
    {
      Code: 'XFE',
      Name: 'Flexitank',
    },
    {
      Code: 'XFI',
      Name: 'Firkin',
    },
    {
      Code: 'XFL',
      Name: 'Flask',
    },
    {
      Code: 'XFO',
      Name: 'Footlocker',
    },
    {
      Code: 'XFP',
      Name: 'Filmpack',
    },
    {
      Code: 'XFR',
      Name: 'Frame',
    },
    {
      Code: 'XFT',
      Name: 'Foodtainer',
    },
    {
      Code: 'XFW',
      Name: 'Cart, flatbed',
    },
    {
      Code: 'XFX',
      Name: 'Bag, flexible container',
    },
    {
      Code: 'XGB',
      Name: 'Bottle, gas',
    },
    {
      Code: 'XGI',
      Name: 'Girder',
    },
    {
      Code: 'XGL',
      Name: 'Container, gallon',
    },
    {
      Code: 'XGR',
      Name: 'Receptacle, glass',
    },
    {
      Code: 'XGU',
      Name: 'Tray, containing horizontally stacked flat items',
    },
    {
      Code: 'XGY',
      Name: 'Bag, gunny',
    },
    {
      Code: 'XGZ',
      Name: 'Girders, in bundle/bunch/truss',
    },
    {
      Code: 'XHA',
      Name: 'Basket, with handle, plastic',
    },
    {
      Code: 'XHB',
      Name: 'Basket, with handle, wooden',
    },
    {
      Code: 'XHC',
      Name: 'Basket, with handle, cardboard',
    },
    {
      Code: 'XHG',
      Name: 'Hogshead',
    },
    {
      Code: 'XHN',
      Name: 'Hanger',
    },
    {
      Code: 'XHR',
      Name: 'Hamper',
    },
    {
      Code: 'XIA',
      Name: 'Package, display, wooden',
    },
    {
      Code: 'XIB',
      Name: 'Package, display, cardboard',
    },
    {
      Code: 'XIC',
      Name: 'Package, display, plastic',
    },
    {
      Code: 'XID',
      Name: 'Package, display, metal',
    },
    {
      Code: 'XIE',
      Name: 'Package, show',
    },
    {
      Code: 'XIF',
      Name: 'Package, flow',
    },
    {
      Code: 'XIG',
      Name: 'Package, paper wrapped',
    },
    {
      Code: 'XIH',
      Name: 'Drum, plastic',
    },
    {
      Code: 'XIK',
      Name: 'Package, cardboard, with bottle grip-holes',
    },
    {
      Code: 'XIL',
      Name: 'Tray, rigid, lidded stackable (CEN TS 14482:2002)',
    },
    {
      Code: 'XIN',
      Name: 'Ingot',
    },
    {
      Code: 'XIZ',
      Name: 'Ingots, in bundle/bunch/truss',
    },
    {
      Code: 'XJB',
      Name: 'Bag, jumbo',
    },
    {
      Code: 'XJC',
      Name: 'Jerrican, rectangular',
    },
    {
      Code: 'XJG',
      Name: 'Jug',
    },
    {
      Code: 'XJR',
      Name: 'Jar',
    },
    {
      Code: 'XJT',
      Name: 'Jutebag',
    },
    {
      Code: 'XJY',
      Name: 'Jerrican, cylindrical',
    },
    {
      Code: 'XKG',
      Name: 'Keg',
    },
    {
      Code: 'XKI',
      Name: 'Kit',
    },
    {
      Code: 'XLE',
      Name: 'Luggage',
    },
    {
      Code: 'XLG',
      Name: 'Log',
    },
    {
      Code: 'XLT',
      Name: 'Lot',
    },
    {
      Code: 'XLU',
      Name: 'Lug',
    },
    {
      Code: 'XLV',
      Name: 'Liftvan',
    },
    {
      Code: 'XLZ',
      Name: 'Logs, in bundle/bunch/truss',
    },
    {
      Code: 'XMA',
      Name: 'Crate, metal',
    },
    {
      Code: 'XMB',
      Name: 'Bag, multiply',
    },
    {
      Code: 'XMC',
      Name: 'Crate, milk',
    },
    {
      Code: 'XME',
      Name: 'Container, metal',
    },
    {
      Code: 'XMR',
      Name: 'Receptacle, metal',
    },
    {
      Code: 'XMS',
      Name: 'Sack, multi-wall',
    },
    {
      Code: 'XMT',
      Name: 'Mat',
    },
    {
      Code: 'XMW',
      Name: 'Receptacle, plastic wrapped',
    },
    {
      Code: 'XMX',
      Name: 'Matchbox',
    },
    {
      Code: 'XNA',
      Name: 'Not available',
    },
    {
      Code: 'XNE',
      Name: 'Unpacked or unpackaged',
    },
    {
      Code: 'XNF',
      Name: 'Unpacked or unpackaged, single unit',
    },
    {
      Code: 'XNG',
      Name: 'Unpacked or unpackaged, multiple units',
    },
    {
      Code: 'XNS',
      Name: 'Nest',
    },
    {
      Code: 'XNT',
      Name: 'Net',
    },
    {
      Code: 'XNU',
      Name: 'Net, tube, plastic',
    },
    {
      Code: 'XNV',
      Name: 'Net, tube, textile',
    },
    {
      Code: 'XO1',
      Name: 'Two sided cage on wheels with fixing strap',
    },
    {
      Code: 'XO2',
      Name: 'Trolley',
    },
    {
      Code: 'XO3',
      Name: 'Oneway pallet ISO 0 - 1/2 EURO Pallet',
    },
    {
      Code: 'XO4',
      Name: 'Oneway pallet ISO 1 - 1/1 EURO Pallet',
    },
    {
      Code: 'XO5',
      Name: 'Oneway pallet ISO 2 - 2/1 EURO Pallet',
    },
    {
      Code: 'XO6',
      Name: 'Pallet with exceptional dimensions',
    },
    {
      Code: 'XO7',
      Name: 'Wooden pallet  40 cm x 80 cm',
    },
    {
      Code: 'XO8',
      Name: 'Plastic pallet SRS 60 cm x 80 cm',
    },
    {
      Code: 'XO9',
      Name: 'Plastic pallet SRS 80 cm x 120 cm',
    },
    {
      Code: 'XOA',
      Name: 'Pallet, CHEP 40 cm x 60 cm',
    },
    {
      Code: 'XOB',
      Name: 'Pallet, CHEP 80 cm x 120 cm',
    },
    {
      Code: 'XOC',
      Name: 'Pallet, CHEP 100 cm x 120 cm',
    },
    {
      Code: 'XOD',
      Name: 'Pallet, AS 4068-1993',
    },
    {
      Code: 'XOE',
      Name: 'Pallet, ISO T11',
    },
    {
      Code: 'XOF',
      Name: 'Platform, unspecified weight or dimension',
    },
    {
      Code: 'XOG',
      Name: 'Pallet ISO 0 - 1/2 EURO Pallet',
    },
    {
      Code: 'XOH',
      Name: 'Pallet ISO 1 - 1/1 EURO Pallet',
    },
    {
      Code: 'XOI',
      Name: 'Pallet ISO 2 â€“ 2/1 EURO Pallet',
    },
    {
      Code: 'XOJ',
      Name: '1/4 EURO Pallet',
    },
    {
      Code: 'XOK',
      Name: 'Block',
    },
    {
      Code: 'XOL',
      Name: '1/8 EURO Pallet',
    },
    {
      Code: 'XOM',
      Name: 'Synthetic pallet ISO 1',
    },
    {
      Code: 'XON',
      Name: 'Synthetic pallet ISO 2',
    },
    {
      Code: 'XOP',
      Name: 'Wholesaler pallet',
    },
    {
      Code: 'XOQ',
      Name: 'Pallet 80 X 100 cm',
    },
    {
      Code: 'XOR',
      Name: 'Pallet 60 X 100 cm',
    },
    {
      Code: 'XOS',
      Name: 'Oneway pallet',
    },
    {
      Code: 'XOT',
      Name: 'Octabin',
    },
    {
      Code: 'XOU',
      Name: 'Container, outer',
    },
    {
      Code: 'XOV',
      Name: 'Returnable pallet',
    },
    {
      Code: 'XOW',
      Name: 'Large bag, pallet sized',
    },
    {
      Code: 'XOX',
      Name: 'A wheeled pallet with raised rim (81 x 67 x 135)',
    },
    {
      Code: 'XOY',
      Name: 'A Wheeled pallet with raised rim (81 x 72 x 135)',
    },
    {
      Code: 'XOZ',
      Name: 'Wheeled pallet with raised rim ( 81 x 60 x 16)',
    },
    {
      Code: 'XP1',
      Name: 'CHEP pallet 60 cm x 80 cm',
    },
    {
      Code: 'XP2',
      Name: 'Pan',
    },
    {
      Code: 'XP3',
      Name: 'LPR pallet 60 cm x 80 cm',
    },
    {
      Code: 'XP4',
      Name: 'LPR pallet 80 cm x 120 cm',
    },
    {
      Code: 'XPA',
      Name: 'Packet',
    },
    {
      Code: 'XPB',
      Name: 'Pallet, box Combined open-ended box and pallet',
    },
    {
      Code: 'XPC',
      Name: 'Parcel',
    },
    {
      Code: 'XPD',
      Name: 'Pallet, modular, collars 80cms * 100cms',
    },
    {
      Code: 'XPE',
      Name: 'Pallet, modular, collars 80cms * 120cms',
    },
    {
      Code: 'XPF',
      Name: 'Pen',
    },
    {
      Code: 'XPG',
      Name: 'Plate',
    },
    {
      Code: 'XPH',
      Name: 'Pitcher',
    },
    {
      Code: 'XPI',
      Name: 'Pipe',
    },
    {
      Code: 'XPJ',
      Name: 'Punnet',
    },
    {
      Code: 'XPK',
      Name: 'Package',
    },
    {
      Code: 'XPL',
      Name: 'Pail',
    },
    {
      Code: 'XPN',
      Name: 'Plank',
    },
    {
      Code: 'XPO',
      Name: 'Pouch',
    },
    {
      Code: 'XPP',
      Name: 'Piece',
    },
    {
      Code: 'XPR',
      Name: 'Receptacle, plastic',
    },
    {
      Code: 'XPT',
      Name: 'Pot',
    },
    {
      Code: 'XPU',
      Name: 'Tray',
    },
    {
      Code: 'XPV',
      Name: 'Pipes, in bundle/bunch/truss',
    },
    {
      Code: 'XPX',
      Name: 'Pallet',
    },
    {
      Code: 'XPY',
      Name: 'Plates, in bundle/bunch/truss',
    },
    {
      Code: 'XPZ',
      Name: 'Planks, in bundle/bunch/truss',
    },
    {
      Code: 'XQA',
      Name: 'Drum, steel, non-removable head',
    },
    {
      Code: 'XQB',
      Name: 'Drum, steel, removable head',
    },
    {
      Code: 'XQC',
      Name: 'Drum, aluminium, non-removable head',
    },
    {
      Code: 'XQD',
      Name: 'Drum, aluminium, removable head',
    },
    {
      Code: 'XQF',
      Name: 'Drum, plastic, non-removable head',
    },
    {
      Code: 'XQG',
      Name: 'Drum, plastic, removable head',
    },
    {
      Code: 'XQH',
      Name: 'Barrel, wooden, bung type',
    },
    {
      Code: 'XQJ',
      Name: 'Barrel, wooden, removable head',
    },
    {
      Code: 'XQK',
      Name: 'Jerrican, steel, non-removable head',
    },
    {
      Code: 'XQL',
      Name: 'Jerrican, steel, removable head',
    },
    {
      Code: 'XQM',
      Name: 'Jerrican, plastic, non-removable head',
    },
    {
      Code: 'XQN',
      Name: 'Jerrican, plastic, removable head',
    },
    {
      Code: 'XQP',
      Name: 'Box, wooden, natural wood, ordinary',
    },
    {
      Code: 'XQQ',
      Name: 'Box, wooden, natural wood, with sift proof walls',
    },
    {
      Code: 'XQR',
      Name: 'Box, plastic, expanded',
    },
    {
      Code: 'XQS',
      Name: 'Box, plastic, solid',
    },
    {
      Code: 'XRD',
      Name: 'Rod',
    },
    {
      Code: 'XRG',
      Name: 'Ring',
    },
    {
      Code: 'XRJ',
      Name: 'Rack, clothing hanger',
    },
    {
      Code: 'XRK',
      Name: 'Rack',
    },
    {
      Code: 'XRL',
      Name: 'Reel',
    },
    {
      Code: 'XRO',
      Name: 'Roll',
    },
    {
      Code: 'XRT',
      Name: 'Rednet',
    },
    {
      Code: 'XRZ',
      Name: 'Rods, in bundle/bunch/truss',
    },
    {
      Code: 'XSA',
      Name: 'Sack',
    },
    {
      Code: 'XSB',
      Name: 'Slab',
    },
    {
      Code: 'XSC',
      Name: 'Crate, shallow',
    },
    {
      Code: 'XSD',
      Name: 'Spindle',
    },
    {
      Code: 'XSE',
      Name: 'Sea-chest',
    },
    {
      Code: 'XSH',
      Name: 'Sachet',
    },
    {
      Code: 'XSI',
      Name: 'Skid',
    },
    {
      Code: 'XSK',
      Name: 'Case, skeleton',
    },
    {
      Code: 'XSL',
      Name: 'Slipsheet',
    },
    {
      Code: 'XSM',
      Name: 'Sheetmetal',
    },
    {
      Code: 'XSO',
      Name: 'Spool',
    },
    {
      Code: 'XSP',
      Name: 'Sheet, plastic wrapping',
    },
    {
      Code: 'XSS',
      Name: 'Case, steel',
    },
    {
      Code: 'XST',
      Name: 'Sheet',
    },
    {
      Code: 'XSU',
      Name: 'Suitcase',
    },
    {
      Code: 'XSV',
      Name: 'Envelope, steel',
    },
    {
      Code: 'XSW',
      Name: 'Shrinkwrapped',
    },
    {
      Code: 'XSX',
      Name: 'Set',
    },
    {
      Code: 'XSY',
      Name: 'Sleeve',
    },
    {
      Code: 'XSZ',
      Name: 'Sheets, in bundle/bunch/truss',
    },
    {
      Code: 'XT1',
      Name: 'Tablet',
    },
    {
      Code: 'XTB',
      Name: 'Tub',
    },
    {
      Code: 'XTC',
      Name: 'Tea-chest',
    },
    {
      Code: 'XTD',
      Name: 'Tube, collapsible',
    },
    {
      Code: 'XTE',
      Name: 'Tyre',
    },
    {
      Code: 'XTG',
      Name: 'Tank container, generic',
    },
    {
      Code: 'XTI',
      Name: 'Tierce',
    },
    {
      Code: 'XTK',
      Name: 'Tank, rectangular',
    },
    {
      Code: 'XTL',
      Name: 'Tub, with lid',
    },
    {
      Code: 'XTN',
      Name: 'Tin',
    },
    {
      Code: 'XTO',
      Name: 'Tun',
    },
    {
      Code: 'XTR',
      Name: 'Trunk',
    },
    {
      Code: 'XTS',
      Name: 'Truss',
    },
    {
      Code: 'XTT',
      Name: 'Bag, tote',
    },
    {
      Code: 'XTU',
      Name: 'Tube',
    },
    {
      Code: 'XTV',
      Name: 'Tube, with nozzle',
    },
    {
      Code: 'XTW',
      Name: 'Pallet, triwall',
    },
    {
      Code: 'XTY',
      Name: 'Tank, cylindrical',
    },
    {
      Code: 'XTZ',
      Name: 'Tubes, in bundle/bunch/truss',
    },
    {
      Code: 'XUC',
      Name: 'Uncaged',
    },
    {
      Code: 'XUN',
      Name: 'Unit',
    },
    {
      Code: 'XVA',
      Name: 'Vat',
    },
    {
      Code: 'XVG',
      Name: 'Bulk, gas (at 1031 mbar and 15Â°C)',
    },
    {
      Code: 'XVI',
      Name: 'Vial',
    },
    {
      Code: 'XVK',
      Name: 'Vanpack',
    },
    {
      Code: 'XVL',
      Name: 'Bulk, liquid',
    },
    {
      Code: 'XVN',
      Name: 'Vehicle',
    },
    {
      Code: 'XVO',
      Name: 'Bulk, solid, large particles (â€œnodulesâ€)',
    },
    {
      Code: 'XVP',
      Name: 'Vacuum-packed',
    },
    {
      Code: 'XVQ',
      Name: 'Bulk, liquefied gas (at abnormal temperature/pressure)',
    },
    {
      Code: 'XVR',
      Name: 'Bulk, solid, granular particles (â€œgrainsâ€)',
    },
    {
      Code: 'XVS',
      Name: 'Bulk, scrap metal',
    },
    {
      Code: 'XVY',
      Name: 'Bulk, solid, fine particles (â€œpowdersâ€)',
    },
    {
      Code: 'XWA',
      Name: 'Intermediate bulk container',
    },
    {
      Code: 'XWB',
      Name: 'Wickerbottle',
    },
    {
      Code: 'XWC',
      Name: 'Intermediate bulk container, steel',
    },
    {
      Code: 'XWD',
      Name: 'Intermediate bulk container, aluminium',
    },
    {
      Code: 'XWF',
      Name: 'Intermediate bulk container, metal',
    },
    {
      Code: 'XWG',
      Name: 'Intermediate bulk container, steel, pressurised > 10 kpa',
    },
    {
      Code: 'XWH',
      Name: 'Intermediate bulk container, aluminium, pressurised > 10 kpa',
    },
    {
      Code: 'XWJ',
      Name: 'Intermediate bulk container, metal, pressure 10 kpa',
    },
    {
      Code: 'XWK',
      Name: 'Intermediate bulk container, steel, liquid',
    },
    {
      Code: 'XWL',
      Name: 'Intermediate bulk container, aluminium, liquid',
    },
    {
      Code: 'XWM',
      Name: 'Intermediate bulk container, metal, liquid',
    },
    {
      Code: 'XWN',
      Name: 'Intermediate bulk container, woven plastic, without coat/liner',
    },
    {
      Code: 'XWP',
      Name: 'Intermediate bulk container, woven plastic, coated',
    },
    {
      Code: 'XWQ',
      Name: 'Intermediate bulk container, woven plastic, with liner',
    },
    {
      Code: 'XWR',
      Name: 'Intermediate bulk container, woven plastic, coated and liner',
    },
    {
      Code: 'XWS',
      Name: 'Intermediate bulk container, plastic film',
    },
    {
      Code: 'XWT',
      Name: 'Intermediate bulk container, textile with out coat/liner',
    },
    {
      Code: 'XWU',
      Name: 'Intermediate bulk container, natural wood, with inner liner',
    },
    {
      Code: 'XWV',
      Name: 'Intermediate bulk container, textile, coated',
    },
    {
      Code: 'XWW',
      Name: 'Intermediate bulk container, textile, with liner',
    },
    {
      Code: 'XWX',
      Name: 'Intermediate bulk container, textile, coated and liner',
    },
    {
      Code: 'XWY',
      Name: 'Intermediate bulk container, plywood, with inner liner',
    },
    {
      Code: 'XWZ',
      Name: 'Intermediate bulk container, reconstituted wood, with inner liner',
    },
    {
      Code: 'XXA',
      Name: 'Bag, woven plastic, without inner coat/liner',
    },
    {
      Code: 'XXB',
      Name: 'Bag, woven plastic, sift proof',
    },
    {
      Code: 'XXC',
      Name: 'Bag, woven plastic, water resistant',
    },
    {
      Code: 'XXD',
      Name: 'Bag, plastics film',
    },
    {
      Code: 'XXF',
      Name: 'Bag, textile, without inner coat/liner',
    },
    {
      Code: 'XXG',
      Name: 'Bag, textile, sift proof',
    },
    {
      Code: 'XXH',
      Name: 'Bag, textile, water resistant',
    },
    {
      Code: 'XXJ',
      Name: 'Bag, paper, multi-wall',
    },
    {
      Code: 'XXK',
      Name: 'Bag, paper, multi-wall, water resistant',
    },
    {
      Code: 'XYA',
      Name: 'Composite packaging, plastic receptacle in steel drum',
    },
    {
      Code: 'XYB',
      Name: 'Composite packaging, plastic receptacle in steel crate box',
    },
    {
      Code: 'XYC',
      Name: 'Composite packaging, plastic receptacle in aluminium drum',
    },
    {
      Code: 'XYD',
      Name: 'Composite packaging, plastic receptacle in aluminium crate',
    },
    {
      Code: 'XYF',
      Name: 'Composite packaging, plastic receptacle in wooden box',
    },
    {
      Code: 'XYG',
      Name: 'Composite packaging, plastic receptacle in plywood drum',
    },
    {
      Code: 'XYH',
      Name: 'Composite packaging, plastic receptacle in plywood box',
    },
    {
      Code: 'XYJ',
      Name: 'Composite packaging, plastic receptacle in fibre drum',
    },
    {
      Code: 'XYK',
      Name: 'Composite packaging, plastic receptacle in fibreboard box',
    },
    {
      Code: 'XYL',
      Name: 'Composite packaging, plastic receptacle in plastic drum',
    },
    {
      Code: 'XYM',
      Name: 'Composite packaging, plastic receptacle in solid plastic box',
    },
    {
      Code: 'XYN',
      Name: 'Composite packaging, glass receptacle in steel drum',
    },
    {
      Code: 'XYP',
      Name: 'Composite packaging, glass receptacle in steel crate box',
    },
    {
      Code: 'XYQ',
      Name: 'Composite packaging, glass receptacle in aluminium drum',
    },
    {
      Code: 'XYR',
      Name: 'Composite packaging, glass receptacle in aluminium crate',
    },
    {
      Code: 'XYS',
      Name: 'Composite packaging, glass receptacle in wooden box',
    },
    {
      Code: 'XYT',
      Name: 'Composite packaging, glass receptacle in plywood drum',
    },
    {
      Code: 'XYV',
      Name: 'Composite packaging, glass receptacle in wickerwork hamper',
    },
    {
      Code: 'XYW',
      Name: 'Composite packaging, glass receptacle in fibre drum',
    },
    {
      Code: 'XYX',
      Name: 'Composite packaging, glass receptacle in fibreboard box',
    },
    {
      Code: 'XYY',
      Name: 'Composite packaging, glass receptacle in expandable plastic pack',
    },
    {
      Code: 'XYZ',
      Name: 'Composite packaging, glass receptacle in solid plastic pack',
    },
    {
      Code: 'XZA',
      Name: 'Intermediate bulk container, paper, multi-wall',
    },
    {
      Code: 'XZB',
      Name: 'Bag, large',
    },
    {
      Code: 'XZC',
      Name: 'Intermediate bulk container, paper, multi-wall, water resistant',
    },
    {
      Code: 'XZD',
      Name: 'Intermediate bulk container, rigid plastic, with structural equipment, solids',
    },
    {
      Code: 'XZF',
      Name: 'Intermediate bulk container, rigid plastic, freestanding, solids',
    },
    {
      Code: 'XZG',
      Name: 'Intermediate bulk container, rigid plastic, with structural equipment,\n\t\t\t\tpressurised',
    },
    {
      Code: 'XZH',
      Name: 'Intermediate bulk container, rigid plastic, freestanding, pressurised',
    },
    {
      Code: 'XZJ',
      Name: 'Intermediate bulk container, rigid plastic, with structural equipment, liquids',
    },
    {
      Code: 'XZK',
      Name: 'Intermediate bulk container, rigid plastic, freestanding, liquids',
    },
    {
      Code: 'XZL',
      Name: 'Intermediate bulk container, composite, rigid plastic, solids',
    },
    {
      Code: 'XZM',
      Name: 'Intermediate bulk container, composite, flexible plastic, solids',
    },
    {
      Code: 'XZN',
      Name: 'Intermediate bulk container, composite, rigid plastic, pressurised',
    },
    {
      Code: 'XZP',
      Name: 'Intermediate bulk container, composite, flexible plastic, pressurised',
    },
    {
      Code: 'XZQ',
      Name: 'Intermediate bulk container, composite, rigid plastic, liquids',
    },
    {
      Code: 'XZR',
      Name: 'Intermediate bulk container, composite, flexible plastic, liquids',
    },
    {
      Code: 'XZS',
      Name: 'Intermediate bulk container, composite',
    },
    {
      Code: 'XZT',
      Name: 'Intermediate bulk container, fibreboard',
    },
    {
      Code: 'XZU',
      Name: 'Intermediate bulk container, flexible',
    },
    {
      Code: 'XZV',
      Name: 'Intermediate bulk container, metal, other than steel',
    },
    {
      Code: 'XZW',
      Name: 'Intermediate bulk container, natural wood',
    },
    {
      Code: 'XZX',
      Name: 'Intermediate bulk container, plywood',
    },
    {
      Code: 'XZY',
      Name: 'Intermediate bulk container, reconstituted wood',
    },
    {
      Code: 'XZZ',
      Name: 'Mutually defined',
    },
  ],
  getByCode: (code: string) =>
    unitTypesService.getAllTypes().find((unit) => unit.Code === code),
  getAsEnum: () =>
    z.enum(
      unitTypesService.getAllTypes().map((c) => c.Code) as [string, ...string[]]
    ),
};
