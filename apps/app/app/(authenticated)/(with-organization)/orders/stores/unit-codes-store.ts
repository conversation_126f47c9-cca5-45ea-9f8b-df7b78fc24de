import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Define the interface for the unit codes store
interface UnitCodesState {
  recentlyUsed: string[];
  favorites: string[];
  addToRecentlyUsed: (code: string) => void;
  addToFavorites: (code: string) => void;
  removeFromFavorites: (code: string) => void;
  clearRecentlyUsed: () => void;
}

// Maximum number of recently used codes to store
const MAX_RECENTLY_USED = 10;

// Create the store with persistence
export const useUnitCodesStore = create<UnitCodesState>()(
  persist(
    (set) => ({
      recentlyUsed: [],
      favorites: [],

      // Add a code to recently used
      addToRecentlyUsed: (code: string) =>
        set((state) => {
          // Remove the code if it already exists to avoid duplicates
          const filteredCodes = state.recentlyUsed.filter((c) => c !== code);

          // Add the new code at the beginning and limit the array size
          return {
            recentlyUsed: [code, ...filteredCodes].slice(0, MAX_RECENTLY_USED),
          };
        }),

      // Add a code to favorites
      addToFavorites: (code: string) =>
        set((state) => {
          // Only add if it doesn't already exist
          if (state.favorites.includes(code)) {
            return state;
          }
          return {
            favorites: [...state.favorites, code],
          };
        }),

      // Remove a code from favorites
      removeFromFavorites: (code: string) =>
        set((state) => ({
          favorites: state.favorites.filter((c) => c !== code),
        })),

      // Clear all recently used codes
      clearRecentlyUsed: () =>
        set({
          recentlyUsed: [],
        }),
    }),
    {
      name: 'unit-codes-storage', // Name for localStorage
    }
  )
);
