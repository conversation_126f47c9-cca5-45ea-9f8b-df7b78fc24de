'use server';

import { ApiEndpoint, fetchWithAuth } from '@/app/lib/api';
import type { Order } from '@/app/types';
import { getJwtFromSession } from '@/app/utils/auth-helpers';
import { urlSerialize } from '@repo/design-system/lib/utils';
import { log } from '@repo/observability/log';

type CoreResponse<T> = {
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    first_page?: number;
    is_empty?: boolean;
    has_total?: boolean;
    hasMore_pages?: boolean;
    has_pages?: boolean;
  };
  data: T[];
};

export async function getOrders(
  page = 1,
  perPage = 10
): Promise<CoreResponse<Order>> {
  try {
    const orders = await fetchWithAuth<CoreResponse<Order>>(
      urlSerialize(ApiEndpoint.ORDERS, { page, per_page: perPage }),
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getOrders', { orders });
    return orders;
  } catch (error) {
    log.warn('Failed to fetch orders from core backend', { error });
    throw new Error('Failed to fetch orders from core backend');
  }
}

export async function getOrder(id: string): Promise<{ data: Order }> {
  try {
    const order = await fetchWithAuth<{ data: Order }>(
      `${ApiEndpoint.ORDERS}/${id}`,
      await getJwtFromSession(),
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    log.info('getOrder', { order });
    return order;
  } catch (error) {
    log.warn('Failed to fetch order from core backend', { error });
    throw new Error('Failed to fetch order from core backend');
  }
}
