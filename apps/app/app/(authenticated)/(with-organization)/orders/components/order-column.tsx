'use client';

import type { Order, SubmittedDocument } from '@/app/types';
import { Badge } from '@repo/design-system/components/ui/badge';
import type { ColumnDef } from '@tanstack/react-table';

const getStatusBadgeSubmittedDocument = (
  status: SubmittedDocument['status']
) => {
  const statusColors: Record<SubmittedDocument['status'], string> = {
    Submitted: 'bg-blue-100 text-blue-800',
    Cancelled: 'bg-red-100 text-red-800',
    Valid: 'bg-green-100 text-green-800',
    Invalid: 'bg-red-100 text-red-800',
  };

  return <Badge className={statusColors[status]}>{status}</Badge>;
};

const getStatusBadge = (status: Order['status']) => {
  const statusColors: Record<Order['status'], string> = {
    Draft: 'bg-gray-100 text-gray-800',
    Pending: 'bg-yellow-100 text-yellow-800',
    Submitted: 'bg-blue-100 text-blue-800',
    Cancelled: 'bg-yellow-100 text-yellow-800',
    Invalid: 'bg-red-100 text-red-800',
    Valid: 'bg-green-100 text-green-800',
  };

  return <Badge className={statusColors[status]}>{status}</Badge>;
};

export const columns: ColumnDef<Order>[] = [
  // {
  //   accessorKey: 'id',
  //   header: ({ column }) => {
  //     return (
  //       <Button
  //         variant="ghost"
  //         size="sm"
  //         onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
  //       >
  //         Order ID
  //         <ArrowUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  // },
  {
    accessorKey: 'invoice_code',
    header: 'Invoice Number',
  },
  {
    accessorKey: 'buyer',
    header: 'Customer',
    cell: ({ row }) => row.original.buyer?.name ?? '-',
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      // Get the first document with details if available
      const documentWithDetails =
        row.original.submitted_documents.length > 0
          ? row.original.submitted_documents[0]
          : null;
      return documentWithDetails
        ? getStatusBadgeSubmittedDocument(documentWithDetails.status)
        : getStatusBadge(row.original.status);
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => new Date(row.original.created_at).toLocaleString(),
  },
];
