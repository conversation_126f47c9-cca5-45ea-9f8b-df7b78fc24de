'use client';

import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import { PlusCircle } from 'lucide-react';
import * as React from 'react';

import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@repo/design-system/components/ui/drawer';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';

import type { OrderFormData } from '../schemas/order-form-schema';
import { OrderForm } from './form/order-form';

interface OrderDialogProps {
  initialData?: OrderFormData;
  trigger?: React.ReactNode;
}

export function OrderDialog({ initialData, trigger }: OrderDialogProps) {
  const [open, setOpen] = React.useState(false);
  const isDesktop = useIsDesktop();
  // const router = useRouter();

  // Function to handle successful order creation/update
  const handleSuccess = () => {
    setOpen(false);
    // router.refresh();
  };

  // Default trigger if none provided
  const defaultTrigger = (
    <Button>
      <PlusCircle className="mr-2 h-4 w-4" />
      Create Order
    </Button>
  );

  // Content for both dialog and drawer
  const content = (
    <Tabs defaultValue="manual" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="manual">Manual Entry</TabsTrigger>
        <TabsTrigger value="csv">CSV Import</TabsTrigger>
        <TabsTrigger value="pdf">PDF Import</TabsTrigger>
      </TabsList>

      <TabsContent value="manual" className="mt-4">
        <OrderForm initialData={initialData} onSuccess={handleSuccess} />
      </TabsContent>

      <TabsContent value="csv" className="mt-4">
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <p className="text-muted-foreground">
            CSV import functionality will be implemented in Phase 2.
          </p>
          <p className="mt-2 text-muted-foreground text-sm">
            This will allow bulk import of orders with verification.
          </p>
        </div>
      </TabsContent>

      <TabsContent value="pdf" className="mt-4">
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <p className="text-muted-foreground">
            PDF import functionality will be implemented in Phase 3.
          </p>
          <p className="mt-2 text-muted-foreground text-sm">
            This will allow importing order data from PDF invoices with editable
            parsed content.
          </p>
        </div>
      </TabsContent>
    </Tabs>
  );

  // Render dialog for desktop
  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
        <DialogContent className="overflow-y-auto sm:max-h-[80vh] sm:max-w-[900px]">
          <DialogHeader>
            <DialogTitle>Create New Order</DialogTitle>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  // Render drawer for mobile
  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>{trigger || defaultTrigger}</DrawerTrigger>
      <DrawerContent>
        <DrawerHeader className="text-left">
          <DrawerTitle>Create New Order</DrawerTitle>
        </DrawerHeader>
        <div className="px-4">{content}</div>
        <DrawerFooter className="pt-2">
          <DrawerClose asChild>
            <Button variant="outline">Cancel</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
