'use client';

import {
  Stepper,
  StepperIndicator,
  Stepper<PERSON><PERSON>,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
} from '@repo/design-system/components/ui/stepper';
import { useOrderFormStore } from '../../store/order-form-store';

export const orderFormSteps = [
  {
    step: 1,
    title: 'Basic Information',
    mobileTitle: 'Basic',
    description: 'Enter the basic order information',
  },
  {
    step: 2,
    title: 'Line Items',
    mobileTitle: 'Items',
    description: 'Add line items to your order',
  },
  {
    step: 3,
    title: 'Additional Details',
    mobileTitle: 'Details',
    description: 'Add optional additional details',
  },
  {
    step: 4,
    title: 'Review & Submit',
    mobileTitle: 'Review',
    description: 'Review your order and submit',
  },
];

export function OrderFormStepper() {
  const { currentStep, setCurrentStep } = useOrderFormStore();

  // Handle step change from the stepper
  const handleStepChange = (step: number) => {
    // Convert 1-based step number back to 0-based index
    const targetStep = step - 1;

    // Only allow navigation to previous steps or the current step
    if (targetStep <= currentStep) {
      setCurrentStep(targetStep);
    }
  };

  return (
    <Stepper
      value={currentStep + 1}
      onValueChange={handleStepChange}
      className="my-6"
    >
      {orderFormSteps.map(({ step, title, mobileTitle }) => (
        <StepperItem
          key={step}
          step={step}
          className="not-last:flex-1"
          // Disable steps that are ahead of the current step
          disabled={step - 1 > currentStep}
        >
          <StepperTrigger className="flex-1 flex-col rounded">
            <StepperIndicator />
            <div className="text-center md:text-center">
              <StepperTitle className="text-xs md:text-sm">
                <span className="hidden md:inline">{title}</span>
                <span className="inline md:hidden">{mobileTitle}</span>
              </StepperTitle>
            </div>
          </StepperTrigger>
          {step < orderFormSteps.length && (
            <StepperSeparator className="max-md:mt-3.5 md:mx-4" />
          )}
        </StepperItem>
      ))}
    </Stepper>
  );
}
