'use client';

import {
  AdvanceSelect,
  type SelectableItem,
} from '@repo/design-system/components/ui/advance-select';
import { useEffect, useState } from 'react';
import { searchUnitTypes } from '../../actions/unit-types';
import { unitTypesService } from '../../schemas/unit-types';
import { useUnitCodesStore } from '../../stores/unit-codes-store';
import { suggestUnitCodes } from '../../utils/unit-code-suggestions';

// Define the unit code item type
interface UnitCodeItem extends SelectableItem {
  code: string;
  name: string;
}

// Define common unit categories
const UNIT_CATEGORIES = [
  {
    id: 'common',
    name: 'Common Units',
    codes: [
      'EA', // Each
      'PCS', // Piece
      'KGM', // Kilogram
      'LTR', // Liter
      'MTR', // Meter
      'BOX', // Box
      'SET', // Set
      'PCE', // Piece
      'HUR', // Hour
      'DAY', // Day
      'MON', // Month
    ],
  },
  {
    id: 'volume',
    name: 'Volume',
    codes: ['LTR', 'MLT', 'MTQ', 'MMQ', 'HTL', 'DLT', 'CLT'],
  },
  {
    id: 'weight',
    name: 'Weight',
    codes: ['KGM', 'GRM', 'MGM', 'TNE', 'LBR', 'ONZ', 'CGM'],
  },
  {
    id: 'length',
    name: 'Length',
    codes: ['MTR', 'KMT', 'DMT', 'CMT', 'MMT', 'INH', 'FOT', 'YRD'],
  },
  {
    id: 'time',
    name: 'Time',
    codes: ['HUR', 'DAY', 'WEE', 'MON', 'ANN', 'MIN', 'SEC'],
  },
  {
    id: 'packaging',
    name: 'Packaging',
    codes: ['BOX', 'BAG', 'CTN', 'CAN', 'JAR', 'PKG', 'PAL', 'XCT'],
  },
];

// Define the props for the unit code selector
interface UnitCodeSelectorProps {
  value?: string;
  onChange: (value: string) => void;
  productDescription?: string;
}

export function UnitCodeSelector({
  value = '',
  onChange,
  productDescription = '',
}: UnitCodeSelectorProps) {
  // Get store values and actions
  const {
    recentlyUsed,
    favorites,
    addToRecentlyUsed,
    addToFavorites,
    removeFromFavorites,
  } = useUnitCodesStore();

  // State for all unit types (loaded once)
  const [allUnitTypes, setAllUnitTypes] = useState<UnitCodeItem[]>([]);

  // Load all unit types once (for local operations)
  useEffect(() => {
    const unitTypes = unitTypesService.getAllTypes().map((unit) => ({
      id: unit.Code,
      label: `${unit.Name} (${unit.Code})`,
      value: unit.Code,
      code: unit.Code,
      name: unit.Name,
    }));
    setAllUnitTypes(unitTypes);
  }, []);

  // Get suggested unit codes based on product description
  const getSuggestedItems = () => {
    if (!productDescription) {
      return [];
    }

    const suggestedCodes = suggestUnitCodes(productDescription);
    return suggestedCodes
      .map((code) => {
        const unit = unitTypesService.getByCode(code);
        if (!unit) {
          return null;
        }
        return {
          id: unit.Code,
          label: `${unit.Name} (${unit.Code})`,
          value: unit.Code,
          code: unit.Code,
          name: unit.Name,
        };
      })
      .filter(Boolean) as UnitCodeItem[];
  };

  // Get common unit codes
  const getFrequentItems = () => {
    const commonCodes =
      UNIT_CATEGORIES.find((cat) => cat.id === 'common')?.codes || [];
    return commonCodes
      .map((code) => {
        const unit = unitTypesService.getByCode(code);
        if (!unit) {
          return null;
        }
        return {
          id: unit.Code,
          label: `${unit.Name} (${unit.Code})`,
          value: unit.Code,
          code: unit.Code,
          name: unit.Name,
        };
      })
      .filter(Boolean) as UnitCodeItem[];
  };

  // Search function for unit types
  const searchItems = async (term: string) => {
    if (!term || term.length < 2) {
      return [];
    }

    try {
      const results = await searchUnitTypes(term);
      return results.map((unit) => ({
        id: unit.Code,
        label: `${unit.Name} (${unit.Code})`,
        value: unit.Code,
        code: unit.Code,
        name: unit.Name,
      }));
    } catch (error) {
      console.error('Error searching unit types:', error);
      return [];
    }
  };

  // Get items by category
  const getItemsByCategory = async (categoryId: string) => {
    const category = UNIT_CATEGORIES.find((cat) => cat.id === categoryId);
    if (!category) {
      return [];
    }

    return category.codes
      .map((code) => {
        const unit = unitTypesService.getByCode(code);
        if (!unit) {
          return null;
        }
        return {
          id: unit.Code,
          label: `${unit.Name} (${unit.Code})`,
          value: unit.Code,
          code: unit.Code,
          name: unit.Name,
        };
      })
      .filter(Boolean) as UnitCodeItem[];
  };

  return (
    <AdvanceSelect<UnitCodeItem>
      value={value}
      onChange={onChange as (value: string | string[]) => void}
      isMulti={false}
      items={allUnitTypes}
      searchFunction={searchItems}
      getItemsByCategory={getItemsByCategory}
      getSuggestedItems={getSuggestedItems}
      getFrequentItems={getFrequentItems}
      placeholder="Select unit type"
      searchPlaceholder="Search unit types..."
      noResultsMessage="No unit types found"
      loadingMessage="Loading unit types..."
      categories={UNIT_CATEGORIES}
      favorites={favorites}
      recentlyUsed={recentlyUsed}
      onAddToFavorites={addToFavorites}
      onRemoveFromFavorites={removeFromFavorites}
      onAddToRecentlyUsed={addToRecentlyUsed}
    />
  );
}
