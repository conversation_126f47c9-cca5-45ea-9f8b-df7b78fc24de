'use client';
import { ApiEndpoint } from '@/app/lib/api';
import { clientFetchWithAuth } from '@/app/lib/api.client';
import { useJwtToken } from '@/app/utils/auth-client-helpers';
import { env } from '@/env';
import { Button } from '@repo/design-system/components/ui/button';
import { Calendar } from '@repo/design-system/components/ui/calendar';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/design-system/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Switch } from '@repo/design-system/components/ui/switch';
import { TimePicker } from '@repo/design-system/components/ui/time-picker';
import { formatDateTime } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import { BoxesIcon, CalendarIcon } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useEffect } from 'react';
import type { z } from 'zod';
import { basicInfoSchema } from '../../schemas/order-form-schema';
import { useOrderFormStore } from '../../store/order-form-store';

type BasicInfoData = z.infer<typeof basicInfoSchema>;

interface BasicInfoStepProps {
  onNext: () => void;
}

export function BasicInfoStep({ onNext }: BasicInfoStepProps) {
  const { jwt } = useJwtToken();
  const { formData, resetFormData, updateFormData, setBasicInfoComplete } =
    useOrderFormStore();

  // Initialize form with values from store
  const form = useForm<BasicInfoData>({
    resolver: zodResolver(basicInfoSchema),
    defaultValues: {
      isReady: formData.isReady ?? false,
      isConsolidate: formData.isConsolidate ?? false,
      invoiceCode: formData.invoiceCode ?? '',
      invoiceDateTime:
        typeof formData.invoiceDateTime === 'string'
          ? new Date(formData.invoiceDateTime)
          : formData.invoiceDateTime,
      buyerName: formData.buyerName ?? '',
      buyerTin: formData.buyerTin ?? '',
      registrationType: formData.registrationType ?? 'BRN',
      registrationNumber: formData.registrationNumber ?? '',
      sstRegistrationNumber: formData.sstRegistrationNumber ?? '',
      email: formData.email ?? '',
      address: formData.address ?? {
        addressLine0: '',
        addressLine1: '',
        addressLine2: '',
        postalZone: '',
        cityName: '',
        state: '',
        country: '',
      },
      contactNumber: formData.contactNumber ?? '',
      buyerType: formData.buyerType,
    },
  });

  // Handle form submission
  const onSubmit = async (data: BasicInfoData) => {
    if (!jwt) {
      return;
    }

    try {
      // check if invoiceCode is already used within the company
      const { data: response } = await clientFetchWithAuth<{
        data: { success: boolean };
      }>(ApiEndpoint.INVOICE_CODES, jwt, {
        method: 'POST',
        body: JSON.stringify({ invoice_code: data.invoiceCode }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // If the API returns success: false, it means the invoice code is already used
      if (response.success === false) {
        // Show error message
        toast.error(
          'This invoice code is already in use. Please use a different code.'
        );
        // Set error on the form field
        form.setError('invoiceCode', {
          type: 'manual',
          message: 'Invoice code already in use',
        });
        return;
      }

      // If we get here, the invoice code is valid
      updateFormData(data);
      setBasicInfoComplete(true);
      onNext();
    } catch (error) {
      // Handle any API errors
      toast.error('Failed to validate invoice code. Please try again.');
      console.error('Error validating invoice code:', error);
    }
  };

  // Update completion status when form becomes valid
  useEffect(() => {
    const subscription = form.watch(() => {
      setBasicInfoComplete(form.formState.isValid);
    });
    return () => subscription.unsubscribe();
  }, [form, setBasicInfoComplete]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Invoice</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="invoiceCode"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Invoice Code</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter invoice code" {...field} />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="invoiceDateTime"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Invoice Date</FormLabel>
                    <Popover>
                      <FormControl>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              'justify-start text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? (
                              formatDateTime(field.value, 'PPP HH:mm:ss')
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                      </FormControl>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                        <div className="border-border border-t p-3">
                          <TimePicker
                            setDate={field.onChange}
                            date={field.value}
                          />
                        </div>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <FormField
              control={form.control}
              name="isReady"
              render={({ field }) => (
                <GridFormItem className="flex w-full flex-row items-start justify-between">
                  <FormLabel className="space-x-2">
                    <BoxesIcon />
                    <div className="flex flex-col items-start space-y-3">
                      <div>
                        <p className="mb-2">
                          Will this order be submitted to MyInvois immediately
                          upon creation?
                        </p>
                        <p className="text-muted-foreground text-xs">
                          A draft order will not be submitted to MyInvois upon
                          creation in the system. You can decide when to submit
                          later.
                        </p>
                        <p className="text-muted-foreground text-xs">
                          While draft orders are not submitted to MyInvois, they
                          are still accessible in the system.
                        </p>
                      </div>
                      <div className="h-3">
                        <AnimatePresence>
                          {form.watch('isReady') && (
                            <motion.div
                              key="buyer-info"
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <p className="mb-2">
                                This order will be submitted to MyInvois
                                immediately upon creation.
                              </p>
                            </motion.div>
                          )}
                        </AnimatePresence>

                        <AnimatePresence>
                          {!form.watch('isReady') && (
                            <motion.div
                              key="buyer-info"
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                            >
                              <p className="mb-2">
                                This order will be created as draft and not
                                submitted to MyInvois immediately upon creation.
                              </p>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </div>
                  </FormLabel>
                  <FormControl>
                    <Switch
                      // className="data-[state=checked]:[&_span]:rtl:-translate-x-2 h-4 w-6 after:absolute after:inset-0 [&_span]:size-3 data-[state=checked]:[&_span]:translate-x-2"
                      // checked={form.watch('isConsolidate')}
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </CardContent>
        </Card>
        {/* <div className="border-input has-data-[state=checked]:border-primary/50 relative flex w-full items-start gap-2 rounded-md border p-4 shadow-xs outline-none"> */}

        <Card>
          <CardContent>
            <FormField
              control={form.control}
              name="isConsolidate"
              render={({ field }) => (
                <GridFormItem className="flex w-full flex-row items-start justify-between">
                  <FormLabel className="space-x-2">
                    <BoxesIcon />
                    <div className="flex flex-col items-start">
                      <div>
                        <p className="mb-2">
                          Is this part of a consolidated invoice?
                        </p>
                        <p className="text-muted-foreground text-xs">
                          A consolidated invoice is submitted every month xxx
                          (more description here)
                        </p>
                        <p className="text-muted-foreground text-xs">
                          No buyer information is needed for consolidated
                          invoices.
                        </p>
                      </div>
                    </div>
                  </FormLabel>
                  <FormControl>
                    <Switch
                      // className="data-[state=checked]:[&_span]:rtl:-translate-x-2 h-4 w-6 after:absolute after:inset-0 [&_span]:size-3 data-[state=checked]:[&_span]:translate-x-2"
                      // checked={form.watch('isConsolidate')}
                      checked={field.value}
                      onCheckedChange={(value) => {
                        field.onChange(value);

                        if (value) {
                          // If switching to consolidated
                          // Reset buyer-related fields in one go
                          form.reset({
                            ...form.getValues(),
                            isConsolidate: value,
                            address: undefined,
                            buyerTin: undefined,
                            buyerName: undefined,
                            email: undefined,
                            buyerType: undefined,
                            registrationType: undefined,
                            registrationNumber: undefined,
                            sstRegistrationNumber: undefined,
                            contactNumber: undefined,
                          });
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />
          </CardContent>

          <AnimatePresence>
            {!form.watch('isConsolidate') && (
              <motion.div
                key="buyer-info"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <CardHeader>
                  <CardTitle className="text-base">Buyer</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 pt-0">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="buyerType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Buyer Type</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={(
                                value:
                                  | 'LOCAL'
                                  | 'LOCAL_EXEMPTED_ENTITY'
                                  | 'FOREIGNER'
                              ) => {
                                form.setValue('buyerType', value);

                                if (value === 'LOCAL_EXEMPTED_ENTITY') {
                                  form.setValue(
                                    'buyerTin',
                                    env.NEXT_PUBLIC_MYINVOIS_API_EXEMPTED_ENTITY_TIN,
                                    { shouldValidate: false }
                                  );
                                  form.setValue('registrationType', 'BRN', {
                                    shouldValidate: false,
                                  });
                                }

                                if (value === 'FOREIGNER') {
                                  form.setValue(
                                    'buyerTin',
                                    env.NEXT_PUBLIC_MYINVOIS_API_FOREIGN_BUYER_TIN,
                                    { shouldValidate: false }
                                  );
                                  form.setValue(
                                    'registrationType',
                                    'PASSPORT',
                                    { shouldValidate: false }
                                  );
                                }

                                if (value === 'LOCAL') {
                                  form.setValue('buyerTin', '', {
                                    shouldValidate: false,
                                  });
                                  form.setValue('registrationType', 'BRN', {
                                    shouldValidate: false,
                                  });
                                }

                                // Validate once after all changes
                                form.trigger();
                              }}
                              value={field.value}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select buyer type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value={'LOCAL'}>Local</SelectItem>
                                <SelectItem value={'LOCAL_EXEMPTED_ENTITY'}>
                                  Exempted Entity
                                </SelectItem>
                                <SelectItem value={'FOREIGNER'}>
                                  Foreigner
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="buyerName"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Buyer Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter buyer name"
                              {...field}
                              required={form.watch('buyerType') === 'LOCAL'}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="buyerTin"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Buyer TIN</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter buyer TIN"
                              {...field}
                              disabled={[
                                'LOCAL_EXEMPTED_ENTITY',
                                'FOREIGNER',
                              ].includes(form.watch('buyerType') ?? '')}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="registrationType"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Registration Type</FormLabel>
                          <FormControl>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                              disabled={[
                                'LOCAL_EXEMPTED_ENTITY',
                                'FOREIGNER',
                              ].includes(form.watch('buyerType') ?? '')}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select Registration Type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value={'BRN'}>
                                  Business Registration Number (BRN)
                                </SelectItem>
                                <SelectItem value={'NRIC'}>
                                  Identity Card Number (NRIC)
                                </SelectItem>
                                <SelectItem
                                  value={'PASSPORT'}
                                  disabled={
                                    form.watch('buyerType') !== 'FOREIGNER'
                                  }
                                >
                                  Passport Number
                                </SelectItem>
                                <SelectItem value={'ARMY'}>
                                  Army Number
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="registrationNumber"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Registration Number</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter registration number"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="sstRegistrationNumber"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>SST Registration Number</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter SST registration number"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter email" {...field} />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="address.addressLine0"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Address Line 0</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter address line 0"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address.addressLine1"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Address Line 1</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter address line 1"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="address.addressLine2"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Address Line 2</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter address line 2"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address.postalZone"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Postal Zone</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter postal zone"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="address.cityName"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter city"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address.state"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>State</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter state"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="address.country"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Country</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter country"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contactNumber"
                      render={({ field }) => (
                        <GridFormItem>
                          <FormLabel>Contact Number</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter contact number"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </GridFormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>

        <div className="flex justify-between">
          <Button variant="outline" onClick={resetFormData} type="button">
            Reset
          </Button>
          <Button type="submit">Next Step</Button>
        </div>
      </form>
    </Form>
  );
}
