import { Card, CardContent } from '@repo/design-system/components/ui/card';
import {
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useFormContext,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Switch } from '@repo/design-system/components/ui/switch';
import { AnimatePresence, motion } from 'motion/react';
import { useState } from 'react';

export function TaxExemptionDetails({
  lineItemIndex,
}: {
  lineItemIndex: number;
}) {
  // Note: This is a hack, because it will be a load of work to change the backend schema and type again and again.
  // I simply attach the exemption property at the last end of the tax details array.
  const form = useFormContext();

  const [hasExemption, setHasExemption] = useState<boolean>(false);

  return (
    <Card>
      <CardContent>
        <div className="mt-3 space-y-2">
          <h3 className="font-medium text-md">Tax Exemption</h3>
          <div className="flex justify-between">
            <div>
              <p className="mb-2">This item has a tax exemption.</p>
              <p className="text-muted-foreground text-xs">
                Tax exemption can be partially or fully exempted.
              </p>
              <p className="text-muted-foreground text-xs">
                Tax exempted amount will directly affect the tax amount from the
                tax types chosen.
              </p>
            </div>
            <Switch
              checked={hasExemption}
              onCheckedChange={(checked) => {
                setHasExemption(checked);
                if (checked === false) {
                  form.setValue(
                    `lineItems.${lineItemIndex}.taxExemption`,
                    undefined
                  );
                }

                if (checked === true) {
                  form.setValue(`lineItems.${lineItemIndex}.taxExemption`, {
                    taxableAmount: 0,
                    reason: '',
                  });
                }
              }}
            />
          </div>

          <AnimatePresence>
            {hasExemption && (
              <motion.div
                key="buyer-info"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-2"
              >
                <FormField
                  control={form.control}
                  name={`lineItems.${lineItemIndex}.taxExemption.taxableAmount`}
                  render={({ field }) => (
                    <GridFormItem>
                      <FormLabel>Exempted Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            const value = Number.parseFloat(e.target.value);
                            field.onChange(
                              Number.isNaN(value) ? undefined : value
                            );
                          }}
                          value={field.value === undefined ? '' : field.value}
                        />
                      </FormControl>
                      <FormMessage />
                    </GridFormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`lineItems.${lineItemIndex}.taxExemption.reason`}
                  render={({ field }) => (
                    <GridFormItem>
                      <FormLabel>Reason of the exemption</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Special case"
                          {...field}
                          value={field.value}
                        />
                      </FormControl>
                      <FormMessage />
                    </GridFormItem>
                  )}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </CardContent>
    </Card>
  );
}
