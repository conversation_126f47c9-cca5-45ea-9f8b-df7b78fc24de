'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { PlusCircle, Trash2 } from 'lucide-react';
import { useEffect } from 'react';
import type { z } from 'zod';
import { additionalDetailsSchema } from '../../schemas/order-form-schema';
import { useOrderFormStore } from '../../store/order-form-store';

type AdditionalDetailsData = z.infer<typeof additionalDetailsSchema>;

interface AdditionalDetailsStepProps {
  onNext: () => void;
  onBack: () => void;
}

export function AdditionalDetailsStep({
  onNext,
  onBack,
}: AdditionalDetailsStepProps) {
  const { formData, updateFormData, setAdditionalInfoComplete } =
    useOrderFormStore();

  // Initialize form with values from store
  const form = useForm<AdditionalDetailsData>({
    resolver: zodResolver(additionalDetailsSchema),
    defaultValues: {
      payment: formData.payment,
      foreignCurrency: formData.foreignCurrency,
      additionalDocumentReference: formData.additionalDocumentReference,
    },
  });

  // Handle form submission
  const onSubmit = (data: AdditionalDetailsData) => {
    updateFormData(data);
    setAdditionalInfoComplete(true);
    onNext();
  };

  // Update completion status when form becomes valid
  useEffect(() => {
    // Additional details are optional, so always mark as valid
    setAdditionalInfoComplete(true);
  }, [setAdditionalInfoComplete]);

  // Add a new document reference
  const addDocumentReference = () => {
    const currentRefs = form.getValues('additionalDocumentReference') || [];
    form.setValue('additionalDocumentReference', [
      ...currentRefs,
      {
        id: '',
        type: undefined,
        description: '',
      },
    ]);
  };

  // Remove a document reference
  const removeDocumentReference = (index: number) => {
    const currentRefs = form.getValues('additionalDocumentReference') || [];
    form.setValue(
      'additionalDocumentReference',
      currentRefs.filter((_, i) => i !== index)
    );
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Payment Information Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Payment Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 pt-0">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="payment.mode"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Payment Mode</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment mode" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="01">Cash</SelectItem>
                        <SelectItem value="02">Cheque</SelectItem>
                        <SelectItem value="03">Bank Transfer</SelectItem>
                        <SelectItem value="04">Credit Card</SelectItem>
                        <SelectItem value="05">Debit Card</SelectItem>
                        <SelectItem value="06">E-Wallet</SelectItem>
                        <SelectItem value="07">Digital Bank</SelectItem>
                        <SelectItem value="08">Others</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="payment.terms"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Payment Terms</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter payment terms" {...field} />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Foreign Currency Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Foreign Currency</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 pt-0">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="foreignCurrency.currencyCode"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Currency Code</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter currency code (e.g., USD)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="foreignCurrency.currencyExchangeRate"
                render={({ field }) => (
                  <GridFormItem>
                    <FormLabel>Exchange Rate</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Enter exchange rate"
                        {...field}
                        onChange={(e) =>
                          field.onChange(Number.parseFloat(e.target.value))
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Additional Document References Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-base">
              Additional Document References
            </CardTitle>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addDocumentReference}
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Reference
            </Button>
          </CardHeader>
          <CardContent className="space-y-4 pt-0">
            {(form.watch('additionalDocumentReference') || []).map(
              (_, index) => (
                <div
                  key={index}
                  className="relative mb-4 grid grid-cols-1 gap-4 border-b pb-4 md:grid-cols-3"
                >
                  <FormField
                    control={form.control}
                    name={`additionalDocumentReference.${index}.id`}
                    render={({ field }) => (
                      <GridFormItem>
                        <FormLabel>Reference ID</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter reference ID" {...field} />
                        </FormControl>
                        <FormMessage />
                      </GridFormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`additionalDocumentReference.${index}.type`}
                    render={({ field }) => (
                      <GridFormItem>
                        <FormLabel>Reference Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select reference type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="CustomsImportForm">
                              Customs Import Form
                            </SelectItem>
                            <SelectItem value="FreeTradeAgreement">
                              Free Trade Agreement
                            </SelectItem>
                            <SelectItem value="K2">K2</SelectItem>
                            <SelectItem value="CIF">CIF</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </GridFormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`additionalDocumentReference.${index}.description`}
                    render={({ field }) => (
                      <GridFormItem>
                        <FormLabel>Description</FormLabel>
                        <div className="flex items-center gap-2">
                          <FormControl>
                            <Input placeholder="Enter description" {...field} />
                          </FormControl>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeDocumentReference(index)}
                            className="h-9 w-9"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        <FormMessage />
                      </GridFormItem>
                    )}
                  />
                </div>
              )
            )}
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button type="submit">Next Step</Button>
        </div>
      </form>
    </Form>
  );
}
