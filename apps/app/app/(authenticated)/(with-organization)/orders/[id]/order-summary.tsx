'use client';
// import { useIsDesktop } from '@/app/hooks/use-is-desktop';
import type { LineItem, Order, SubmittedDocument } from '@/app/types';
import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import { Edit2 } from 'lucide-react';
import { useMemo, useState } from 'react';
import { classificationCodesService } from '../schemas/classifications';
import { taxTypesService } from '../schemas/tax-types';
import { unitTypesService } from '../schemas/unit-types';
import { UpdateBuyerForm } from './components/update-buyer-form';
import { UpdateLineItemForm } from './components/update-line-item-form';
import { UpdateOrderInformationForm } from './components/update-order-information-form';

interface OrderSummaryProps {
  order?: Order;
}

function OrderView({
  order,
}: {
  order: Order;
}) {
  // Get the first document with details if available
  const documentWithDetails =
    order.submitted_documents.length > 0 ? order.submitted_documents[0] : null;

  // Format status for display
  const getStatusBadge = (status: Order['status']) => {
    const statusColors: Record<Order['status'], string> = {
      Draft: 'bg-gray-100 text-gray-800',
      Pending: 'bg-yellow-100 text-yellow-800',
      Submitted: 'bg-blue-100 text-blue-800',
      Cancelled: 'bg-yellow-100 text-yellow-800',
      Invalid: 'bg-red-100 text-red-800',
      Valid: 'bg-green-100 text-green-800',
    };

    return <Badge className={statusColors[status]}>{status}</Badge>;
  };

  const getStatusBadgeSubmittedDocument = (
    status: SubmittedDocument['status']
  ) => {
    const statusColors: Record<SubmittedDocument['status'], string> = {
      Submitted: 'bg-blue-100 text-blue-800',
      Cancelled: 'bg-red-100 text-red-800',
      Valid: 'bg-green-100 text-green-800',
      Invalid: 'bg-red-100 text-red-800',
    };

    return <Badge className={statusColors[status]}>{status}</Badge>;
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-MY', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (_) {
      return dateString;
    }
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
    }).format(amount);
  };

  // Get classification description
  const getClassificationDescription = (code: string) => {
    const classification = classificationCodesService.getByCode(code);
    return classification ? classification.Description : code;
  };

  // Get tax type description
  const getTaxTypeDescription = (code: string) => {
    const taxType = taxTypesService.getByCode(code);
    return taxType ? taxType.description : code;
  };

  // Get unit type description
  const getUnitTypeDescription = (code: string | undefined) => {
    if (!code) {
      return '';
    }
    const unitType = unitTypesService.getByCode(code);
    return unitType ? unitType.Name : code;
  };

  // Update fields
  // const isDesktop = useIsDesktop();

  const [updateOrderInformationOpen, setUpdateOrderInformationOpen] =
    useState(false);
  const [updateBuyerOpen, setUpdateBuyerOpen] = useState(false);
  const [updateLineItemOpen, setUpdateLineItemOpen] = useState(false);

  const statusMessage = useMemo(() => {
    switch (order.status) {
      case 'Pending':
      case 'Invalid':
      case 'Cancelled':
        return 'Update Order';
      case 'Draft':
        return 'Only order with Pending status can be submitted to MyInvois';
      case 'Submitted':
        return 'Order has already been submitted to MyInvois and is pending for validation';
      case 'Valid':
        return 'Order has already been submitted to MyInvois and is valid';
      default:
        return 'Unknown status';
    }
  }, [order]);

  return (
    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
      {/* Order Information */}
      <Card>
        <CardHeader>
          <div className="flex justify-between">
            <CardTitle>Order Information</CardTitle>
            <Tooltip>
              <TooltipTrigger asChild>
                <div>
                  <Dialog
                    open={updateOrderInformationOpen}
                    onOpenChange={setUpdateOrderInformationOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="default"
                        size="lg"
                        type="button"
                        disabled={
                          ![
                            'Pending',
                            'Invalid',
                            'Cancelled',
                            'Draft',
                          ].includes(order.status)
                        }
                      >
                        <Edit2 />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="overflow-y-auto sm:max-h-[80vh] sm:max-w-[900px]">
                      <DialogHeader>
                        <DialogTitle>Create New Order</DialogTitle>
                      </DialogHeader>
                      <UpdateOrderInformationForm
                        initialData={{
                          isReady: order.is_ready,
                          externalId: order.external_id,
                          invoiceCode: order.invoice_code,
                          invoiceDateTime: new Date(
                            order.invoice_date_time.date
                          ),
                        }}
                        orderId={order.id}
                        setUpdateOrderInformationOpen={
                          setUpdateOrderInformationOpen
                        }
                      />
                    </DialogContent>
                  </Dialog>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>{statusMessage}</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </CardHeader>
        <CardContent>
          <dl className="grid grid-cols-1 gap-x-4 gap-y-2 sm:grid-cols-2">
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Invoice Code
              </dt>
              <dd className="mt-1">{order.invoice_code}</dd>
            </div>
            <div>{/* Blank space to align the UI */}</div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Status
              </dt>
              <dd className="mt-1">
                {documentWithDetails
                  ? getStatusBadgeSubmittedDocument(documentWithDetails.status)
                  : getStatusBadge(order.status)}
              </dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Order is Ready for submission
              </dt>
              <dd className="mt-1">
                <Badge
                  className={` ${order.is_ready ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                >
                  {order.is_ready ? 'Yes' : 'No'}
                </Badge>
              </dd>
            </div>

            {order.invoice_date_time && (
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Invoice Issuance Date
                </dt>
                <dd className="mt-1">
                  {formatDate(order.invoice_date_time.date)}
                </dd>
              </div>
            )}

            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Created At
              </dt>
              <dd className="mt-1">{formatDate(order.created_at)}</dd>
            </div>
            <div>
              <dt className="font-medium text-muted-foreground text-sm">
                Updated At
              </dt>
              <dd className="mt-1">{formatDate(order.updated_at)}</dd>
            </div>
          </dl>
        </CardContent>
      </Card>

      {/* Buyer Information */}
      {order.buyer && (
        <Card>
          <CardHeader>
            <div className="flex justify-between">
              <CardTitle>Buyer Information</CardTitle>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <Dialog
                      open={updateBuyerOpen}
                      onOpenChange={setUpdateBuyerOpen}
                    >
                      <DialogTrigger asChild>
                        <Button
                          variant="default"
                          size="lg"
                          type="button"
                          disabled={
                            ![
                              'Pending',
                              'Invalid',
                              'Cancelled',
                              'Draft',
                            ].includes(order.status)
                          }
                        >
                          <Edit2 />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="overflow-y-auto sm:max-h-[80vh] sm:max-w-[900px]">
                        <DialogHeader>
                          <DialogTitle>Create New Order</DialogTitle>
                        </DialogHeader>
                        <UpdateBuyerForm
                          initialData={{
                            buyerName: order.buyer.name,
                            buyerTin: order.buyer.tin,
                            registrationType: order.buyer.registrationType,
                            address: order.buyer.address,
                            contactNumber: order.buyer.contactNumber,
                            email: order.buyer.email,
                            registrationNumber: order.buyer.registrationNumber,
                            sstRegistrationNumber:
                              order.buyer.sstRegistrationNumber,
                          }}
                          orderId={order.id}
                          setUpdateBuyerOpen={setUpdateBuyerOpen}
                        />
                      </DialogContent>
                    </Dialog>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{statusMessage}</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-1 gap-x-4 gap-y-2">
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  Name
                </dt>
                <dd className="mt-1">{order.buyer.name}</dd>
              </div>
              <div>
                <dt className="font-medium text-muted-foreground text-sm">
                  TIN
                </dt>
                <dd className="mt-1">{order.buyer.tin}</dd>
              </div>
              {order.buyer && (
                <>
                  {order.buyer.registrationType && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Registration Type
                      </dt>
                      <dd className="mt-1">{order.buyer.registrationType}</dd>
                    </div>
                  )}
                  {order.buyer.registrationNumber && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Registration Number
                      </dt>
                      <dd className="mt-1">{order.buyer.registrationNumber}</dd>
                    </div>
                  )}
                  {order.buyer.sstRegistrationNumber && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        SST Registration Number
                      </dt>
                      <dd className="mt-1">
                        {order.buyer.sstRegistrationNumber}
                      </dd>
                    </div>
                  )}
                  {order.buyer.address && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Address
                      </dt>
                      <dd className="mt-1">
                        {order.buyer.address.addressLine0}
                        {order.buyer.address.addressLine1 && (
                          <>, {order.buyer.address.addressLine1}</>
                        )}
                        {order.buyer.address.addressLine2 && (
                          <>, {order.buyer.address.addressLine2}</>
                        )}
                        <br />
                        {order.buyer.address.cityName},{' '}
                        {order.buyer.address.state},{' '}
                        {order.buyer.address.postalZone}
                        <br />
                        {order.buyer.address.country}
                      </dd>
                    </div>
                  )}
                  {order.buyer.contactNumber && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Contact Number
                      </dt>
                      <dd className="mt-1">{order.buyer.contactNumber}</dd>
                    </div>
                  )}
                  {order.buyer.email && (
                    <div>
                      <dt className="font-medium text-muted-foreground text-sm">
                        Email
                      </dt>
                      <dd className="mt-1">{order.buyer.email}</dd>
                    </div>
                  )}
                </>
              )}
            </dl>
          </CardContent>
        </Card>
      )}

      {/* Line Items */}
      {order.line_items && order.line_items.length > 0 && (
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader>
            <div className="flex justify-between">
              <CardTitle>Line Items</CardTitle>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div>
                    <Dialog
                      open={updateLineItemOpen}
                      onOpenChange={setUpdateLineItemOpen}
                    >
                      <DialogTrigger asChild>
                        <Button
                          variant="default"
                          size="lg"
                          type="button"
                          disabled={
                            ![
                              'Pending',
                              'Invalid',
                              'Cancelled',
                              'Draft',
                            ].includes(order.status)
                          }
                        >
                          <Edit2 />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="overflow-y-auto sm:max-h-[80vh] sm:max-w-[900px]">
                        <DialogHeader>
                          <DialogTitle>Create New Order</DialogTitle>
                        </DialogHeader>
                        <UpdateLineItemForm
                          initialData={{
                            lineItems: order.line_items.map((lineItem) => {
                              return {
                                classifications: lineItem.classifications,
                                description: lineItem.description,
                                id: lineItem.id,
                                originCountry: lineItem.originCountry,
                                taxDetails: lineItem.taxDetails,
                                unit: lineItem.unit,
                                allowanceCharges: lineItem.allowanceCharges,
                                tarriffCode: lineItem.tarriffCode,
                                taxExemption: lineItem.taxExemption,
                              };
                            }),
                          }}
                          orderId={order.id}
                          setUpdateLineItemOpen={setUpdateLineItemOpen}
                        />
                      </DialogContent>
                    </Dialog>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{statusMessage}</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Tax</TableHead>
                  <TableHead>Sum Allowance/Charges</TableHead>
                  <TableHead>Tax Exemption</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order.line_items.map((item: LineItem, index: number) => (
                  <TableRow key={item.id || index}>
                    <TableCell className="font-medium">
                      {item.description}
                      <div className="mt-1 text-muted-foreground text-xs">
                        Classifications:{' '}
                        {item.classifications
                          .map((code) => getClassificationDescription(code))
                          .join(', ')}
                      </div>
                    </TableCell>
                    <TableCell>
                      {item.unit.count} {getUnitTypeDescription(item.unit.code)}
                    </TableCell>
                    <TableCell>{formatCurrency(item.unit.price)}</TableCell>
                    <TableCell>
                      {item.taxDetails.map((tax, index) => (
                        <div key={index} className="text-sm">
                          {getTaxTypeDescription(tax.taxType)}{' '}
                          {tax.taxRate.percentage
                            ? `(${tax.taxRate.percentage}%)`
                            : tax.taxRate.ratePerUnit
                              ? ` (${tax.taxRate.ratePerUnit} per unit)`
                              : ''}
                        </div>
                      ))}
                    </TableCell>
                    <TableCell>
                      {Number.parseFloat(
                        (item.allowanceCharges
                          ? item.allowanceCharges.reduce(
                              (sum, charge) =>
                                sum +
                                (charge.isCharge
                                  ? charge.amount
                                  : charge.amount * -1),
                              0
                            )
                          : 0
                        ).toFixed(5)
                      )}
                    </TableCell>
                    <TableCell>
                      {item.taxExemption
                        ? Number.parseFloat(
                            item.taxExemption.taxableAmount.toFixed(5)
                          )
                        : 'NA'}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(
                        Number.parseFloat(
                          (
                            item.unit.price * item.unit.count +
                            item.taxAmount +
                            (item.allowanceCharges
                              ? item.allowanceCharges.reduce(
                                  (sum, charge) =>
                                    sum +
                                    (charge.isCharge
                                      ? charge.amount
                                      : charge.amount * -1),
                                  0
                                )
                              : 0)
                          ).toFixed(5)
                        )
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Totals */}
      {order.legal_monetary_total && (
        <Card className="col-span-1 lg:col-span-2">
          <CardHeader>
            <CardTitle>Order Totals</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
              <div className="col-start-1">
                <dt className="font-medium text-muted-foreground">
                  Subtotal (excluding tax)
                </dt>
                <dd className="mt-1">
                  {formatCurrency(order.legal_monetary_total.excludingTax)}
                </dd>
              </div>

              {order.legal_monetary_total.discountValue &&
                order.legal_monetary_total.discountValue > 0 && (
                  <div className="col-start-1">
                    <dt className="font-medium text-muted-foreground">
                      Discount (including invoice level discount)
                    </dt>
                    <dd className="mt-1">
                      -
                      {formatCurrency(order.legal_monetary_total.discountValue)}
                    </dd>
                  </div>
                )}

              {order.legal_monetary_total.feeAmount &&
                order.legal_monetary_total.feeAmount > 0 && (
                  <div className="col-start-1">
                    <dt className="font-medium text-muted-foreground">
                      Additional Fees
                    </dt>
                    <dd className="mt-1">
                      {formatCurrency(order.legal_monetary_total.feeAmount)}
                    </dd>
                  </div>
                )}

              <div className="col-start-1">
                <dt className="font-medium text-muted-foreground">
                  Tax Amount
                </dt>
                <dd className="mt-1">
                  {formatCurrency(
                    order.legal_monetary_total.includingTax -
                      order.legal_monetary_total.excludingTax
                  )}
                </dd>
              </div>

              <div className="col-start-1 border-t pt-2">
                <dt className="font-medium">Total Amount</dt>
                <dd className="mt-1 font-bold">
                  {formatCurrency(order.legal_monetary_total.payableAmount)}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      )}

      {/* Additional Document References */}
      {order.additional_document_reference &&
        order.additional_document_reference.length > 0 && (
          <Card className="col-span-1 lg:col-span-2">
            <CardHeader>
              <CardTitle>Additional Document References</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Reference ID</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {order.additional_document_reference.map((ref, index) => (
                    <TableRow key={index}>
                      <TableCell>{ref.id}</TableCell>
                      <TableCell>{ref.type || 'N/A'}</TableCell>
                      <TableCell>{ref.description || 'N/A'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
    </div>
  );
}

export default function OrderSummary({ order }: OrderSummaryProps) {
  // If no order is provided, show a loading state
  if (!order) {
    return (
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Order Information</CardTitle>
          </CardHeader>
          <CardContent>Loading order information...</CardContent>
        </Card>
      </div>
    );
  }

  return <OrderView order={order} />;
}
