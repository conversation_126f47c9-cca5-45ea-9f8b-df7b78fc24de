import type { SubmittedDocument } from '@/app/types';
import { columns } from '../../../documents/components/submitted-document-column';
import { SubmittedDocumentTable } from '../../../documents/components/submitted-document-table';
import { getOrderDocuments } from '../../actions/get-order-documents';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export default async function OrderDocuments({ params }: PageProps) {
  const { id } = await params;

  // Fetch documents for this order
  let documents: SubmittedDocument[] = [];
  try {
    const response = await getOrderDocuments(id);
    documents = response.data.data;
  } catch (error) {
    console.error('Failed to fetch order documents:', error);
  }

  return (
    <SubmittedDocumentTable
      columns={columns}
      initialData={documents}
      initialMeta={{
        total: documents.length,
        per_page: 10,
        current_page: 1,
        last_page: Math.ceil(documents.length / 10),
        from: 1,
        to: documents.length,
      }}
    />
  );
}
