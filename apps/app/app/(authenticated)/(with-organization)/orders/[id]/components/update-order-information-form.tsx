import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';

import { Button } from '@repo/design-system/components/ui/button';
import { Calendar } from '@repo/design-system/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/design-system/components/ui/popover';
import { Switch } from '@repo/design-system/components/ui/switch';
import { TimePicker } from '@repo/design-system/components/ui/time-picker';
import { formatDateTime } from '@repo/design-system/lib/format';
import { cn } from '@repo/design-system/lib/utils';
import { BoxesIcon, CalendarIcon } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useUpdateOrder } from '../../hooks/useUpdateOrder';
import type { UpdateOrderInformationData } from '../../schemas/order-form-schema';

import { useJwtToken } from '@/app/utils/auth-client-helpers';
import { toast } from '@repo/design-system/components/ui/sonner';
import type { Dispatch, SetStateAction } from 'react';
interface UpdateOrderFormProps {
  initialData: UpdateOrderInformationData;
  orderId: number;
  setUpdateOrderInformationOpen: Dispatch<SetStateAction<boolean>>;
}

export function UpdateOrderInformationForm({
  initialData,
  orderId,
  setUpdateOrderInformationOpen,
}: UpdateOrderFormProps) {
  const { jwt } = useJwtToken();
  const form = useForm<UpdateOrderInformationData>({
    defaultValues: {
      ...initialData,
    },
  });

  const { updateOrderInformation, isLoading } = useUpdateOrder();

  const onSubmit = async (data: UpdateOrderInformationData) => {
    if (!jwt) {
      return;
    }

    try {
      await updateOrderInformation(
        {
          isReady: data.isReady,
          externalId: data.externalId,
          invoiceCode: data.invoiceCode,
          invoiceDateTime: data.invoiceDateTime,
        },
        orderId
      );
      setUpdateOrderInformationOpen(false);
      toast.success('Order updated successfully.');
    } catch (err) {
      toast.error('Failed to update order. Please try again.');
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="mx-auto w-full max-w-3xl space-y-4 px-4 md:px-0"
      >
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="invoiceCode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Invoice Code</FormLabel>
                <FormControl>
                  <Input placeholder="Invoice Code" {...field} />
                </FormControl>
                <FormDescription>E.g. INV-001</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* TODO: External ID field not available as we expect it should only be handle by API call, such as Shopify External invoice ID*/}
          {/* <FormField
            control={form.control}
            name="externalId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>External ID (optional)</FormLabel>
                <FormControl>
                  <Input placeholder="External ID" {...field} />
                </FormControl>
                <FormDescription>
                  Extra custom invoice code, if any. E.g. INV-001-extra-001
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          /> */}

          <FormField
            control={form.control}
            name="invoiceDateTime"
            render={({ field }) => (
              <GridFormItem>
                <FormLabel>Invoice Date</FormLabel>
                <Popover>
                  <FormControl>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'justify-start text-left font-normal',
                          !field.value && 'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {field.value ? (
                          formatDateTime(field.value, 'PPP HH:mm:ss')
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                  </FormControl>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={
                        field.value ? new Date(field.value) : new Date()
                      }
                      onSelect={field.onChange}
                    />
                    <div className="border-border border-t p-3">
                      <TimePicker setDate={field.onChange} date={field.value} />
                    </div>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </GridFormItem>
            )}
          />

          <FormField
            control={form.control}
            name="isReady"
            render={({ field }) => (
              <GridFormItem className="col-span-2 flex w-full flex-row items-start justify-between">
                <FormLabel className="space-x-2">
                  <BoxesIcon />
                  <div className="flex flex-col items-start space-y-3">
                    <div>
                      <p className="mb-2">
                        Will this order be ready to be submitted to MyInvois
                      </p>
                      <p className="text-muted-foreground text-xs">
                        A draft order cannot be submitted to MyInvois, you have
                        to manually set this to to be ready. The{' '}
                        <span className="font-extrabold">status</span> of the
                        invoice will be adjusted accordingly.
                      </p>
                    </div>
                    <div className="h-3">
                      <AnimatePresence>
                        {form.watch('isReady') && (
                          <motion.div
                            key="buyer-info"
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <p className="mb-2">
                              This order is ready to be submitted to MyInvois.
                            </p>
                          </motion.div>
                        )}
                      </AnimatePresence>

                      <AnimatePresence>
                        {!form.watch('isReady') && (
                          <motion.div
                            key="buyer-info"
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <p className="mb-2">
                              This order will not be available for submission
                              yet until it is set to ready again.
                            </p>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                </FormLabel>
                <FormControl>
                  <Switch
                    // className="data-[state=checked]:[&_span]:rtl:-translate-x-2 h-4 w-6 after:absolute after:inset-0 [&_span]:size-3 data-[state=checked]:[&_span]:translate-x-2"
                    // checked={form.watch('isConsolidate')}
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <FormMessage />
              </GridFormItem>
            )}
          />
        </div>

        <div className={'flex justify-end'}>
          <Button onClick={form.handleSubmit(onSubmit)} disabled={isLoading}>
            Submit
          </Button>
        </div>
      </form>
    </Form>
  );
}
