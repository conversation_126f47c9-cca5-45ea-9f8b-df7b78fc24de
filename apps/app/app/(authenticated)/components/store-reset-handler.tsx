'use client';

import { checkAndResetStore } from '@repo/auth/utils/sign-out-handler';
import { useEffect } from 'react';

/**
 * Component that checks and resets the store on page load if needed
 * This should be included in the layout to ensure it runs on every page load
 */
export function StoreResetHandler() {
  useEffect(() => {
    // Check and reset the store if needed
    checkAndResetStore();
  }, []);

  // This component doesn't render anything
  return null;
}
