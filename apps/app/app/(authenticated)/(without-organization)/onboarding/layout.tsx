import { JwtProvider } from '@/app/utils/auth-client-helpers';
import { auth } from '@repo/auth/server';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import type { ReactNode } from 'react';

type OnboardingProps = {
  readonly children: ReactNode;
};

const Onboarding = async ({ children }: OnboardingProps) => {
  // Check if user is authenticated
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return redirect('/sign-in');
  }

  // user might have or might not have an active organization
  // if they redirect here from CreateOrganization, they don't have an active organization
  // if they redirect here from the onboarding reminder, they have an active organization
  // hence, we do not need to redirect them based on organization

  return (
    <JwtProvider>
      <div className="flex h-screen w-full flex-col">
        <main className="flex flex-1 flex-col">{children}</main>
      </div>
    </JwtProvider>
  );
};

export default Onboarding;
