'use client';

import { ApiEndpoint } from '@/app/lib/api';
import { clientFetchWithAuth } from '@/app/lib/api.client';
import type { Company } from '@/app/types';
import { useJwtToken } from '@/app/utils/auth-client-helpers';
import { type ApiError, createApiError } from '@/app/utils/error-utils';
import type { BusinessFormData } from '@repo/auth/schemas/business-form-schema';
import { log } from '@repo/observability/log';
import { useState } from 'react';
import { mutate } from 'swr';

export function useCreateCompany() {
  const { jwt } = useJwtToken();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<ApiError | null>(null);

  const createCompany = async (
    body: Partial<BusinessFormData>
  ): Promise<Company> => {
    if (!jwt) {
      const authError = createApiError('Authentication required');
      setError(authError);
      throw authError;
    }

    setIsLoading(true);
    setError(null);

    try {
      const company = await clientFetchWithAuth<{ data: Company }>(
        ApiEndpoint.COMPANIES,
        jwt,
        {
          method: 'POST',
          body: JSON.stringify(body),
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      log.info('Company created successfully', { company });

      // Invalidate any cached company data
      mutate(
        (url) =>
          typeof url === 'string' && url.startsWith(ApiEndpoint.COMPANIES)
      );

      return company.data;
    } catch (err) {
      // Just pass through the error if it's already an ApiError
      // Otherwise, create a new ApiError with the original error as info
      const processedError =
        err instanceof Error
          ? (err as ApiError)
          : createApiError('Failed to create company', err);

      log.warn('Failed to create company', { error: err });
      setError(processedError);

      // Note: only set false if error occurs
      setIsLoading(false);
      throw processedError;
    }
  };

  return {
    createCompany,
    isLoading,
    error,
  };
}
