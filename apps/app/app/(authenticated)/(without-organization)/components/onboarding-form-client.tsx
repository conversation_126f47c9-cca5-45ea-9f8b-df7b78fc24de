'use client';

import { extractErrorMessage } from '@/app/utils/error-utils';
import { OnboardingForm } from '@repo/auth/components/onboarding';
import { toast } from '@repo/design-system/components/ui/sonner';
import { log } from '@repo/observability/log';
import { useRouter } from 'next/navigation';
import { useCreateCompany } from '../onboarding/hooks';

export function OnboardingFormClient() {
  const router = useRouter();
  const { createCompany, isLoading } = useCreateCompany();

  return (
    <OnboardingForm
      isSubmitting={isLoading}
      onComplete={async (formData) => {
        await createCompany(formData);
        toast.success('Onboarding completed!');

        // Redirect to dashboard after successful onboarding
        router.push('/');
      }}
      onError={(error) => {
        // Error handling is done in the component
        log.warn('Failed to complete onboarding:', { error });

        // Use the centralized error message extraction utility
        const errorMessage = extractErrorMessage(
          error,
          'Failed to complete onboarding. Please try again.'
        );

        // Display the specific error message
        toast.error(errorMessage);
      }}
    />
  );
}
