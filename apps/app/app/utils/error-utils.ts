'use client';

import { log } from '@repo/observability/log';

/**
 * Type for API errors with additional properties
 */
export interface ApiError extends Error {
  info?: unknown;
  status?: number;
}

/**
 * Extracts a human-readable error message from various error structures
 * @param err The error object to extract a message from
 * @param defaultMessage Default message to return if no specific message can be extracted
 * @returns A human-readable error message
 */
export function extractErrorMessage(
  err: unknown,
  defaultMessage = 'An unexpected error occurred. Please try again.'
): string {
  try {
    // Case 1: Error is directly a string
    if (typeof err === 'string') {
      return err;
    }

    // Case 2: Error is an object
    if (err && typeof err === 'object') {
      // Case 2a: Error has a message property
      if ('message' in err && typeof err.message === 'string') {
        return err.message;
      }

      // Case 2b: Error has an error property
      if ('error' in err && typeof err.error === 'string') {
        return err.error;
      }

      // Case 2c: Error has info property (from fetch error)
      if ('info' in err && err.info && typeof err.info === 'object') {
        const info = err.info as Record<string, string | unknown>;

        if ('error' in info && typeof info.error === 'string') {
          return info.error;
        }

        if ('message' in info && typeof info.message === 'string') {
          return info.message;
        }
      }
    }

    // Default case: return the default message
    return defaultMessage;
  } catch (parseError) {
    log.warn('Error parsing error object:', { parseError });
    return defaultMessage;
  }
}

/**
 * Creates a standardized API error with additional properties
 * @param message Error message
 * @param info Additional error information
 * @param status HTTP status code
 * @returns Standardized API error
 */
export function createApiError(
  message: string,
  info?: unknown,
  status?: number
): ApiError {
  const error = new Error(message) as ApiError;
  error.info = info;
  error.status = status;
  return error;
}
