'use client';

import { useSession } from '@repo/auth/client';
import { log } from '@repo/observability/log';
import {
  type ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

// Define the context type
interface JwtContextType {
  bearerToken: string | null;
  jwt: string | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<string | null>;
}

// Create the context with a default value
const JwtContext = createContext<JwtContextType | undefined>(undefined);

// JWT token expiration time in milliseconds (default: 55 minutes)
// Set slightly less than the actual expiration to ensure we refresh before it expires
const JWT_EXPIRATION_MS = 55 * 60 * 1000;

/**
 * Provider component that fetches and manages the JWT token
 */
export function JwtProvider({ children }: { children: ReactNode }) {
  const { data: session, isPending: isSessionLoading } = useSession();
  const [bearerToken, setBearerToken] = useState<string | null>(null); // bearer used to auth app & api
  const [jwt, setJwt] = useState<string | null>(null); // jwt used to auth app & core
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [lastFetchTime, setLastFetchTime] = useState<number | null>(null);

  const fetchJwt = useCallback(async (): Promise<string | null> => {
    if (!session?.session?.token) {
      setIsLoading(false);
      return null;
    }

    try {
      setIsLoading(true);

      const sessionToken = session.session.token;
      setBearerToken(sessionToken);

      const tokenResponse = await fetch('/api/auth/token', {
        headers: {
          Authorization: `Bearer ${sessionToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!tokenResponse.ok) {
        throw new Error(`Failed to fetch JWT: ${tokenResponse.status}`);
      }

      const tokenData = await tokenResponse.json();
      const jwtToken = tokenData.token;

      if (!jwtToken) {
        throw new Error('No JWT found in response');
      }

      setJwt(jwtToken);
      setLastFetchTime(Date.now());
      setError(null);
      return jwtToken;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      log.warn('Error fetching JWT token', { error });
      setError(error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  // Fetch JWT when session changes
  useEffect(() => {
    if (!isSessionLoading && session) {
      fetchJwt();
    } else if (!isSessionLoading && !session) {
      setIsLoading(false);
      setJwt(null);
      setLastFetchTime(null);
    }
  }, [session, isSessionLoading, fetchJwt]);

  // Set up token refresh
  useEffect(() => {
    if (!lastFetchTime || !jwt) {
      return;
    }

    // Calculate time until token refresh
    const timeUntilRefresh = JWT_EXPIRATION_MS - (Date.now() - lastFetchTime);

    // Set up refresh timer
    const refreshTimer = setTimeout(
      () => {
        log.debug('JWT token refresh timer triggered');
        fetchJwt();
      },
      Math.max(0, timeUntilRefresh)
    );

    return () => clearTimeout(refreshTimer);
  }, [lastFetchTime, jwt, fetchJwt]);

  // Provide the context value
  const contextValue: JwtContextType = {
    bearerToken,
    jwt,
    isLoading,
    error,
    refetch: fetchJwt,
  };

  return (
    <JwtContext.Provider value={contextValue}>{children}</JwtContext.Provider>
  );
}

/**
 * Hook to get JWT token from the context
 * @returns Object containing the JWT token and loading state
 */
export const useJwtToken = (): JwtContextType => {
  const context = useContext(JwtContext);

  if (context === undefined) {
    throw new Error('useJwtToken must be used within a JwtProvider');
  }

  return context;
};
