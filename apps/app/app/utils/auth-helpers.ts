import { env } from '@/env';
import { auth } from '@repo/auth/server';
import { log } from '@repo/observability/log';
import { headers } from 'next/headers';

/**
 * Find jwt from session
 */
export const getJwtFromSession = async (): Promise<
  [string | null, string | null]
> => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return [null, null];
  }

  // Get the session token and use it directly as a Bearer token (bearer plugin handles auth)
  const sessionToken = session.session.token;
  if (!sessionToken) {
    log.warn('No session token found in session');
    return [null, null];
  }

  const tokenResponse = await fetch(
    `${env.NEXT_PUBLIC_APP_URL}/api/auth/token`,
    {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
        'Content-Type': 'application/json',
      },
    }
  );

  const tokenData = await tokenResponse.json();
  const jwt = tokenData.token;

  if (!jwt) {
    log.warn('No JWT found in session');
    return [sessionToken, null];
  }

  return [sessionToken, jwt];
};
