---
title: Authentication
description: Learn how to authenticate your API requests
---

# Authentication

The BizCare MyInvois API only supports API key authetication method to secure your API requests.

### API Key Authentication

Include your API key in the request header.

**Header:**
```
X-API-Key: your-api-key-here
```

**Example Request:**
```bash
curl -X GET "https://api.myinvois.bizcare.my/api/orders" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

## Getting Your API Key

1. **Sign up** for an account on our platform
2. **Create an organization** and complete the onboarding process
3. **Navigate to Settings** → **API Keys**
4. **Generate a new API key** and store it securely

<Callout type="warning">
  **Keep your API key secure!** Never expose your API key in client-side code or public repositories.
</Callout>

## Authentication Errors

### 401 Unauthorized

Returned when authentication credentials are missing or invalid.

```json
{
  "error": "Unauthorized"
}
```

**Common causes:**
- Missing authentication header
- Invalid API key or token

### 403 Forbidden

Returned when you don't have permission to access the requested resource.

```json
{
  "error": "Access token is missing or invalid"
}
```

## Rate Limiting

API requests are rate-limited to ensure fair usage:

- **Standard Plan**: 1,000 requests per hour
- **Premium Plan**: 10,000 requests per hour
- **Enterprise Plan**: Custom limits

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Security Best Practices

1. **Use HTTPS**: Always make requests over HTTPS
2. **Rotate Keys**: Regularly rotate your API keys
3. **Scope Permissions**: Use the minimum required permissions
4. **Monitor Usage**: Track API usage and watch for unusual activity
5. **Store Securely**: Use environment variables or secure key management systems

## Testing Authentication

You can test your authentication setup with a simple request:

```bash
curl -X GET "https://api.myinvois.bizcare.my/api/companies" \
  -H "X-API-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

A successful response indicates your authentication is working correctly.
