---
title: Update User
description: Update an existing user's information
---

# Update User

Update an existing user's information with partial or complete data.

## Endpoint

```http
PUT /api/integrations/users/{id}
```

## Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | The unique identifier of the user to update |

## Request Body

All fields are optional for updates. Only include the fields you want to modify.

| Field | Type | Description |
|-------|------|-------------|
| `name` | string | Full name of the user |
| `firstName` | string | First name of the user |
| `lastName` | string | Last name of the user |
| `email` | string | Email address (must be unique) |
| `phone` | string | Phone number |
| `dob` | string | Date of birth (ISO 8601 format) |
| `role` | string | User role |

## Request Example

```json
{
  "name": "<PERSON>",
  "phone": "+1987654321",
  "role": "admin"
}
```

## Response Format

```json
{
  "id": "user-456",
  "name": "<PERSON>e <PERSON>",
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "phone": "+1987654321",
  "dob": "1985-03-20",
  "role": "admin",
  "members": [
    {
      "id": "member-789",
      "organizationId": "org-123",
      "role": "admin",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "createdAt": "2024-01-15T14:30:00Z",
  "updatedAt": "2024-01-16T09:15:00Z"
}
```

## Examples

### Partial Update

```bash
curl -X PUT "https://api.example.com/api/integrations/users/user-456" \
  -H "X-Invois-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+1987654321",
    "role": "admin"
  }'
```

### JavaScript Example

```javascript
const updateData = {
  name: "Jane Doe Smith",
  role: "admin"
};

const response = await fetch('https://api.example.com/api/integrations/users/user-456', {
  method: 'PUT',
  headers: {
    'X-Invois-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(updateData)
});

const updatedUser = await response.json();
```

## Validation Rules

- **Email**: Must be unique if provided
- **Role**: Must be one of: `super-admin`, `admin`, `customer`
- **Phone**: Must be valid phone number format if provided
- **Date of Birth**: Must be valid ISO 8601 date if provided

## Error Responses

- `400 Bad Request` - Validation errors
- `401 Unauthorized` - Invalid or missing API key
- `404 Not Found` - User not found
- `409 Conflict` - Email already exists (if updating email)
- `429 Too Many Requests` - Rate limit exceeded

### Validation Error Example

```json
{
  "error": "Validation Error",
  "message": "The request contains invalid data",
  "details": {
    "email": ["Email is already taken"],
    "role": ["Invalid role specified"]
  }
}
```

## Important Notes

- Updates are partial - only provided fields will be modified
- The `updatedAt` timestamp will be automatically updated
- Organization memberships cannot be modified through this endpoint
- Role changes may affect user permissions immediately

## Next Steps

- [Get User](/shopify-integration/users/get-user) - Verify the update
- [User Organizations](/shopify-integration/users/user-organizations) - Manage user memberships
- [Error Handling](/shopify-integration/error-handling) - Handle update errors