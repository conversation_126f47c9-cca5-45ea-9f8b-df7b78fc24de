---
title: Create User
description: Create a new user in the system
---

# Create User

Create a new user in the system with the provided information.

## Endpoint

```http
POST /api/integrations/users
```

## Request Body

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | Yes | Full name of the user |
| `firstName` | string | No | First name of the user |
| `lastName` | string | No | Last name of the user |
| `email` | string | Yes | Email address (must be unique) |
| `phone` | string | No | Phone number |
| `dob` | string | No | Date of birth (ISO 8601 format) |
| `role` | string | No | User role (default: "customer") |

## Request Example

```json
{
  "name": "<PERSON>",
  "firstName": "<PERSON>",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "dob": "1985-03-20",
  "role": "customer"
}
```

## Response Format

```json
{
  "id": "user-456",
  "name": "<PERSON>",
  "firstName": "<PERSON>",
  "lastName": "<PERSON>",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "dob": "1985-03-20",
  "role": "customer",
  "members": [],
  "createdAt": "2024-01-15T14:30:00Z",
  "updatedAt": "2024-01-15T14:30:00Z"
}
```

## Examples

### cURL Example

```bash
curl -X POST "https://api.example.com/api/integrations/users" \
  -H "X-Invois-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "role": "customer"
  }'
```

### JavaScript Example

```javascript
const userData = {
  name: "Jane Smith",
  firstName: "Jane",
  lastName: "Smith",
  email: "<EMAIL>",
  role: "customer"
};

const response = await fetch('https://api.example.com/api/integrations/users', {
  method: 'POST',
  headers: {
    'X-Invois-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(userData)
});

const newUser = await response.json();
```

## Validation Rules

- **Email**: Must be a valid email format and unique in the system
- **Name**: Required, minimum 2 characters
- **Role**: Must be one of: `super-admin`, `admin`, `customer`
- **Phone**: Optional, must be valid phone number format if provided
- **Date of Birth**: Optional, must be valid ISO 8601 date if provided

## Error Responses

- `400 Bad Request` - Validation errors or missing required fields
- `401 Unauthorized` - Invalid or missing API key
- `409 Conflict` - Email already exists
- `429 Too Many Requests` - Rate limit exceeded

### Validation Error Example

```json
{
  "error": "Validation Error",
  "message": "The request contains invalid data",
  "details": {
    "email": ["Email is already taken"],
    "name": ["Name is required"]
  }
}
```

## Next Steps

- [Get User](/shopify-integration/users/get-user) - Retrieve user details
- [Update User](/shopify-integration/users/update-user) - Modify user information
- [Error Handling](/shopify-integration/error-handling) - Handle validation errors