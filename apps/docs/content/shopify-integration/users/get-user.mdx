---
title: Get User
description: Retrieve detailed information about a specific user
---

# Get User

Retrieve detailed information about a specific user, including their organization memberships and relationships.

## Endpoint

```http
GET /api/integrations/users/{id}
```

## Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | The unique identifier of the user |

## Response Format

```json
{
  "id": "user-123",
  "name": "<PERSON>",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "dob": "1990-05-15",
  "role": "customer",
  "members": [
    {
      "id": "member-456",
      "organizationId": "org-789",
      "role": "admin",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

## Examples

### Basic Request

```bash
curl -X GET "https://api.example.com/api/integrations/users/user-123" \
  -H "X-Invois-Key: your-api-key-here"
```

### JavaScript Example

```javascript
const response = await fetch('https://api.example.com/api/integrations/users/user-123', {
  method: 'GET',
  headers: {
    'X-Invois-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  }
});

const user = await response.json();
console.log(user);
```

## Error Responses

- `401 Unauthorized` - Invalid or missing API key
- `404 Not Found` - User not found
- `429 Too Many Requests` - Rate limit exceeded

## User Roles

Users can have one of the following roles:
- `super-admin` - Full system access
- `admin` - Administrative access
- `customer` - Standard user access

## Organization Memberships

The `members` array contains information about the user's organization memberships, including:
- Organization ID
- Role within the organization
- Membership creation date

## Next Steps

- [Update User](/shopify-integration/users/update-user) - Modify user information
- [User Organizations](/shopify-integration/users/user-organizations) - Get user's organizations
- [Data Models](/shopify-integration/data-models) - Understand data relationships