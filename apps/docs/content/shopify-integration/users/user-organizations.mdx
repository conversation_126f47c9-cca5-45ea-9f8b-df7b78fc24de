---
title: User Organizations
description: Retrieve all organizations associated with a specific user
---

# User Organizations

Retrieve all organizations that a specific user is a member of, including their role and membership details.

## Endpoint

```http
GET /api/integrations/users/{id}/organizations
```

## Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | The unique identifier of the user |

## Response Format

```json
{
  "data": [
    {
      "id": "org-123",
      "name": "Acme Corporation",
      "slug": "acme-corp",
      "logo": "https://example.com/logos/acme.png",
      "memberCount": 25,
      "premiumTier": {
        "id": "tier-premium",
        "name": "Premium",
        "features": ["advanced-reporting", "priority-support"]
      },
      "membership": {
        "id": "member-456",
        "role": "admin",
        "createdAt": "2024-01-15T10:30:00Z"
      },
      "createdAt": "2024-01-10T08:00:00Z"
    },
    {
      "id": "org-789",
      "name": "Tech Startup Inc",
      "slug": "tech-startup",
      "logo": null,
      "memberCount": 8,
      "premiumTier": null,
      "membership": {
        "id": "member-789",
        "role": "customer",
        "createdAt": "2024-01-20T14:15:00Z"
      },
      "createdAt": "2024-01-18T12:30:00Z"
    }
  ],
  "meta": {
    "total": 2,
    "count": 2
  }
}
```

## Examples

### Basic Request

```bash
curl -X GET "https://api.example.com/api/integrations/users/user-123/organizations" \
  -H "X-Invois-Key: your-api-key-here"
```

### JavaScript Example

```javascript
const response = await fetch('https://api.example.com/api/integrations/users/user-123/organizations', {
  method: 'GET',
  headers: {
    'X-Invois-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  }
});

const userOrganizations = await response.json();
console.log(`User belongs to ${userOrganizations.meta.total} organizations`);
```

## Response Fields

### Organization Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique organization identifier |
| `name` | string | Organization name |
| `slug` | string | URL-friendly organization identifier |
| `logo` | string | Organization logo URL (nullable) |
| `memberCount` | integer | Total number of organization members |
| `premiumTier` | object | Premium tier information (nullable) |
| `createdAt` | string | Organization creation timestamp |

### Membership Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique membership identifier |
| `role` | string | User's role in the organization |
| `createdAt` | string | Membership creation timestamp |

### Premium Tier Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Premium tier identifier |
| `name` | string | Tier name |
| `features` | array | List of available features |

## Membership Roles

Users can have different roles within organizations:
- `admin` - Administrative access to the organization
- `customer` - Standard member access
- `owner` - Full ownership and control (if applicable)

## Error Responses

- `401 Unauthorized` - Invalid or missing API key
- `404 Not Found` - User not found
- `429 Too Many Requests` - Rate limit exceeded

## Use Cases

This endpoint is useful for:
- Displaying user's organization memberships
- Checking user permissions across organizations
- Understanding user's access levels
- Building organization-based navigation

## Next Steps

- [List Organizations](/shopify-integration/organizations/list-organizations) - Browse all organizations
- [Get Organization](/shopify-integration/organizations/get-organization) - Get organization details
- [Data Models](/shopify-integration/data-models) - Understand relationships