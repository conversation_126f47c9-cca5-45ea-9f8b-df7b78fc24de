---
title: List Users
description: Retrieve a paginated list of users with optional search functionality
---

# List Users

Retrieve a paginated list of users from the system with optional search and filtering capabilities.

## Endpoint

```http
GET /api/integrations/users
```

## Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `query` | string | No | Search term to filter users by name or email |
| `page` | integer | No | Page number for pagination (default: 1) |
| `per_page` | integer | No | Number of users per page (default: 10, max: 100) |

## Response Format

The endpoint returns a paginated response with user data and metadata.

```json
{
  "data": [
    {
      "id": "user-123",
      "name": "<PERSON>",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "role": "customer",
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "meta": {
    "total": 150,
    "per_page": 10,
    "current_page": 1,
    "last_page": 15,
    "from": 1,
    "to": 10
  }
}
```

## Examples

### Basic Request

```bash
curl -X GET "https://api.example.com/api/integrations/users" \
  -H "X-Invois-Key: your-api-key-here"
```

### Search Users

```bash
curl -X GET "https://api.example.com/api/integrations/users?query=john" \
  -H "X-Invois-Key: your-api-key-here"
```

### Pagination

```bash
curl -X GET "https://api.example.com/api/integrations/users?page=2&per_page=20" \
  -H "X-Invois-Key: your-api-key-here"
```

## Error Responses

- `401 Unauthorized` - Invalid or missing API key
- `400 Bad Request` - Invalid query parameters
- `429 Too Many Requests` - Rate limit exceeded

## Next Steps

- [Get User](/shopify-integration/users/get-user) - Retrieve individual user details
- [Create User](/shopify-integration/users/create-user) - Create new users
- [Error Handling](/shopify-integration/error-handling) - Handle API errors