---
title: Authentication
description: Learn how to authenticate your API requests using the X-Invois-Key header for secure access to the invoice management system
---

# Authentication

The invoice management system uses API key authentication to secure access to all integration endpoints. Every API request must include a valid API key to access user and organization data. This guide provides comprehensive information on how to properly authenticate your requests and handle authentication scenarios.

## API Key Authentication

All API requests to the integration endpoints must include a valid API key in the `X-Invois-Key` header. The system uses a simple but secure API key validation mechanism that checks the provided key against the configured server key.

### Header Format

```http
X-Invois-Key: your-api-key-here
```

The API key must be included in every request to any `/api/integrations/*` endpoint. Without a valid API key, all requests will be rejected with a `401 Unauthorized` response.

### API Key Requirements

- **Format**: The API key is a string value that must match exactly with the server-configured key
- **Case Sensitivity**: API keys are case-sensitive and must be provided exactly as issued
- **Scope**: A single API key provides access to all integration endpoints
- **Persistence**: API keys do not expire automatically but can be rotated by administrators

### Getting Your API Key

API keys are managed by system administrators and are configured at the server level. To obtain your API key:

1. Contact your system administrator or integration team
2. Provide your use case and integration requirements
3. Receive your unique API key securely (never via email or unsecured channels)
4. Store the API key securely in your application configuration

## Security Best Practices

### API Key Storage
- **Environment Variables**: Store API keys in environment variables, never in source code
- **Secure Configuration**: Use secure configuration management systems for production deployments
- **Access Control**: Limit access to API keys to only necessary team members
- **Documentation**: Never include real API keys in documentation or examples

### Network Security
- **HTTPS Only**: Always use HTTPS for API requests to prevent key interception
- **Network Restrictions**: Consider implementing IP whitelisting if supported
- **Request Logging**: Monitor API key usage and implement alerting for suspicious activity

### Key Management
- **Regular Rotation**: Establish a schedule for regular API key rotation
- **Immediate Revocation**: Have procedures in place to immediately revoke compromised keys
- **Multiple Environments**: Use different API keys for development, staging, and production
- **Backup Access**: Ensure multiple team members can access key management systems

## Authentication Implementation

### Request Headers
Every authenticated request must include the following headers:

```http
X-Invois-Key: your-api-key-here
Content-Type: application/json
```

### Authentication Flow
1. Client includes `X-Invois-Key` header in request
2. Server extracts the API key from the request headers
3. Server validates the API key against the configured value
4. If valid, the request proceeds to the endpoint handler
5. If invalid or missing, server returns `401 Unauthorized`

## Authentication Errors

### Missing API Key
When no `X-Invois-Key` header is provided:

**Response Status**: `401 Unauthorized`
```json
{
  "error": "Unauthorized: Valid API key required"
}
```

### Invalid API Key
When an incorrect API key is provided:

**Response Status**: `401 Unauthorized`
```json
{
  "error": "Unauthorized: Valid API key required"
}
```

### Malformed Header
When the header is present but malformed:

**Response Status**: `401 Unauthorized`
```json
{
  "error": "Unauthorized: Valid API key required"
}
```

## Troubleshooting Authentication Issues

### Common Problems

1. **Case Sensitivity**: Ensure the API key matches exactly, including case
2. **Whitespace**: Check for leading/trailing whitespace in the API key
3. **Header Name**: Verify the header name is exactly `X-Invois-Key`
4. **Network Issues**: Ensure requests are made over HTTPS
5. **Key Expiration**: Confirm the API key hasn't been rotated or revoked

### Debugging Steps

1. **Verify Header**: Check that the `X-Invois-Key` header is being sent
2. **Check Key Value**: Confirm the API key value matches what was provided
3. **Test with cURL**: Use cURL to isolate client-side issues
4. **Review Logs**: Check server logs for authentication errors
5. **Contact Support**: Reach out to administrators if issues persist

### Testing Authentication

Use this simple test to verify your API key is working:

```bash
curl -X GET "https://your-api-domain.com/api/integrations/users?per_page=1" \
  -H "X-Invois-Key: your-api-key-here" \
  -H "Content-Type: application/json"
```

A successful response indicates your authentication is working correctly.

## Rate Limiting and Authentication

Authenticated requests are subject to rate limiting. See the [Rate Limiting](/shopify-integration/rate-limiting) documentation for details on:
- Request limits per API key
- Rate limit headers in responses
- Handling rate limit exceeded errors

## Next Steps

Now that you understand authentication, you can:
- [Start managing users](/shopify-integration/users) - Access user endpoints
- [Work with organizations](/shopify-integration/organizations) - Manage organization data
- [Handle errors properly](/shopify-integration/error-handling) - Implement robust error handling
- [Review code examples](/shopify-integration/code-examples) - See authentication in action