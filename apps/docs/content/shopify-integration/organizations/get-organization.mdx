---
title: Get Organization
description: Retrieve detailed information about a specific organization
---

# Get Organization

Retrieve detailed information about a specific organization, including member information and premium tier data.

## Endpoint

```http
GET /api/integrations/organizations/{id}
```

## Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | The unique identifier of the organization |

## Response Format

```json
{
  "id": "org-123",
  "name": "Acme Corporation",
  "slug": "acme-corp",
  "logo": "https://example.com/logos/acme.png",
  "memberCount": 25,
  "premiumTier": {
    "id": "tier-premium",
    "name": "Premium",
    "features": [
      "advanced-reporting",
      "priority-support",
      "custom-integrations",
      "unlimited-users"
    ],
    "limits": {
      "maxUsers": 1000,
      "maxStorage": "100GB",
      "apiCallsPerMonth": 100000
    }
  },
  "members": [
    {
      "id": "member-456",
      "userId": "user-789",
      "role": "admin",
      "user": {
        "id": "user-789",
        "name": "<PERSON> Doe",
        "email": "<EMAIL>"
      },
      "createdAt": "2024-01-15T10:30:00Z"
    },
    {
      "id": "member-789",
      "userId": "user-123",
      "role": "customer",
      "user": {
        "id": "user-123",
        "name": "Jane Smith",
        "email": "<EMAIL>"
      },
      "createdAt": "2024-01-20T14:15:00Z"
    }
  ],
  "createdAt": "2024-01-10T08:00:00Z"
}
```

## Examples

### Basic Request

```bash
curl -X GET "https://api.example.com/api/integrations/organizations/org-123" \
  -H "X-Invois-Key: your-api-key-here"
```

### JavaScript Example

```javascript
const response = await fetch('https://api.example.com/api/integrations/organizations/org-123', {
  method: 'GET',
  headers: {
    'X-Invois-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  }
});

const organization = await response.json();
console.log(`Organization: ${organization.name} has ${organization.memberCount} members`);
```

## Response Fields

### Organization Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique organization identifier |
| `name` | string | Organization name |
| `slug` | string | URL-friendly organization identifier |
| `logo` | string | Organization logo URL (nullable) |
| `memberCount` | integer | Total number of organization members |
| `premiumTier` | object | Premium tier information (nullable) |
| `members` | array | Array of organization members |
| `createdAt` | string | Organization creation timestamp |

### Member Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique membership identifier |
| `userId` | string | ID of the user |
| `role` | string | Member's role in the organization |
| `user` | object | Basic user information |
| `createdAt` | string | Membership creation timestamp |

### Premium Tier Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Premium tier identifier |
| `name` | string | Tier name |
| `features` | array | List of available features |
| `limits` | object | Usage limits and quotas |

## Member Roles

Organization members can have the following roles:
- `owner` - Full ownership and control of the organization
- `admin` - Administrative access and member management
- `customer` - Standard member access

## Premium Tier Features

Premium tiers may include features such as:
- `advanced-reporting` - Enhanced analytics and reporting
- `priority-support` - Dedicated customer support
- `custom-integrations` - Custom API integrations
- `unlimited-users` - No user count restrictions
- `advanced-security` - Enhanced security features

## Error Responses

- `401 Unauthorized` - Invalid or missing API key
- `404 Not Found` - Organization not found
- `429 Too Many Requests` - Rate limit exceeded

## Use Cases

This endpoint is useful for:
- Displaying organization details and member lists
- Checking premium tier capabilities
- Managing organization settings
- Understanding organization structure and permissions

## Next Steps

- [Update Organization](/shopify-integration/organizations/update-organization) - Modify organization information
- [List Organizations](/shopify-integration/organizations/list-organizations) - Browse all organizations
- [Data Models](/shopify-integration/data-models) - Understand data relationships