---
title: List Organizations
description: Retrieve a paginated list of organizations with search and filtering capabilities
---

# List Organizations

Retrieve a paginated list of organizations from the system with optional search and filtering capabilities.

## Endpoint

```http
GET /api/integrations/organizations
```

## Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `query` | string | No | Search term to filter organizations by name |
| `page` | integer | No | Page number for pagination (default: 1) |
| `per_page` | integer | No | Number of organizations per page (default: 10, max: 100) |

## Response Format

```json
{
  "data": [
    {
      "id": "org-123",
      "name": "Acme Corporation",
      "slug": "acme-corp",
      "logo": "https://example.com/logos/acme.png",
      "memberCount": 25,
      "premiumTier": {
        "id": "tier-premium",
        "name": "Premium",
        "features": ["advanced-reporting", "priority-support"]
      },
      "createdAt": "2024-01-10T08:00:00Z"
    },
    {
      "id": "org-456",
      "name": "Tech Startup Inc",
      "slug": "tech-startup",
      "logo": null,
      "memberCount": 8,
      "premiumTier": null,
      "createdAt": "2024-01-18T12:30:00Z"
    }
  ],
  "meta": {
    "total": 150,
    "per_page": 10,
    "current_page": 1,
    "last_page": 15,
    "from": 1,
    "to": 10
  }
}
```

## Examples

### Basic Request

```bash
curl -X GET "https://api.example.com/api/integrations/organizations" \
  -H "X-Invois-Key: your-api-key-here"
```

### Search Organizations

```bash
curl -X GET "https://api.example.com/api/integrations/organizations?query=acme" \
  -H "X-Invois-Key: your-api-key-here"
```

### Pagination

```bash
curl -X GET "https://api.example.com/api/integrations/organizations?page=2&per_page=20" \
  -H "X-Invois-Key: your-api-key-here"
```

### JavaScript Example

```javascript
const response = await fetch('https://api.example.com/api/integrations/organizations?query=tech&page=1', {
  method: 'GET',
  headers: {
    'X-Invois-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  }
});

const organizations = await response.json();
console.log(`Found ${organizations.meta.total} organizations`);
```

## Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique organization identifier |
| `name` | string | Organization name |
| `slug` | string | URL-friendly organization identifier |
| `logo` | string | Organization logo URL (nullable) |
| `memberCount` | integer | Total number of organization members |
| `premiumTier` | object | Premium tier information (nullable) |
| `createdAt` | string | Organization creation timestamp |

## Premium Tier Information

Organizations may have premium tier subscriptions that include:
- **Tier ID**: Unique identifier for the tier
- **Tier Name**: Display name (e.g., "Premium", "Enterprise")
- **Features**: Array of available features for the tier

## Search Functionality

The search functionality allows you to:
- Search by organization name (case-insensitive)
- Use partial matches
- Combine search with pagination

## Error Responses

- `401 Unauthorized` - Invalid or missing API key
- `400 Bad Request` - Invalid query parameters
- `429 Too Many Requests` - Rate limit exceeded

## Use Cases

This endpoint is useful for:
- Building organization selection interfaces
- Implementing organization search functionality
- Displaying organization directories
- Managing organization relationships

## Next Steps

- [Get Organization](/shopify-integration/organizations/get-organization) - Get detailed organization information
- [Update Organization](/shopify-integration/organizations/update-organization) - Modify organization data
- [Data Models](/shopify-integration/data-models) - Understand organization structure