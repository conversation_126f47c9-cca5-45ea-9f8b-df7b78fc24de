---
title: Update Organization
description: Update an existing organization's information
---

# Update Organization

Update an existing organization's information with partial or complete data.

## Endpoint

```http
PUT /api/integrations/organizations/{id}
```

## Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | The unique identifier of the organization to update |

## Request Body

All fields are optional for updates. Only include the fields you want to modify.

| Field | Type | Description |
|-------|------|-------------|
| `name` | string | Organization name |
| `slug` | string | URL-friendly organization identifier |
| `logo` | string | Organization logo URL |

## Request Example

```json
{
  "name": "Acme Corporation Ltd",
  "logo": "https://example.com/logos/acme-new.png"
}
```

## Response Format

```json
{
  "id": "org-123",
  "name": "Acme Corporation Ltd",
  "slug": "acme-corp",
  "logo": "https://example.com/logos/acme-new.png",
  "memberCount": 25,
  "premiumTier": {
    "id": "tier-premium",
    "name": "Premium",
    "features": ["advanced-reporting", "priority-support"]
  },
  "members": [
    {
      "id": "member-456",
      "userId": "user-789",
      "role": "admin",
      "user": {
        "id": "user-789",
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "createdAt": "2024-01-10T08:00:00Z"
}
```

## Examples

### Update Organization Name

```bash
curl -X PUT "https://api.example.com/api/integrations/organizations/org-123" \
  -H "X-Invois-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Acme Corporation Ltd"
  }'
```

### Update Logo

```bash
curl -X PUT "https://api.example.com/api/integrations/organizations/org-123" \
  -H "X-Invois-Key: your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "logo": "https://example.com/logos/new-logo.png"
  }'
```

### JavaScript Example

```javascript
const updateData = {
  name: "Acme Corporation Ltd",
  logo: "https://example.com/logos/acme-new.png"
};

const response = await fetch('https://api.example.com/api/integrations/organizations/org-123', {
  method: 'PUT',
  headers: {
    'X-Invois-Key': 'your-api-key-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(updateData)
});

const updatedOrganization = await response.json();
```

## Validation Rules

- **Name**: Must be at least 2 characters long
- **Slug**: Must be unique, URL-friendly (lowercase, hyphens allowed)
- **Logo**: Must be a valid URL if provided

## Error Responses

- `400 Bad Request` - Validation errors
- `401 Unauthorized` - Invalid or missing API key
- `404 Not Found` - Organization not found
- `409 Conflict` - Slug already exists (if updating slug)
- `429 Too Many Requests` - Rate limit exceeded

### Validation Error Example

```json
{
  "error": "Validation Error",
  "message": "The request contains invalid data",
  "details": {
    "name": ["Name must be at least 2 characters"],
    "slug": ["Slug is already taken"]
  }
}
```

## Important Notes

- Updates are partial - only provided fields will be modified
- The organization's member list and premium tier cannot be modified through this endpoint
- Slug changes may affect existing integrations and URLs
- Logo URLs should be publicly accessible and preferably use HTTPS

## Permissions

Only users with appropriate permissions can update organizations:
- Organization owners can update all fields
- Organization admins may have limited update permissions
- Regular members cannot update organization information

## Next Steps

- [Get Organization](/shopify-integration/organizations/get-organization) - Verify the update
- [List Organizations](/shopify-integration/organizations/list-organizations) - Browse organizations
- [Error Handling](/shopify-integration/error-handling) - Handle update errors