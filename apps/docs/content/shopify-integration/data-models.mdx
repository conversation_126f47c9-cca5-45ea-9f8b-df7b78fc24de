---
title: Data Models
description: Understand the structure and relationships of users, organizations, and memberships
---

# Data Models

This section provides a comprehensive overview of the data models used in the invoice management system API, including their structure, relationships, and constraints.

## Core Models

### User Model

The User model represents individual users in the system.

```typescript
interface User {
  id: string;                    // Unique identifier
  name: string;                  // Full name
  firstName?: string;            // First name (optional)
  lastName?: string;             // Last name (optional)
  email: string;                 // Email address (unique)
  phone?: string;                // Phone number (optional)
  dob?: string;                  // Date of birth (ISO 8601)
  role: 'super-admin' | 'admin' | 'customer'; // User role
  members: Member[];             // Organization memberships
  createdAt: string;             // Creation timestamp
  updatedAt: string;             // Last update timestamp
}
```

#### User Roles

- **super-admin**: Full system access across all organizations
- **admin**: Administrative access with elevated permissions
- **customer**: Standard user access with basic permissions

### Organization Model

The Organization model represents companies or groups in the system.

```typescript
interface Organization {
  id: string;                    // Unique identifier
  name: string;                  // Organization name
  slug?: string;                 // URL-friendly identifier
  logo?: string;                 // Logo URL (optional)
  memberCount: number;           // Total number of members
  premiumTier?: PremiumTier;     // Premium subscription (optional)
  members: Member[];             // Organization members
  createdAt: string;             // Creation timestamp
}
```

### Member Model

The Member model represents the relationship between users and organizations.

```typescript
interface Member {
  id: string;                    // Unique membership identifier
  userId: string;                // Reference to User
  organizationId: string;        // Reference to Organization
  role: 'owner' | 'admin' | 'customer'; // Role within organization
  user?: User;                   // User details (when populated)
  organization?: Organization;   // Organization details (when populated)
  createdAt: string;             // Membership creation timestamp
}
```

#### Member Roles

- **owner**: Full control over the organization
- **admin**: Administrative privileges within the organization
- **customer**: Standard member access

### Premium Tier Model

The PremiumTier model represents subscription tiers and their capabilities.

```typescript
interface PremiumTier {
  id: string;                    // Unique tier identifier
  name: string;                  // Tier name (e.g., "Premium", "Enterprise")
  features: string[];            // Available features
  limits?: {                     // Usage limits
    maxUsers?: number;
    maxStorage?: string;
    apiCallsPerMonth?: number;
  };
}
```

## Relationships

### Entity Relationship Diagram

```mermaid
erDiagram
    User ||--o{ Member : "has many"
    Organization ||--o{ Member : "has many"
    Organization ||--o| PremiumTier : "belongs to"
    
    User {
        string id PK
        string name
        string firstName
        string lastName
        string email UK
        string phone
        string dob
        string role
        datetime createdAt
        datetime updatedAt
    }
    
    Organization {
        string id PK
        string name
        string slug UK
        string logo
        int memberCount
        string premiumTierId FK
        datetime createdAt
    }
    
    Member {
        string id PK
        string userId FK
        string organizationId FK
        string role
        datetime createdAt
    }
    
    PremiumTier {
        string id PK
        string name
        json features
        json limits
    }
```

## Data Constraints

### User Constraints

- **Email**: Must be unique across the system
- **Name**: Required, minimum 2 characters
- **Role**: Must be one of the defined role values
- **Phone**: Optional, must be valid format if provided

### Organization Constraints

- **Name**: Required, minimum 2 characters
- **Slug**: Must be unique, URL-friendly format
- **Member Count**: Automatically calculated, read-only

### Member Constraints

- **User-Organization**: Unique combination (one membership per user per organization)
- **Role**: Must be one of the defined member role values

## API Response Formats

### Paginated Response

Many endpoints return paginated data:

```typescript
interface PaginatedResponse<T> {
  data: T[];                     // Array of items
  meta: {
    total: number;               // Total number of items
    per_page: number;            // Items per page
    current_page: number;        // Current page number
    last_page: number;           // Last page number
    from: number;                // First item number on page
    to: number;                  // Last item number on page
  };
}
```

### Error Response

Error responses follow a consistent format:

```typescript
interface ErrorResponse {
  error: string;                 // Error type
  message: string;               // Human-readable message
  details?: {                    // Field-specific errors
    [field: string]: string[];
  };
}
```

## Common Patterns

### User-Organization Relationships

Users can belong to multiple organizations with different roles:

```json
{
  "user": {
    "id": "user-123",
    "name": "John Doe",
    "members": [
      {
        "organizationId": "org-456",
        "role": "admin"
      },
      {
        "organizationId": "org-789",
        "role": "customer"
      }
    ]
  }
}
```

### Organization Member Lists

Organizations contain member information with user details:

```json
{
  "organization": {
    "id": "org-456",
    "name": "Acme Corp",
    "members": [
      {
        "id": "member-123",
        "role": "admin",
        "user": {
          "id": "user-789",
          "name": "Jane Smith",
          "email": "<EMAIL>"
        }
      }
    ]
  }
}
```

## Data Validation

### Input Validation

All API endpoints validate input data according to model constraints:

- Required fields must be present
- Data types must match specifications
- String lengths must meet minimum/maximum requirements
- Enum values must be from allowed sets

### Response Validation

API responses are guaranteed to match the documented schemas:

- All required fields will be present
- Data types will match specifications
- Relationships will be properly populated when requested

## Next Steps

- [Authentication](/shopify-integration/authentication) - Learn about API authentication
- [Error Handling](/shopify-integration/error-handling) - Handle validation errors
- [User Management](/shopify-integration/users) - Work with user data
- [Organization Management](/shopify-integration/organizations) - Manage organizations