{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 3004 --turbo", "start": "next start", "test": "vitest", "generate-openapi": "tsx scripts/generate-openapi.ts", "postinstall": "fumadocs-mdx"}, "dependencies": {"fumadocs-core": "15.5.4", "fumadocs-mdx": "11.6.9", "fumadocs-ui": "15.5.4", "next": "15.3.0", "openapi-types": "^12.1.3", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@repo/observability": "workspace:*", "@repo/testing": "workspace:*", "@tailwindcss/postcss": "^4.1.10", "@types/mdx": "^2.0.13", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "postcss": "^8.5.6", "tailwindcss": "^4.1.4", "tsx": "^4.19.2", "typescript": "^5.8.3", "vitest": "^3.1.4"}}