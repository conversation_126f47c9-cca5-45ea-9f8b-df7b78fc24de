import type { OpenAPIV3 } from 'openapi-types';

/**
 * OpenAPI specification generator for Shopify integration endpoints
 */

// Base types for API documentation
export interface APIEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  operationId: string;
  summary: string;
  description: string;
  tags: string[];
  parameters?: OpenAPIV3.ParameterObject[];
  requestBody?: OpenAPIV3.RequestBodyObject;
  responses: { [statusCode: string]: OpenAPIV3.ResponseObject };
  security?: OpenAPIV3.SecurityRequirementObject[];
}

export interface ModelSchema {
  name: string;
  schema: OpenAPIV3.SchemaObject;
}

/**
 * Main OpenAPI generator class
 */
export class OpenAPIGenerator {
  private baseUrl: string;
  private version: string;

  constructor(baseUrl = 'https://api.example.com', version = '1.0.0') {
    this.baseUrl = baseUrl;
    this.version = version;
  }

  /**
   * Generate complete OpenAPI specification
   */
  generateSpec(): OpenAPIV3.Document {
    const spec: OpenAPIV3.Document = {
      openapi: '3.0.3',
      info: {
        title: 'Shopify Integration API',
        description:
          'API for managing users and organizations in the invoice management system',
        version: this.version,
        contact: {
          name: 'API Support',
          email: '<EMAIL>',
        },
      },
      servers: [
        {
          url: this.baseUrl,
          description: 'Production server',
        },
      ],
      security: [
        {
          ApiKeyAuth: [],
        },
      ],
      components: {
        securitySchemes: {
          ApiKeyAuth: {
            type: 'apiKey',
            in: 'header',
            name: 'X-Invois-Key',
            description: 'API key for authentication',
          },
        },
        schemas: this.generateSchemas(),
        responses: this.generateCommonResponses(),
      },
      paths: this.generatePaths(),
      tags: [
        {
          name: 'Users',
          description: 'User management operations',
        },
        {
          name: 'Organizations',
          description: 'Organization management operations',
        },
      ],
    };

    return spec;
  }

  /**
   * Generate JSON schema definitions for data models
   */
  private generateSchemas(): { [key: string]: OpenAPIV3.SchemaObject } {
    return {
      User: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'Unique identifier for the user',
          },
          name: {
            type: 'string',
            description: 'Full name of the user',
          },
          firstName: {
            type: 'string',
            nullable: true,
            description: 'First name of the user',
          },
          lastName: {
            type: 'string',
            nullable: true,
            description: 'Last name of the user',
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'Email address of the user',
          },
          phone: {
            type: 'string',
            nullable: true,
            description: 'Phone number of the user',
          },
          dob: {
            type: 'string',
            format: 'date-time',
            nullable: true,
            description: 'Date of birth',
          },
          role: {
            type: 'string',
            enum: ['super-admin', 'admin', 'customer'],
            description: 'User role in the system',
          },
          members: {
            type: 'array',
            items: { $ref: '#/components/schemas/Member' },
            description: 'Organization memberships',
          },
        },
        required: ['id', 'name', 'email', 'role'],
      },
      Organization: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'Unique identifier for the organization',
          },
          name: {
            type: 'string',
            description: 'Name of the organization',
          },
          slug: {
            type: 'string',
            nullable: true,
            description: 'URL-friendly identifier for the organization',
          },
          logo: {
            type: 'string',
            nullable: true,
            description: 'URL to the organization logo',
          },
          premiumTier: {
            $ref: '#/components/schemas/PremiumTier',
            nullable: true,
            description: 'Premium tier information',
          },
          members: {
            type: 'array',
            items: { $ref: '#/components/schemas/Member' },
            description: 'Organization members',
          },
          memberCount: {
            type: 'integer',
            description: 'Total number of members in the organization',
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: 'Creation timestamp',
          },
        },
        required: ['id', 'name'],
      },
      Member: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'Unique identifier for the membership',
          },
          role: {
            type: 'string',
            description: 'Role of the user in the organization',
          },
          organization: {
            $ref: '#/components/schemas/OrganizationSummary',
            description: 'Organization information',
          },
          user: {
            $ref: '#/components/schemas/UserSummary',
            description: 'User information',
          },
        },
        required: ['id', 'role'],
      },
      PremiumTier: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'Unique identifier for the premium tier',
          },
          name: {
            type: 'string',
            description: 'Name of the premium tier',
          },
          description: {
            type: 'string',
            nullable: true,
            description: 'Description of the premium tier',
          },
          maxInvoicesPerMonth: {
            type: 'integer',
            description: 'Maximum number of invoices allowed per month',
          },
          price: {
            type: 'number',
            format: 'decimal',
            description: 'Price of the premium tier',
          },
        },
        required: ['id', 'name', 'maxInvoicesPerMonth', 'price'],
      },
      UserSummary: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'Unique identifier for the user',
          },
          name: {
            type: 'string',
            description: 'Full name of the user',
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'Email address of the user',
          },
        },
        required: ['id', 'name', 'email'],
      },
      OrganizationSummary: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            format: 'uuid',
            description: 'Unique identifier for the organization',
          },
          name: {
            type: 'string',
            description: 'Name of the organization',
          },
          slug: {
            type: 'string',
            nullable: true,
            description: 'URL-friendly identifier for the organization',
          },
        },
        required: ['id', 'name'],
      },
      PaginatedResponse: {
        type: 'object',
        properties: {
          data: {
            type: 'array',
            items: {},
            description: 'Array of data items',
          },
          meta: {
            $ref: '#/components/schemas/PaginationMeta',
            description: 'Pagination metadata',
          },
        },
        required: ['data', 'meta'],
      },
      PaginationMeta: {
        type: 'object',
        properties: {
          total: {
            type: 'integer',
            description: 'Total number of items',
          },
          per_page: {
            type: 'integer',
            description: 'Number of items per page',
          },
          current_page: {
            type: 'integer',
            description: 'Current page number',
          },
          last_page: {
            type: 'integer',
            description: 'Last page number',
          },
          from: {
            type: 'integer',
            description: 'Starting item number',
          },
          to: {
            type: 'integer',
            description: 'Ending item number',
          },
        },
        required: [
          'total',
          'per_page',
          'current_page',
          'last_page',
          'from',
          'to',
        ],
      },
      ErrorResponse: {
        type: 'object',
        properties: {
          error: {
            type: 'string',
            description: 'Error message',
          },
          message: {
            type: 'string',
            description: 'Detailed error description',
          },
          details: {
            type: 'object',
            additionalProperties: {
              type: 'array',
              items: { type: 'string' },
            },
            description: 'Field-specific validation errors',
          },
        },
        required: ['error'],
      },
      CreateUserRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'Full name of the user',
          },
          firstName: {
            type: 'string',
            description: 'First name of the user',
          },
          lastName: {
            type: 'string',
            description: 'Last name of the user',
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'Email address of the user',
          },
          phone: {
            type: 'string',
            description: 'Phone number of the user',
          },
          dob: {
            type: 'string',
            format: 'date-time',
            description: 'Date of birth',
          },
          role: {
            type: 'string',
            enum: ['super-admin', 'admin', 'customer'],
            default: 'customer',
            description: 'User role in the system',
          },
        },
        required: ['name', 'email'],
      },
      UpdateUserRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'Full name of the user',
          },
          firstName: {
            type: 'string',
            description: 'First name of the user',
          },
          lastName: {
            type: 'string',
            description: 'Last name of the user',
          },
          email: {
            type: 'string',
            format: 'email',
            description: 'Email address of the user',
          },
          phone: {
            type: 'string',
            description: 'Phone number of the user',
          },
          dob: {
            type: 'string',
            format: 'date-time',
            description: 'Date of birth',
          },
          role: {
            type: 'string',
            enum: ['super-admin', 'admin', 'customer'],
            description: 'User role in the system',
          },
        },
      },
      UpdateOrganizationRequest: {
        type: 'object',
        properties: {
          name: {
            type: 'string',
            description: 'Name of the organization',
          },
          slug: {
            type: 'string',
            description: 'URL-friendly identifier for the organization',
          },
          logo: {
            type: 'string',
            description: 'URL to the organization logo',
          },
        },
      },
    };
  }

  /**
   * Generate common response definitions
   */
  private generateCommonResponses(): {
    [key: string]: OpenAPIV3.ResponseObject;
  } {
    return {
      UnauthorizedError: {
        description: 'Authentication required',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/ErrorResponse' },
            example: {
              error: 'Unauthorized: Valid API key required',
            },
          },
        },
      },
      NotFoundError: {
        description: 'Resource not found',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/ErrorResponse' },
            example: {
              error: 'User not found',
            },
          },
        },
      },
      ValidationError: {
        description: 'Validation error',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/ErrorResponse' },
            example: {
              error: 'Validation failed',
              message: 'The provided data is invalid',
              details: {
                email: ['Email is required'],
                name: ['Name must be at least 2 characters'],
              },
            },
          },
        },
      },
    };
  }

  /**
   * Generate all API paths
   */
  private generatePaths(): OpenAPIV3.PathsObject {
    return {
      '/api/integrations/users': {
        get: {
          operationId: 'listUsers',
          summary: 'List users',
          description:
            'Retrieve a paginated list of users with optional search functionality',
          tags: ['Users'],
          parameters: [
            {
              name: 'query',
              in: 'query',
              description:
                'Search query to filter users by name, email, or phone',
              required: false,
              schema: { type: 'string' },
              example: '<EMAIL>',
            },
            {
              name: 'page',
              in: 'query',
              description: 'Page number for pagination',
              required: false,
              schema: { type: 'integer', minimum: 1, default: 1 },
              example: 1,
            },
            {
              name: 'per_page',
              in: 'query',
              description: 'Number of items per page',
              required: false,
              schema: {
                type: 'integer',
                minimum: 1,
                maximum: 100,
                default: 10,
              },
              example: 10,
            },
          ],
          responses: {
            '200': {
              description: 'Successful response',
              content: {
                'application/json': {
                  schema: {
                    allOf: [
                      { $ref: '#/components/schemas/PaginatedResponse' },
                      {
                        properties: {
                          data: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/User' },
                          },
                        },
                      },
                    ],
                  },
                },
              },
            },
            '401': { $ref: '#/components/responses/UnauthorizedError' },
          },
          security: [{ ApiKeyAuth: [] }],
        },
        post: {
          operationId: 'createUser',
          summary: 'Create user',
          description: 'Create a new user in the system',
          tags: ['Users'],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/CreateUserRequest' },
              },
            },
          },
          responses: {
            '201': {
              description: 'User created successfully',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      data: { $ref: '#/components/schemas/UserSummary' },
                    },
                  },
                },
              },
            },
            '400': { $ref: '#/components/responses/ValidationError' },
            '401': { $ref: '#/components/responses/UnauthorizedError' },
          },
          security: [{ ApiKeyAuth: [] }],
        },
      },
      '/api/integrations/users/{id}': {
        get: {
          operationId: 'getUser',
          summary: 'Get user by ID',
          description: 'Retrieve detailed information about a specific user',
          tags: ['Users'],
          parameters: [
            {
              name: 'id',
              in: 'path',
              description: 'User ID',
              required: true,
              schema: { type: 'string', format: 'uuid' },
              example: '123e4567-e89b-12d3-a456-************',
            },
          ],
          responses: {
            '200': {
              description: 'Successful response',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      data: { $ref: '#/components/schemas/User' },
                    },
                  },
                },
              },
            },
            '401': { $ref: '#/components/responses/UnauthorizedError' },
            '404': { $ref: '#/components/responses/NotFoundError' },
          },
          security: [{ ApiKeyAuth: [] }],
        },
        put: {
          operationId: 'updateUser',
          summary: 'Update user',
          description: "Update an existing user's information",
          tags: ['Users'],
          parameters: [
            {
              name: 'id',
              in: 'path',
              description: 'User ID',
              required: true,
              schema: { type: 'string', format: 'uuid' },
              example: '123e4567-e89b-12d3-a456-************',
            },
          ],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/UpdateUserRequest' },
              },
            },
          },
          responses: {
            '200': {
              description: 'User updated successfully',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      data: { $ref: '#/components/schemas/User' },
                    },
                  },
                },
              },
            },
            '400': { $ref: '#/components/responses/ValidationError' },
            '401': { $ref: '#/components/responses/UnauthorizedError' },
            '404': { $ref: '#/components/responses/NotFoundError' },
          },
          security: [{ ApiKeyAuth: [] }],
        },
      },
      '/api/integrations/users/{id}/organizations': {
        get: {
          operationId: 'getUserOrganizations',
          summary: 'Get user organizations',
          description: 'Retrieve all organizations that a user is a member of',
          tags: ['Users'],
          parameters: [
            {
              name: 'id',
              in: 'path',
              description: 'User ID',
              required: true,
              schema: { type: 'string', format: 'uuid' },
              example: '123e4567-e89b-12d3-a456-************',
            },
          ],
          responses: {
            '200': {
              description: 'Successful response',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      data: {
                        type: 'array',
                        items: {
                          allOf: [
                            { $ref: '#/components/schemas/Organization' },
                            {
                              properties: {
                                role: {
                                  type: 'string',
                                  description:
                                    "User's role in this organization",
                                },
                              },
                            },
                          ],
                        },
                      },
                    },
                  },
                },
              },
            },
            '401': { $ref: '#/components/responses/UnauthorizedError' },
            '404': { $ref: '#/components/responses/NotFoundError' },
          },
          security: [{ ApiKeyAuth: [] }],
        },
      },
      '/api/integrations/organizations': {
        get: {
          operationId: 'listOrganizations',
          summary: 'List organizations',
          description:
            'Retrieve a paginated list of organizations with optional search functionality',
          tags: ['Organizations'],
          parameters: [
            {
              name: 'query',
              in: 'query',
              description: 'Search query to filter organizations by name',
              required: false,
              schema: { type: 'string' },
              example: 'Acme Corp',
            },
            {
              name: 'page',
              in: 'query',
              description: 'Page number for pagination',
              required: false,
              schema: { type: 'integer', minimum: 1, default: 1 },
              example: 1,
            },
            {
              name: 'per_page',
              in: 'query',
              description: 'Number of items per page',
              required: false,
              schema: {
                type: 'integer',
                minimum: 1,
                maximum: 100,
                default: 10,
              },
              example: 10,
            },
          ],
          responses: {
            '200': {
              description: 'Successful response',
              content: {
                'application/json': {
                  schema: {
                    allOf: [
                      { $ref: '#/components/schemas/PaginatedResponse' },
                      {
                        properties: {
                          data: {
                            type: 'array',
                            items: {
                              $ref: '#/components/schemas/Organization',
                            },
                          },
                        },
                      },
                    ],
                  },
                },
              },
            },
            '401': { $ref: '#/components/responses/UnauthorizedError' },
          },
          security: [{ ApiKeyAuth: [] }],
        },
      },
      '/api/integrations/organizations/{id}': {
        get: {
          operationId: 'getOrganization',
          summary: 'Get organization by ID',
          description:
            'Retrieve detailed information about a specific organization',
          tags: ['Organizations'],
          parameters: [
            {
              name: 'id',
              in: 'path',
              description: 'Organization ID',
              required: true,
              schema: { type: 'string', format: 'uuid' },
              example: '123e4567-e89b-12d3-a456-************',
            },
          ],
          responses: {
            '200': {
              description: 'Successful response',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      data: { $ref: '#/components/schemas/Organization' },
                    },
                  },
                },
              },
            },
            '401': { $ref: '#/components/responses/UnauthorizedError' },
            '404': { $ref: '#/components/responses/NotFoundError' },
          },
          security: [{ ApiKeyAuth: [] }],
        },
        put: {
          operationId: 'updateOrganization',
          summary: 'Update organization',
          description: "Update an existing organization's information",
          tags: ['Organizations'],
          parameters: [
            {
              name: 'id',
              in: 'path',
              description: 'Organization ID',
              required: true,
              schema: { type: 'string', format: 'uuid' },
              example: '123e4567-e89b-12d3-a456-************',
            },
          ],
          requestBody: {
            required: true,
            content: {
              'application/json': {
                schema: {
                  $ref: '#/components/schemas/UpdateOrganizationRequest',
                },
              },
            },
          },
          responses: {
            '200': {
              description: 'Organization updated successfully',
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      data: { $ref: '#/components/schemas/Organization' },
                    },
                  },
                },
              },
            },
            '400': { $ref: '#/components/responses/ValidationError' },
            '401': { $ref: '#/components/responses/UnauthorizedError' },
            '404': { $ref: '#/components/responses/NotFoundError' },
          },
          security: [{ ApiKeyAuth: [] }],
        },
      },
    };
  }

  /**
   * Generate OpenAPI spec as JSON string
   */
  generateSpecJSON(): string {
    return JSON.stringify(this.generateSpec(), null, 2);
  }

  /**
   * Generate endpoint documentation for a specific endpoint
   */
  generateEndpointDoc(endpoint: APIEndpoint): OpenAPIV3.PathItemObject {
    const pathItem: OpenAPIV3.PathItemObject = {};

    const operation: OpenAPIV3.OperationObject = {
      operationId: endpoint.operationId,
      summary: endpoint.summary,
      description: endpoint.description,
      tags: endpoint.tags,
      parameters: endpoint.parameters,
      requestBody: endpoint.requestBody,
      responses: endpoint.responses,
      security: endpoint.security,
    };

    pathItem[endpoint.method.toLowerCase() as keyof OpenAPIV3.PathItemObject] =
      operation;

    return pathItem;
  }
}

/**
 * Create a singleton instance of the OpenAPI generator
 */
export const openApiGenerator = new OpenAPIGenerator();

/**
 * Generate and return the complete OpenAPI specification
 */
export function generateOpenAPISpec(): OpenAPIV3.Document {
  return openApiGenerator.generateSpec();
}

/**
 * Generate OpenAPI specification as JSON string
 */
export function generateOpenAPISpecJSON(): string {
  return openApiGenerator.generateSpecJSON();
}
