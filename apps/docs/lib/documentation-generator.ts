import type { OpenAPIV3 } from 'openapi-types';
import { type APIEndpoint, OpenAPIGenerator } from './openapi-generator';
import { EndpointParser, type ParsedEndpoint } from './endpoint-parser';

/**
 * Complete documentation generator that combines OpenAPI generation with endpoint parsing
 */
export class DocumentationGenerator {
  private openApiGenerator: OpenAPIGenerator;
  private endpointParser: EndpointParser;

  constructor(baseUrl = 'https://api.example.com', version = '1.0.0') {
    this.openApiGenerator = new OpenAPIGenerator(baseUrl, version);
    this.endpointParser = new EndpointParser();
  }

  /**
   * Generate complete OpenAPI specification by combining static definitions with parsed endpoints
   */
  async generateCompleteSpec(): Promise<OpenAPIV3.Document> {
    // Get the base specification
    const baseSpec = this.openApiGenerator.generateSpec();

    // Parse actual endpoints from the codebase
    const parsedEndpoints = await this.endpointParser.parseAllEndpoints();

    // Merge parsed endpoints with base specification
    const mergedSpec = this.mergeEndpointsWithSpec(baseSpec, parsedEndpoints);

    return mergedSpec;
  }

  /**
   * Merge parsed endpoints with the base OpenAPI specification
   */
  private mergeEndpointsWithSpec(
    baseSpec: OpenAPIV3.Document,
    parsedEndpoints: ParsedEndpoint[]
  ): OpenAPIV3.Document {
    const mergedSpec = { ...baseSpec };

    // Create a new paths object
    const mergedPaths: OpenAPIV3.PathsObject = { ...baseSpec.paths };

    // Process each parsed endpoint
    for (const endpoint of parsedEndpoints) {
      const pathKey = endpoint.path;

      // Initialize path object if it doesn't exist
      if (!mergedPaths[pathKey]) {
        mergedPaths[pathKey] = {};
      }

      // Add the operation to the path
      const method =
        endpoint.method.toLowerCase() as keyof OpenAPIV3.PathItemObject;
      const operation: OpenAPIV3.OperationObject = {
        operationId: endpoint.operationId,
        summary: endpoint.summary,
        description: endpoint.description,
        tags: endpoint.tags,
        parameters: endpoint.parameters,
        responses: endpoint.responses,
        security: endpoint.security,
      };

      // Add request body if present
      if (endpoint.requestBody) {
        operation.requestBody = endpoint.requestBody;
      }

      // Merge with existing operation or add new one
      if (mergedPaths[pathKey]![method]) {
        // If operation already exists, merge properties
        mergedPaths[pathKey]![method] = {
          ...mergedPaths[pathKey]![method],
          ...operation,
        };
      } else {
        // Add new operation
        mergedPaths[pathKey]![method] = operation;
      }
    }

    // Update the specification with merged paths
    mergedSpec.paths = mergedPaths;

    return mergedSpec;
  }

  /**
   * Generate documentation for a specific endpoint
   */
  async generateEndpointDocumentation(
    apiPath: string,
    method: string
  ): Promise<{
    endpoint: ParsedEndpoint | null;
    openApiPath: OpenAPIV3.PathItemObject | null;
  }> {
    const parsedEndpoint = await this.endpointParser.parseEndpoint(
      apiPath,
      method
    );

    if (!parsedEndpoint) {
      return { endpoint: null, openApiPath: null };
    }

    const openApiPath = this.openApiGenerator.generateEndpointDoc(
      parsedEndpoint as APIEndpoint
    );

    return {
      endpoint: parsedEndpoint,
      openApiPath,
    };
  }

  /**
   * Validate that all endpoints are properly documented
   */
  async validateDocumentation(): Promise<{
    valid: boolean;
    issues: string[];
    coverage: {
      total: number;
      documented: number;
      percentage: number;
    };
  }> {
    const issues: string[] = [];
    const parsedEndpoints = await this.endpointParser.parseAllEndpoints();
    const baseSpec = this.openApiGenerator.generateSpec();

    let documentedCount = 0;

    for (const endpoint of parsedEndpoints) {
      const pathKey = endpoint.path;
      const method = endpoint.method.toLowerCase();

      // Check if endpoint is documented in base spec
      if (
        baseSpec.paths?.[pathKey]?.[method as keyof OpenAPIV3.PathItemObject]
      ) {
        documentedCount++;
      } else {
        issues.push(
          `Endpoint ${endpoint.method} ${pathKey} is not documented in base specification`
        );
      }

      // Validate endpoint structure
      if (!endpoint.operationId) {
        issues.push(
          `Endpoint ${endpoint.method} ${pathKey} is missing operationId`
        );
      }

      if (!endpoint.summary) {
        issues.push(
          `Endpoint ${endpoint.method} ${pathKey} is missing summary`
        );
      }

      if (!endpoint.description) {
        issues.push(
          `Endpoint ${endpoint.method} ${pathKey} is missing description`
        );
      }

      if (endpoint.tags.length === 0) {
        issues.push(`Endpoint ${endpoint.method} ${pathKey} is missing tags`);
      }
    }

    const coverage = {
      total: parsedEndpoints.length,
      documented: documentedCount,
      percentage:
        parsedEndpoints.length > 0
          ? (documentedCount / parsedEndpoints.length) * 100
          : 0,
    };

    return {
      valid: issues.length === 0,
      issues,
      coverage,
    };
  }

  /**
   * Generate OpenAPI specification as JSON string
   */
  async generateSpecJSON(): Promise<string> {
    const spec = await this.generateCompleteSpec();
    return JSON.stringify(spec, null, 2);
  }

  /**
   * Generate OpenAPI specification as YAML string
   */
  async generateSpecYAML(): Promise<string> {
    const spec = await this.generateCompleteSpec();
    // Note: You would need to install a YAML library like 'js-yaml' for this
    // For now, returning JSON format
    return JSON.stringify(spec, null, 2);
  }

  /**
   * Get endpoint statistics
   */
  async getEndpointStats(): Promise<{
    totalEndpoints: number;
    endpointsByMethod: { [method: string]: number };
    endpointsByTag: { [tag: string]: number };
    authenticationRequired: number;
    publicEndpoints: number;
  }> {
    const parsedEndpoints = await this.endpointParser.parseAllEndpoints();

    const stats = {
      totalEndpoints: parsedEndpoints.length,
      endpointsByMethod: {} as { [method: string]: number },
      endpointsByTag: {} as { [tag: string]: number },
      authenticationRequired: 0,
      publicEndpoints: 0,
    };

    for (const endpoint of parsedEndpoints) {
      // Count by method
      stats.endpointsByMethod[endpoint.method] =
        (stats.endpointsByMethod[endpoint.method] || 0) + 1;

      // Count by tag
      for (const tag of endpoint.tags) {
        stats.endpointsByTag[tag] = (stats.endpointsByTag[tag] || 0) + 1;
      }

      // Count authentication
      if (endpoint.security.length > 0) {
        stats.authenticationRequired++;
      } else {
        stats.publicEndpoints++;
      }
    }

    return stats;
  }

  /**
   * Generate endpoint summary for documentation overview
   */
  async generateEndpointSummary(): Promise<
    {
      path: string;
      method: string;
      operationId: string;
      summary: string;
      tags: string[];
      authenticated: boolean;
    }[]
  > {
    const parsedEndpoints = await this.endpointParser.parseAllEndpoints();

    return parsedEndpoints.map((endpoint) => ({
      path: endpoint.path,
      method: endpoint.method,
      operationId: endpoint.operationId,
      summary: endpoint.summary,
      tags: endpoint.tags,
      authenticated: endpoint.security.length > 0,
    }));
  }
}

/**
 * Create a singleton instance of the documentation generator
 */
export const documentationGenerator = new DocumentationGenerator();

/**
 * Generate complete OpenAPI specification
 */

// biome-ignore lint/suspicious/useAwait: <explanation>
export async function generateCompleteOpenAPISpec(): Promise<OpenAPIV3.Document> {
  return documentationGenerator.generateCompleteSpec();
}

/**
 * Generate complete OpenAPI specification as JSON
 */

// biome-ignore lint/suspicious/useAwait: <explanation>
export async function generateCompleteOpenAPISpecJSON(): Promise<string> {
  return documentationGenerator.generateSpecJSON();
}

/**
 * Validate documentation coverage
 */

// biome-ignore lint/suspicious/useAwait: <explanation>
export async function validateDocumentationCoverage() {
  return documentationGenerator.validateDocumentation();
}

/**
 * Get endpoint statistics
 */

// biome-ignore lint/suspicious/useAwait: <explanation>
export async function getEndpointStatistics() {
  return documentationGenerator.getEndpointStats();
}
