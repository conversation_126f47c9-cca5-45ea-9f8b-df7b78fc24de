import { describe, it, expect, beforeEach } from 'vitest';
import { DocumentationGenerator } from '../documentation-generator';

describe('DocumentationGenerator', () => {
  let generator: DocumentationGenerator;

  beforeEach(() => {
    generator = new DocumentationGenerator('https://api.test.com', '1.0.0');
  });

  describe('generateCompleteSpec', () => {
    it('should generate a valid OpenAPI specification', async () => {
      const spec = await generator.generateCompleteSpec();

      expect(spec).toBeDefined();
      expect(spec.openapi).toBe('3.0.3');
      expect(spec.info.title).toBe('Shopify Integration API');
      expect(spec.info.version).toBe('1.0.0');
      expect(spec.servers).toHaveLength(1);
      expect(spec.servers?.[0].url).toBe('https://api.test.com');
    });

    it('should include security schemes', async () => {
      const spec = await generator.generateCompleteSpec();

      expect(spec.components?.securitySchemes).toBeDefined();
      expect(spec.components?.securitySchemes?.ApiKeyAuth).toMatchObject({
        type: 'apiKey',
        in: 'header',
        name: 'X-Invois-Key',
      });
    });

    it('should include schema definitions', async () => {
      const spec = await generator.generateCompleteSpec();

      expect(spec.components?.schemas).toBeDefined();
      expect(spec.components?.schemas?.User).toBeDefined();
      expect(spec.components?.schemas?.Organization).toBeDefined();
      expect(spec.components?.schemas?.Member).toBeDefined();
      expect(spec.components?.schemas?.PaginatedResponse).toBeDefined();
      expect(spec.components?.schemas?.ErrorResponse).toBeDefined();
    });

    it('should include API paths', async () => {
      const spec = await generator.generateCompleteSpec();

      expect(spec.paths).toBeDefined();
      expect(spec.paths['/api/integrations/users']).toBeDefined();
      expect(spec.paths['/api/integrations/users/{id}']).toBeDefined();
      expect(spec.paths['/api/integrations/organizations']).toBeDefined();
      expect(spec.paths['/api/integrations/organizations/{id}']).toBeDefined();
    });

    it('should include tags', async () => {
      const spec = await generator.generateCompleteSpec();

      expect(spec.tags).toBeDefined();
      expect(spec.tags).toHaveLength(2);
      expect(spec.tags?.find((tag) => tag.name === 'Users')).toBeDefined();
      expect(
        spec.tags?.find((tag) => tag.name === 'Organizations')
      ).toBeDefined();
    });
  });

  describe('validateDocumentation', () => {
    it('should validate documentation structure', async () => {
      const validation = await generator.validateDocumentation();

      expect(validation).toBeDefined();
      expect(validation.coverage).toBeDefined();
      expect(validation.coverage.total).toBeGreaterThanOrEqual(0);
      expect(validation.coverage.percentage).toBeGreaterThanOrEqual(0);
      expect(validation.coverage.percentage).toBeLessThanOrEqual(100);
      expect(Array.isArray(validation.issues)).toBe(true);
    });
  });

  describe('getEndpointStats', () => {
    it('should return endpoint statistics', async () => {
      const stats = await generator.getEndpointStats();

      expect(stats).toBeDefined();
      expect(stats.totalEndpoints).toBeGreaterThanOrEqual(0);
      expect(stats.endpointsByMethod).toBeDefined();
      expect(stats.endpointsByTag).toBeDefined();
      expect(stats.authenticationRequired).toBeGreaterThanOrEqual(0);
      expect(stats.publicEndpoints).toBeGreaterThanOrEqual(0);
    });
  });

  describe('generateEndpointSummary', () => {
    it('should generate endpoint summary', async () => {
      const summary = await generator.generateEndpointSummary();

      expect(Array.isArray(summary)).toBe(true);

      if (summary.length > 0) {
        const endpoint = summary[0];
        expect(endpoint.path).toBeDefined();
        expect(endpoint.method).toBeDefined();
        expect(endpoint.operationId).toBeDefined();
        expect(endpoint.summary).toBeDefined();
        expect(Array.isArray(endpoint.tags)).toBe(true);
        expect(typeof endpoint.authenticated).toBe('boolean');
      }
    });
  });

  describe('generateSpecJSON', () => {
    it('should generate valid JSON string', async () => {
      const jsonString = await generator.generateSpecJSON();

      expect(typeof jsonString).toBe('string');
      expect(() => JSON.parse(jsonString)).not.toThrow();

      const parsed = JSON.parse(jsonString);
      expect(parsed.openapi).toBe('3.0.3');
      expect(parsed.info.title).toBe('Shopify Integration API');
    });
  });
});
