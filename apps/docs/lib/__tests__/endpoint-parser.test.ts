import { describe, it, expect, beforeEach } from 'vitest';
import { EndpointParser } from '../endpoint-parser';

describe('EndpointParser', () => {
  let parser: EndpointParser;

  beforeEach(() => {
    parser = new EndpointParser();
  });

  describe('extractApiPath', () => {
    it('should convert file path to API path', () => {
      const filePath = 'apps/api/app/api/integrations/users/route.ts';
      const result = (parser as any).extractApiPath(filePath);
      expect(result).toBe('/api/integrations/users');
    });

    it('should handle dynamic routes', () => {
      const filePath = 'apps/api/app/api/integrations/users/[id]/route.ts';
      const result = (parser as any).extractApiPath(filePath);
      expect(result).toBe('/api/integrations/users/{id}');
    });

    it('should handle nested dynamic routes', () => {
      const filePath =
        'apps/api/app/api/integrations/users/[id]/organizations/route.ts';
      const result = (parser as any).extractApiPath(filePath);
      expect(result).toBe('/api/integrations/users/{id}/organizations');
    });
  });

  describe('extractHttpMethods', () => {
    it('should extract HTTP methods from route content', () => {
      const content = `
        export const GET = withLicenseKeyAuth(handler);
        export const POST = withLicenseKeyAuth(postHandler);
        export const OPTIONS = withLicenseKeyAuth(handler);
      `;
      const result = (parser as any).extractHttpMethods(content);
      expect(result).toEqual(['GET', 'POST']);
    });

    it('should handle routes without OPTIONS', () => {
      const content = `
        export const GET = withLicenseKeyAuth(handler);
        export const PUT = withLicenseKeyAuth(putHandler);
      `;
      const result = (parser as any).extractHttpMethods(content);
      expect(result).toEqual(['GET', 'PUT']);
    });
  });

  describe('hasAuthentication', () => {
    it('should detect authentication wrapper', () => {
      const content = 'export const GET = withLicenseKeyAuth(handler);';
      const result = (parser as any).hasAuthentication(content);
      expect(result).toBe(true);
    });

    it('should return false when no authentication', () => {
      const content = 'export const GET = handler;';
      const result = (parser as any).hasAuthentication(content);
      expect(result).toBe(false);
    });
  });

  describe('extractParameters', () => {
    it('should extract path parameters', () => {
      const content = '';
      const apiPath = '/api/integrations/users/{id}';
      const result = (parser as any).extractParameters(content, apiPath);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        name: 'id',
        in: 'path',
        required: true,
        schema: { type: 'string', format: 'uuid' },
      });
    });

    it('should extract query parameters', () => {
      const content = `
        const query = searchParams.get('query');
        const page = searchParams.get('page');
        const perPage = searchParams.get('per_page');
      `;
      const apiPath = '/api/integrations/users';
      const result = (parser as any).extractParameters(content, apiPath);

      expect(result).toHaveLength(3);
      expect(result.find((p: any) => p.name === 'query')).toBeDefined();
      expect(result.find((p: any) => p.name === 'page')).toBeDefined();
      expect(result.find((p: any) => p.name === 'per_page')).toBeDefined();
    });
  });

  describe('generateOperationId', () => {
    it('should generate correct operation ID for list endpoints', () => {
      const result = (parser as any).generateOperationId(
        'GET',
        '/api/integrations/users'
      );
      expect(result).toBe('listUsers');
    });

    it('should generate correct operation ID for get endpoints', () => {
      const result = (parser as any).generateOperationId(
        'GET',
        '/api/integrations/users/{id}'
      );
      expect(result).toBe('getUsers');
    });

    it('should generate correct operation ID for create endpoints', () => {
      const result = (parser as any).generateOperationId(
        'POST',
        '/api/integrations/users'
      );
      expect(result).toBe('createUsers');
    });

    it('should generate correct operation ID for update endpoints', () => {
      const result = (parser as any).generateOperationId(
        'PUT',
        '/api/integrations/users/{id}'
      );
      expect(result).toBe('updateUsers');
    });
  });

  describe('extractTags', () => {
    it('should extract Users tag for user endpoints', () => {
      const result = (parser as any).extractTags('/api/integrations/users');
      expect(result).toEqual(['Users']);
    });

    it('should extract Organizations tag for organization endpoints', () => {
      const result = (parser as any).extractTags(
        '/api/integrations/organizations'
      );
      expect(result).toEqual(['Organizations']);
    });

    it('should default to API tag for unknown endpoints', () => {
      const result = (parser as any).extractTags('/api/integrations/unknown');
      expect(result).toEqual(['API']);
    });
  });

  describe('getStatusDescription', () => {
    it('should return correct descriptions for common status codes', () => {
      expect((parser as any).getStatusDescription('200')).toBe(
        'Successful response'
      );
      expect((parser as any).getStatusDescription('201')).toBe(
        'Created successfully'
      );
      expect((parser as any).getStatusDescription('400')).toBe('Bad request');
      expect((parser as any).getStatusDescription('401')).toBe('Unauthorized');
      expect((parser as any).getStatusDescription('404')).toBe('Not found');
    });

    it('should return default description for unknown status codes', () => {
      expect((parser as any).getStatusDescription('999')).toBe('Response');
    });
  });
});
