import fs from 'node:fs';
import path from 'node:path';
import type { OpenAPIV3 } from 'openapi-types';
import { log } from '@repo/observability/log';

/**
 * Types for parsing endpoint information
 */
export interface ParsedEndpoint {
  path: string;
  method: string;
  operationId: string;
  summary: string;
  description: string;
  tags: string[];
  parameters: OpenAPIV3.ParameterObject[];
  requestBody?: OpenAPIV3.RequestBodyObject;
  responses: { [statusCode: string]: OpenAPIV3.ResponseObject };
  security: OpenAPIV3.SecurityRequirementObject[];
}

export interface RouteInfo {
  filePath: string;
  apiPath: string;
  methods: string[];
  hasAuth: boolean;
  parameters: ParameterInfo[];
  responseSchemas: ResponseInfo[];
}

export interface ParameterInfo {
  name: string;
  type: 'path' | 'query' | 'header';
  required: boolean;
  schema: OpenAPIV3.SchemaObject;
  description?: string;
}

export interface ResponseInfo {
  statusCode: string;
  description: string;
  schema?: OpenAPIV3.SchemaObject;
  example?: any;
}

/**
 * Endpoint documentation parser for Next.js API routes
 */
export class EndpointParser {
  private apiRoutesPath: string;

  constructor(apiRoutesPath = '../../apps/api/app/api/integrations') {
    this.apiRoutesPath = apiRoutesPath;
  }

  /**
   * Parse all integration API routes and extract endpoint information
   */
  async parseAllEndpoints(): Promise<ParsedEndpoint[]> {
    const endpoints: ParsedEndpoint[] = [];
    const routeFiles = this.findRouteFiles(this.apiRoutesPath);

    for (const routeFile of routeFiles) {
      try {
        const parsedEndpoint = await this.parseRouteFile(routeFile);
        if (parsedEndpoint) {
          endpoints.push(...parsedEndpoint);
        }
      } catch (error) {
        log.warn(`Failed to parse route file ${routeFile}:`, { error });
      }
    }

    return endpoints;
  }

  /**
   * Find all route.ts files in the API directory
   */
  private findRouteFiles(dir: string): string[] {
    const routeFiles: string[] = [];

    const scanDirectory = (currentDir: string) => {
      if (!fs.existsSync(currentDir)) {
        return;
      }

      const items = fs.readdirSync(currentDir);

      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          scanDirectory(fullPath);
        } else if (item === 'route.ts') {
          routeFiles.push(fullPath);
        }
      }
    };

    scanDirectory(dir);
    return routeFiles;
  }

  /**
   * Parse a single route file and extract endpoint information
   */
  // biome-ignore lint/suspicious/useAwait: <explanation>
  private async parseRouteFile(
    filePath: string
  ): Promise<ParsedEndpoint[] | null> {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const apiPath = this.extractApiPath(filePath);
      const methods = this.extractHttpMethods(content);
      const hasAuth = this.hasAuthentication(content);
      const parameters = this.extractParameters(content, apiPath);
      const responses = this.extractResponses(content);

      const endpoints: ParsedEndpoint[] = [];

      for (const method of methods) {
        const endpoint: ParsedEndpoint = {
          path: apiPath,
          method: method.toUpperCase(),
          operationId: this.generateOperationId(method, apiPath),
          summary: this.generateSummary(method, apiPath),
          description: this.generateDescription(method, apiPath),
          tags: this.extractTags(apiPath),
          parameters,
          responses: this.formatResponses(responses),
          security: hasAuth ? [{ ApiKeyAuth: [] }] : [],
        };

        // Add request body for POST/PUT methods
        if (['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
          endpoint.requestBody = this.extractRequestBody(content, method);
        }

        endpoints.push(endpoint);
      }

      return endpoints;
    } catch (error) {
      console.error(`Error parsing route file ${filePath}:`, error);
      return null;
    }
  }

  /**
   * Extract API path from file path
   */
  private extractApiPath(filePath: string): string {
    // Convert file path to API path
    // e.g., apps/api/app/api/integrations/users/route.ts -> /api/integrations/users
    const relativePath = filePath.replace(/.*\/app\/api/, '/api');
    return relativePath
      .replace('/route.ts', '')
      .replace(/\/\[([^\]]+)\]/g, '/{$1}');
  }

  /**
   * Extract HTTP methods from route file content
   */
  private extractHttpMethods(content: string): string[] {
    const methods: string[] = [];
    const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];

    for (const method of httpMethods) {
      if (content.includes(`export const ${method}`) && method !== 'OPTIONS') {
        // Skip OPTIONS as it's usually for CORS
        methods.push(method);
      }
    }

    return methods;
  }

  /**
   * Check if route has authentication
   */
  private hasAuthentication(content: string): boolean {
    return content.includes('withLicenseKeyAuth');
  }

  /**
   * Extract parameters from route content and path
   */
  private extractParameters(
    content: string,
    apiPath: string
  ): OpenAPIV3.ParameterObject[] {
    const parameters: OpenAPIV3.ParameterObject[] = [];

    // Extract path parameters
    const pathParams = apiPath.match(/\{([^}]+)\}/g);
    if (pathParams) {
      for (const param of pathParams) {
        const paramName = param.slice(1, -1); // Remove { }
        parameters.push({
          name: paramName,
          in: 'path',
          required: true,
          schema: { type: 'string', format: 'uuid' },
          description: `${paramName.charAt(0).toUpperCase() + paramName.slice(1)} ID`,
        });
      }
    }

    // Extract query parameters from searchParams usage
    const queryParamMatches = content.match(
      /searchParams\.get\(['"`]([^'"`]+)['"`]\)/g
    );
    if (queryParamMatches) {
      const uniqueParams = new Set<string>();

      for (const match of queryParamMatches) {
        const paramName = match.match(/['"`]([^'"`]+)['"`]/)?.[1];
        if (paramName && !uniqueParams.has(paramName)) {
          uniqueParams.add(paramName);

          let schema: OpenAPIV3.SchemaObject = { type: 'string' };
          let description = `${paramName} parameter`;

          // Customize based on common parameter names
          // biome-ignore lint/style/useDefaultSwitchClause: <explanation>
          switch (paramName) {
            case 'query':
              description = 'Search query to filter results';
              break;
            case 'page': {
              schema = { type: 'integer', minimum: 1, default: 1 };
              description = 'Page number for pagination';
              break;
            }
            case 'per_page': {
              schema = {
                type: 'integer',
                minimum: 1,
                maximum: 100,
                default: 10,
              };
              description = 'Number of items per page';
              break;
            }
          }

          parameters.push({
            name: paramName,
            in: 'query',
            required: false,
            schema,
            description,
          });
        }
      }
    }

    return parameters;
  }

  /**
   * Extract response information from route content
   */
  private extractResponses(content: string): ResponseInfo[] {
    const responses: ResponseInfo[] = [];

    // Look for NextResponse.json calls with status codes
    const responseMatches = content.match(
      /NextResponse\.json\([^,]+,\s*\{\s*status:\s*(\d+)\s*\}/g
    );
    if (responseMatches) {
      for (const match of responseMatches) {
        const statusMatch = match.match(/status:\s*(\d+)/);
        if (statusMatch) {
          const statusCode = statusMatch[1];
          responses.push({
            statusCode,
            description: this.getStatusDescription(statusCode),
          });
        }
      }
    }

    // Add common responses if not found
    if (!responses.some((r) => r.statusCode === '200')) {
      responses.push({
        statusCode: '200',
        description: 'Successful response',
      });
    }

    // Add authentication error if route has auth
    if (this.hasAuthentication(content)) {
      responses.push({
        statusCode: '401',
        description: 'Authentication required',
      });
    }

    return responses;
  }

  /**
   * Format responses for OpenAPI specification
   */
  private formatResponses(responses: ResponseInfo[]): {
    [statusCode: string]: OpenAPIV3.ResponseObject;
  } {
    const formattedResponses: {
      [statusCode: string]: OpenAPIV3.ResponseObject;
    } = {};

    for (const response of responses) {
      switch (response.statusCode) {
        case '200':
        case '201':
          formattedResponses[response.statusCode] = {
            description: response.description,
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    data: {
                      type: 'object',
                      description: 'Response data',
                    },
                  },
                },
              },
            },
          };
          break;
        case '401':
          formattedResponses[response.statusCode] = {
            $ref: '#/components/responses/UnauthorizedError',
          };
          break;
        case '404':
          formattedResponses[response.statusCode] = {
            $ref: '#/components/responses/NotFoundError',
          };
          break;
        case '400':
          formattedResponses[response.statusCode] = {
            $ref: '#/components/responses/ValidationError',
          };
          break;
        default:
          formattedResponses[response.statusCode] = {
            description: response.description,
            content: {
              'application/json': {
                schema: { $ref: '#/components/schemas/ErrorResponse' },
              },
            },
          };
      }
    }

    return formattedResponses;
  }

  /**
   * Extract request body information for POST/PUT methods
   */
  private extractRequestBody(
    content: string,
    method: string
  ): OpenAPIV3.RequestBodyObject | undefined {
    if (!['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      return undefined;
    }

    // Check if the route uses request.json()
    if (content.includes('request.json()')) {
      return {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              description: 'Request body data',
            },
          },
        },
      };
    }

    return undefined;
  }

  /**
   * Generate operation ID from method and path
   */
  private generateOperationId(method: string, path: string): string {
    const pathParts = path
      .split('/')
      .filter((part) => part && !part.startsWith('{'));
    const resource = pathParts.at(-1) || 'resource';

    const methodMap: { [key: string]: string } = {
      GET: path.includes('{') ? 'get' : 'list',
      POST: 'create',
      PUT: 'update',
      PATCH: 'update',
      DELETE: 'delete',
    };

    const action = methodMap[method.toUpperCase()] || method.toLowerCase();
    return `${action}${resource.charAt(0).toUpperCase() + resource.slice(1)}`;
  }

  /**
   * Generate summary from method and path
   */
  private generateSummary(method: string, path: string): string {
    const pathParts = path
      .split('/')
      .filter((part) => part && !part.startsWith('{'));
    const resource = pathParts.at(-1) || 'resource';

    const methodMap: { [key: string]: string } = {
      GET: path.includes('{') ? 'Get' : 'List',
      POST: 'Create',
      PUT: 'Update',
      PATCH: 'Update',
      DELETE: 'Delete',
    };

    const action = methodMap[method.toUpperCase()] || method;
    return `${action} ${resource}`;
  }

  /**
   * Generate description from method and path
   */
  private generateDescription(method: string, path: string): string {
    const pathParts = path
      .split('/')
      .filter((part) => part && !part.startsWith('{'));
    const resource = pathParts.at(-1) || 'resource';

    const methodMap: { [key: string]: string } = {
      GET: path.includes('{')
        ? 'Retrieve detailed information about a specific'
        : 'Retrieve a paginated list of',
      POST: 'Create a new',
      PUT: 'Update an existing',
      PATCH: 'Partially update an existing',
      DELETE: 'Delete an existing',
    };

    const action =
      methodMap[method.toUpperCase()] || `Perform ${method} operation on`;
    return `${action} ${resource}`;
  }

  /**
   * Extract tags from API path
   */
  private extractTags(path: string): string[] {
    const pathParts = path
      .split('/')
      .filter((part) => part && !part.startsWith('{'));

    // Use the main resource as tag
    if (pathParts.includes('users')) {
      return ['Users'];
    }

    if (pathParts.includes('organizations')) {
      return ['Organizations'];
    }

    return ['API'];
  }

  /**
   * Get status code description
   */
  private getStatusDescription(statusCode: string): string {
    const descriptions: { [key: string]: string } = {
      '200': 'Successful response',
      '201': 'Created successfully',
      '400': 'Bad request',
      '401': 'Unauthorized',
      '403': 'Forbidden',
      '404': 'Not found',
      '422': 'Validation error',
      '500': 'Internal server error',
    };

    return descriptions[statusCode] || 'Response';
  }

  /**
   * Parse specific endpoint by path and method
   */
  async parseEndpoint(
    apiPath: string,
    method: string
  ): Promise<ParsedEndpoint | null> {
    const allEndpoints = await this.parseAllEndpoints();
    return (
      allEndpoints.find(
        (endpoint) =>
          endpoint.path === apiPath &&
          endpoint.method.toUpperCase() === method.toUpperCase()
      ) || null
    );
  }

  /**
   * Get route information for a specific file
   */
  getRouteInfo(filePath: string): RouteInfo | null {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const apiPath = this.extractApiPath(filePath);

      return {
        filePath,
        apiPath,
        methods: this.extractHttpMethods(content),
        hasAuth: this.hasAuthentication(content),
        parameters: this.extractParameters(content, apiPath).map((param) => ({
          name: param.name,
          type: param.in as 'path' | 'query' | 'header',
          required: param.required || false,
          schema: param.schema as OpenAPIV3.SchemaObject,
          description: param.description,
        })),
        responseSchemas: this.extractResponses(content),
      };
    } catch (error) {
      console.error(`Error getting route info for ${filePath}:`, error);
      return null;
    }
  }
}

/**
 * Create a singleton instance of the endpoint parser
 */
export const endpointParser = new EndpointParser();

/**
 * Parse all integration endpoints
 */

// biome-ignore lint/suspicious/useAwait: <explanation>
export async function parseAllIntegrationEndpoints(): Promise<
  ParsedEndpoint[]
> {
  return endpointParser.parseAllEndpoints();
}

/**
 * Parse a specific endpoint
 */

// biome-ignore lint/suspicious/useAwait: <explanation>
export async function parseEndpoint(
  apiPath: string,
  method: string
): Promise<ParsedEndpoint | null> {
  return endpointParser.parseEndpoint(apiPath, method);
}
