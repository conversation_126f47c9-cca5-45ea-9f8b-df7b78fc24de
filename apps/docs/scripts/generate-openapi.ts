#!/usr/bin/env tsx

import { writeFileSync } from 'fs';
import { join } from 'path';
import { generateCompleteOpenAPISpecJSON, validateDocumentationCoverage, getEndpointStatistics } from '../lib/documentation-generator';

/**
 * CLI script to generate OpenAPI specification and validate documentation
 */
async function main() {
  console.log('🚀 Generating OpenAPI specification...');
  
  try {
    // Generate the complete OpenAPI specification
    const openApiSpec = await generateCompleteOpenAPISpecJSON();
    
    // Write to file
    const outputPath = join(process.cwd(), 'openapi.json');
    writeFileSync(outputPath, openApiSpec, 'utf-8');
    console.log(`✅ OpenAPI specification generated: ${outputPath}`);
    
    // Validate documentation coverage
    console.log('\n📊 Validating documentation coverage...');
    const validation = await validateDocumentationCoverage();
    
    console.log(`Coverage: ${validation.coverage.documented}/${validation.coverage.total} endpoints (${validation.coverage.percentage.toFixed(1)}%)`);
    
    if (validation.issues.length > 0) {
      console.log('\n⚠️  Issues found:');
      validation.issues.forEach(issue => console.log(`  - ${issue}`));
    } else {
      console.log('✅ No documentation issues found');
    }
    
    // Get endpoint statistics
    console.log('\n📈 Endpoint statistics:');
    const stats = await getEndpointStatistics();
    
    console.log(`Total endpoints: ${stats.totalEndpoints}`);
    console.log(`Authenticated endpoints: ${stats.authenticationRequired}`);
    console.log(`Public endpoints: ${stats.publicEndpoints}`);
    
    console.log('\nEndpoints by method:');
    Object.entries(stats.endpointsByMethod).forEach(([method, count]) => {
      console.log(`  ${method}: ${count}`);
    });
    
    console.log('\nEndpoints by tag:');
    Object.entries(stats.endpointsByTag).forEach(([tag, count]) => {
      console.log(`  ${tag}: ${count}`);
    });
    
    console.log('\n🎉 Documentation generation completed successfully!');
    
  } catch (error) {
    console.error('❌ Error generating documentation:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);