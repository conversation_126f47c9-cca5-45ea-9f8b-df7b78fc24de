import { keys as flags } from '@repo/feature-flags/keys';
import { keys as core } from '@repo/next-config/keys';
import { keys as observability } from '@repo/observability/keys';
import { keys as rateLimit } from '@repo/rate-limit/keys';
import { keys as security } from '@repo/security/keys';
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  extends: [core(), observability(), flags(), security(), rateLimit()],
  server: {
    CORE_BASE_URL: z.string().min(1).url(),
    CORE_INCOMING_API_KEY: z.string().min(1),
    CORE_OUTGOING_API_KEY: z.string().min(1),
  },
  client: {},
  runtimeEnv: {
    CORE_BASE_URL: process.env.CORE_BASE_URL,
    CORE_INCOMING_API_KEY: process.env.CORE_INCOMING_API_KEY,
    CORE_OUTGOING_API_KEY: process.env.CORE_OUTGOING_API_KEY,
  },
});
