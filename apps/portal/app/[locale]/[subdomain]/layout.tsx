import { TitleUpdater } from '@/components/title-updater';
import { env } from '@/env';
import { OrganizationProvider } from '@/lib/organization-context';
import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import type { ReactNode } from 'react';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ subdomain: string }>;
}): Promise<Metadata> {
  const appUrl = new URL(env.NEXT_PUBLIC_WEB_URL);
  const rootDomain = appUrl.host;

  const { subdomain } = await params;

  // Try to get organization data for better metadata
  try {
    const organization = await database.organization.findUnique({
      where: { slug: subdomain },
      select: { name: true, slug: true },
    });

    if (organization) {
      return {
        title: `${organization.name} - ${rootDomain}`,
        description: `${organization.name} portal on ${rootDomain}`,
      };
    }
  } catch (error) {
    log.warn('Error fetching organization for metadata:', { error, subdomain });
  }

  return {
    title: `${subdomain}.${rootDomain}`,
    description: `Subdomain page for ${subdomain}.${rootDomain}`,
  };
}

export default async function SubdomainLayout({
  children,
  params,
}: {
  children: ReactNode;
  params: Promise<{ subdomain: string }>;
}) {
  const { subdomain } = await params;

  // TODO: use redis to cache organization data
  // Resolve organization from subdomain
  let organization: {
    id: string;
    name: string;
    slug: string;
    logo: string | null;
  } | null = null;
  try {
    const dbOrganization = await database.organization.findUnique({
      where: { slug: subdomain },
      select: {
        id: true,
        name: true,
        slug: true,
        logo: true,
      },
    });

    if (!dbOrganization || !dbOrganization.slug) {
      log.warn('Organization not found for subdomain:', { subdomain });
      notFound();
    }

    // Transform to match our Organization type (ensuring slug is not null)
    organization = {
      id: dbOrganization.id,
      name: dbOrganization.name,
      slug: dbOrganization.slug,
      logo: dbOrganization.logo,
    };
  } catch (error) {
    log.error('Error resolving organization from subdomain:', {
      error,
      subdomain,
    });
    notFound();
  }

  return (
    <OrganizationProvider initialOrganization={organization}>
      <TitleUpdater />
      {children}
    </OrganizationProvider>
  );
}
