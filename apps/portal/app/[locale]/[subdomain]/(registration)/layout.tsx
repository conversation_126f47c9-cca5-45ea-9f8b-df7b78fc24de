import { TopNavigation } from '@/components/top-navigation';
import { Toaster } from '@repo/design-system/components/ui/sonner';
import type * as React from 'react';

type RegistrationLayoutProps = {
  children: React.ReactNode;
};

export default function RegistrationLayout({
  children,
}: RegistrationLayoutProps) {
  return (
    <>
      <div className="container mx-auto flex min-h-screen flex-col">
        <TopNavigation />
        <div className="flex flex-1">{children}</div>
      </div>
      <Toaster />
    </>
  );
}
