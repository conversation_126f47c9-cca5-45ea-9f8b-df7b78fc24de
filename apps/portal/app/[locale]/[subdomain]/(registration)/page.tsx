'use client';

import { AppSidebar } from '@/components/app-sidebar';
import { RegistrationForm } from '@/components/registration/registration-form';
import { RegistrationStepper } from '@/components/registration/registration-stepper';
import {
  SidebarInset,
  SidebarProvider,
} from '@repo/design-system/components/ui/sidebar';
import type * as React from 'react';

export default function RegistrationPage() {
  return (
    <div className="mx-auto w-full p-6">
      <RegistrationStepper />

      <SidebarProvider
        style={
          {
            '--sidebar-width': '16rem',
          } as React.CSSProperties
        }
        className="space-x-8"
      >
        <AppSidebar />
        <SidebarInset>
          <RegistrationForm />
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}
