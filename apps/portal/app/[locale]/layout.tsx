import '../globals.css';
import { DesignSystemProvider } from '@repo/design-system';
import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import { <PERSON>eist, <PERSON>eist_Mono } from 'next/font/google';
import type * as React from 'react';

type RootLayoutProperties = {
  readonly children: React.ReactNode;
  readonly params: Promise<{
    locale: string;
  }>;
};

// Generate metadata for the page
export async function generateMetadata({
  params,
}: RootLayoutProperties): Promise<Metadata> {
  const { locale } = await params;
  await getDictionary(locale); // Load dictionary for potential future use

  // In server components, we can't access the organization context directly
  // We'll set a generic title that will be updated client-side if needed
  return {
    title: 'Invoice Portal',
    description: 'Track and manage your invoices and documents',
  };
}

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

const RootLayout = async ({ children, params }: RootLayoutProperties) => {
  const { locale } = await params;
  // We still get the dictionary for potential future use
  await getDictionary(locale);

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <DesignSystemProvider>{children}</DesignSystemProvider>
      </body>
    </html>
  );
};

export default RootLayout;
