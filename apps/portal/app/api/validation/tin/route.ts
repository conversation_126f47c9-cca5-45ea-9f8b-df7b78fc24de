import { env } from '@/env';
import { database } from '@repo/database';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

const postHandler = async (request: NextRequest) => {
  try {
    const body = await request.json();

    // Extract subdomain to get organization context
    const subdomain = request.headers.get('X-Organization');
    let userId = '1'; // Default fallback

    if (subdomain) {
      // Look up organization by subdomain
      // Get the first user ID from the organization members
      const organization = await database.organization.findUnique({
        where: { slug: subdomain },
        select: {
          id: true,
          members: {
            select: {
              userId: true,
            },
          },
        },
      });

      if (organization) {
        userId = organization.members[0]?.userId;
      } else {
        log.warn('Organization not found for subdomain in TIN validation', {
          subdomain,
        });
      }
    }

    // Forward request to core API
    const response = await fetch(`${env.CORE_BASE_URL}/api/v1/validation/tin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Invois-Key': env.CORE_OUTGOING_API_KEY,
        'X-User-ID': userId,
      },
      body: JSON.stringify(body),
    });

    return NextResponse.json(
      {},
      {
        status: response.status,
      }
    );
  } catch (error) {
    log.error('Error in TIN validation API', { error });

    return NextResponse.json(
      { error: 'Failed to validate TIN' },
      { status: 500 }
    );
  }
};

export const POST = postHandler;
