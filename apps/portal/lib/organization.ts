import { database } from '@repo/database';
import type { NextRequest } from 'next/server';

/**
 * Extracts the subdomain from the request hostname
 * @param request The Next.js request object
 * @param rootDomain The root domain to extract subdomain from
 * @returns The subdomain or null if no subdomain is present
 */
export function extractSubdomain(
  request: NextRequest,
  rootDomain: string
): string | null {
  const url = request.url;
  const host = request.headers.get('host') || '';
  const hostname = host.split(':')[0];

  // Local development environment
  if (url.includes('localhost') || url.includes('127.0.0.1')) {
    // Try to extract subdomain from the full URL
    const fullUrlMatch = url.match(/http:\/\/([^.]+)\.localhost/);
    if (fullUrlMatch?.[1]) {
      return fullUrlMatch[1];
    }

    // Fallback to host header approach
    if (hostname.includes('.localhost')) {
      return hostname.split('.')[0];
    }

    return null;
  }

  // Production environment
  const rootDomainFormatted = rootDomain.split(':')[0];

  // Handle preview deployment URLs (tenant---branch-name.vercel.app)
  if (hostname.includes('---') && hostname.endsWith('.vercel.app')) {
    const parts = hostname.split('---');
    return parts.length > 0 ? parts[0] : null;
  }

  // Regular subdomain detection
  const isSubdomain =
    hostname !== rootDomainFormatted &&
    hostname !== `www.${rootDomainFormatted}` &&
    hostname.endsWith(`.${rootDomainFormatted}`);

  return isSubdomain ? hostname.replace(`.${rootDomainFormatted}`, '') : null;
}

/**
 * Resolves an organization based on the subdomain
 * @param request The Next.js request object
 * @param rootDomain The root domain to extract subdomain from
 * @returns The organization or null if no organization is found
 */
export async function resolveOrganizationFromSubdomain(
  request: NextRequest,
  rootDomain: string
) {
  const subdomain = extractSubdomain(request, rootDomain);

  if (!subdomain) {
    return null;
  }

  // Look up organization by slug (which will be used as subdomain)
  const organization = await database.organization.findUnique({
    where: {
      slug: subdomain,
    },
    select: {
      id: true,
      name: true,
      slug: true,
      logo: true,
    },
  });

  return organization;
}

/**
 * Checks if the current request is for the main application or an organization portal
 * @param request The Next.js request object
 * @param rootDomain The root domain to check against
 * @returns True if this is the main application, false if it's an organization portal
 */
export function isMainApplication(
  request: NextRequest,
  rootDomain: string
): boolean {
  const hostname = request.headers.get('host') || '';
  return hostname === rootDomain || hostname === `www.${rootDomain}`;
}
