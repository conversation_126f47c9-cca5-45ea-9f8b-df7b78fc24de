import * as z from 'zod';

const PHONE_REGEX = /^\+[1-9]\d{1,14}$/;

// Step 1: ID related fields
export const step1Schema = z.object({
  idType: z.string().min(1, {
    message: 'ID Type is required.',
  }),
  identificationNumber: z.string().min(1, {
    message: 'Identification number is required.',
  }),
  taxIdentificationNo: z.string().min(1, {
    message: 'Tax Identification No. is required.',
  }),
});

// Step 2: Contact related fields
export const step2Schema = z
  .object({
    personalCompanyName: z.string().min(1, {
      message: 'Personal / Company Name is required.',
    }),
    email: z.string().email({
      message: 'Invalid email address.',
    }),
    confirmEmail: z.string().email({
      message: 'Invalid email address.',
    }),
    phone: z
      .string()
      .min(1, {
        message: 'Phone number is required.',
      })
      .refine(
        (value) => {
          // Basic validation for E.164 format (starts with + followed by digits)
          return PHONE_REGEX.test(value);
        },
        {
          message: 'Please enter a valid phone number in international format.',
        }
      ),
  })
  .refine((data) => data.email === data.confirmEmail, {
    message: 'Emails do not match',
    path: ['confirmEmail'],
  });

// Step 3: Business related fields
export const step3Schema = z.object({
  businessRegistrationNoOld: z.string().optional(),
  msicCode: z.string().min(1, {
    message: 'MSIC Code is required.',
  }),
  sstNo: z.string().optional(),
  addressLine1: z.string().min(1, {
    message: 'Address Line 1 is required.',
  }),
  addressLine2: z.string().optional(),
  addressLine3: z.string().optional(),
  postcode: z.string().min(1, {
    message: 'Postcode is required.',
  }),
  city: z.string().min(1, {
    message: 'City is required.',
  }),
  state: z.string().min(1, {
    message: 'State is required.',
  }),
  country: z.string().min(1, {
    message: 'Country is required.',
  }),
  invoiceNo: z.string().min(1, {
    message: 'Invoice No. is required.',
  }),
  invoiceAmount: z.string(),
});

// Summary step with CAPTCHA
export const summarySchema = z.object({
  recaptcha: z.string().min(1, {
    message: 'Please complete the captcha verification.',
  }),
});

// Complete registration schema
export const registrationSchema = step1Schema
  .merge(step2Schema.innerType())
  .merge(step3Schema)
  .merge(summarySchema);
