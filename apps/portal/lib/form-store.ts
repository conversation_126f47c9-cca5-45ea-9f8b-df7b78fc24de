'use client';

import type { z } from 'zod';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { registrationSchema } from './validations/registration';

// Define the store type
export type FormData = z.infer<typeof registrationSchema>;

// Define the store interface
interface FormStore {
  formData: FormData;
  currentStep: number;
  totalSteps: number;
  taxIdVerified: boolean;
  isHydrated: boolean;
  updateFormData: (data: Partial<FormData>) => void;
  setCurrentStep: (step: number) => void;
  setTaxIdVerified: (verified: boolean) => void;
  reset: () => void;
}

// Create the initial form data
const initialFormData: FormData = {
  idType: 'BRN',
  identificationNumber: '',
  taxIdentificationNo: '',
  personalCompanyName: '',
  email: '',
  confirmEmail: '',
  phone: '',
  businessRegistrationNoOld: '',
  msicCode: '',
  sstNo: '',
  addressLine1: '',
  addressLine2: '',
  addressLine3: '',
  postcode: '',
  city: '',
  state: '',
  country: '',
  invoiceNo: '',
  invoiceAmount: '',
  recaptcha: '',
};

// Create the store with persistence
export const useFormStore = create<FormStore>()(
  persist(
    (set) => ({
      formData: initialFormData,
      currentStep: 0,
      totalSteps: 4,
      taxIdVerified: false,
      isHydrated: false, // Track hydration state
      updateFormData: (data) =>
        set((state) => ({
          formData: { ...state.formData, ...data },
        })),
      setCurrentStep: (step) => set({ currentStep: step }),
      setTaxIdVerified: (verified) => set({ taxIdVerified: verified }),
      reset: () =>
        set({
          formData: initialFormData,
          currentStep: 0,
          taxIdVerified: false,
        }),
    }),
    {
      name: 'registration-form-storage',
      onRehydrateStorage: () => (state) => {
        // When storage is rehydrated, update the hydration state
        if (state) {
          state.isHydrated = true;
        }
      },
    }
  )
);
