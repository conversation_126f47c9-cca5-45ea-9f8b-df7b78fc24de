'use client';

import {
  type DefaultValues,
  type FieldValues,
  type UseFormReturn,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { useEffect, useState } from 'react';
import type { ZodSchema } from 'zod';
import { useFormStore } from '../form-store';
import type { FormData } from '../form-store';

/**
 * Custom hook to handle form hydration with Zustand persist
 *
 * @param schema - Zod schema for form validation
 * @param getDefaultValues - Function to get default values from formData
 * @param initialValues - Initial values to use before hydration
 * @returns Form instance and hydration state
 */
export function useHydratedForm<T extends FieldValues>(
  schema: ZodSchema,
  getDefaultValues: (formData: FormData) => T,
  initialValues: DefaultValues<T>
): { form: UseFormReturn<T>; isHydrated: boolean } {
  const [localHydrated, setLocalHydrated] = useState(false);
  const { formData, isHydrated } = useFormStore();

  // Initialize form with initial values
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues: initialValues,
  });

  // Update form values when store is hydrated
  useEffect(() => {
    if (isHydrated && !localHydrated) {
      form.reset(getDefaultValues(formData));
      setLocalHydrated(true);
    }
  }, [formData, form, isHydrated, localHydrated, getDefaultValues]);

  // Fallback for when the store might not trigger onRehydrateStorage
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!localHydrated) {
        form.reset(getDefaultValues(formData));
        setLocalHydrated(true);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [formData, form, localHydrated, getDefaultValues]);

  return { form, isHydrated: localHydrated };
}
