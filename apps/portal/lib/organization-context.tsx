'use client';

import { organization, useSession } from '@repo/auth/client';
import { log } from '@repo/observability/log';
import type * as React from 'react';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

export type Organization = {
  id: string;
  name: string;
  slug: string;
  logo?: string | null;
};

export type OrganizationContextType = {
  organization: Organization | null;
  isOrganizationPortal: boolean;
  isLoading: boolean;
  setActiveOrganization: (org: Organization) => Promise<void>;
};

export const OrganizationContext = createContext<OrganizationContextType>({
  organization: null,
  isOrganizationPortal: false,
  isLoading: true,
  setActiveOrganization: async () => {
    // Default implementation - will be overridden by provider
  },
});

export const useOrganization = () => useContext(OrganizationContext);

export const OrganizationProvider = ({
  children,
  initialOrganization,
}: {
  children: React.ReactNode;
  initialOrganization?: Organization | null;
}) => {
  const [currentOrganization, setCurrentOrganization] =
    useState<Organization | null>(initialOrganization || null);
  const [isLoading, setIsLoading] = useState(!initialOrganization);
  const { data: session } = useSession();

  // Function to set the active organization in better-auth
  const setActiveOrganization = useCallback(
    async (org: Organization) => {
      try {
        if (session?.user) {
          // Use better-auth organization client to set active organization
          await organization.setActive({
            organizationId: org.id,
          });
          setCurrentOrganization(org);
        }
      } catch (error) {
        log.warn('Error setting active organization:', { error });
      }
    },
    [session?.user]
  );

  useEffect(() => {
    // If we have an initial organization from the subdomain, set it as active
    if (initialOrganization && session?.user) {
      setActiveOrganization(initialOrganization).finally(() => {
        setIsLoading(false);
      });
    } else if (initialOrganization) {
      // If we have initial organization but no session, just set it locally
      setCurrentOrganization(initialOrganization);
      setIsLoading(false);
    } else {
      setIsLoading(false);
    }
  }, [initialOrganization, session?.user, setActiveOrganization]);

  return (
    <OrganizationContext.Provider
      value={{
        organization: currentOrganization,
        isOrganizationPortal: !!currentOrganization,
        isLoading,
        setActiveOrganization,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
};
