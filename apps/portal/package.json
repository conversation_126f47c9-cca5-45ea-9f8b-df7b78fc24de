{"name": "portal", "private": true, "scripts": {"dev": "next dev -p 3003 --turbopack", "build": "next build", "start": "next start", "analyze": "ANALYZE=true pnpm build", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@arcjet/next": "1.0.0-beta.5", "@radix-ui/react-icons": "^1.3.2", "@repo/design-system": "workspace:*", "@repo/email": "workspace:*", "@repo/feature-flags": "workspace:*", "@repo/internationalization": "workspace:*", "@repo/next-config": "workspace:*", "@repo/observability": "workspace:*", "@repo/rate-limit": "workspace:*", "@repo/security": "workspace:*", "@repo/seo": "workspace:*", "@sentry/nextjs": "^9.13.0", "@t3-oss/env-nextjs": "^0.12.0", "date-fns": "^4.1.0", "fumadocs-core": "^15.2.7", "import-in-the-middle": "^1.13.1", "lucide-react": "^0.488.0", "mdx-bundler": "^10.1.1", "next": "15.3.0", "react": "19.1.0", "react-dom": "19.1.0", "react-google-recaptcha": "^3.1.0", "react-wrap-balancer": "^1.1.1", "require-in-the-middle": "^7.5.2", "sharp": "^0.34.1", "shiki": "^3.2.2", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@types/react-google-recaptcha": "^2.1.9", "tailwindcss": "^4.1.4", "typescript": "^5.8.3"}}