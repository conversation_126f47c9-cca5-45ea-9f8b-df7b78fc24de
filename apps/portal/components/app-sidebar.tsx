'use client';

import { useFormStore } from '@/lib/form-store';
import { registrationSteps } from '@/lib/steps';
import type * as React from 'react';

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarHeader,
} from '@repo/design-system/components/ui/sidebar';
import {
  Stepper,
  StepperDescription,
  StepperIndicator,
  StepperItem,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
} from '@repo/design-system/components/ui/stepper';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { currentStep, setCurrentStep } = useFormStore();

  // Convert 0-based index to 1-based step number for the stepper
  const stepperValue = currentStep + 1;

  // Handle step change from the stepper
  const handleStepChange = (step: number) => {
    // Convert 1-based step number back to 0-based index
    const targetStep = step - 1;

    // Only allow navigation to previous steps or the current step
    if (targetStep <= currentStep) {
      setCurrentStep(targetStep);
    }
  };

  return (
    <Sidebar variant="floating" {...props} className="relative h-fit">
      <SidebarHeader>
        <div className="p-4">
          <h2 className="font-semibold text-lg">Submission Steps</h2>
          <p className="text-muted-foreground text-sm">
            Complete all steps to request an e-invoice
          </p>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup className="px-4 pb-8">
          <Stepper
            value={stepperValue}
            onValueChange={handleStepChange}
            orientation="vertical"
          >
            {registrationSteps.map(({ step, title, description }) => (
              <StepperItem
                key={step}
                step={step}
                className="relative not-last:flex-1 items-start"
                // Disable steps that are ahead of the current step
                disabled={step - 1 > currentStep}
              >
                <StepperTrigger className="items-start rounded pb-12 last:pb-0">
                  <StepperIndicator />
                  <div className="mt-0.5 space-y-0.5 px-2 text-left">
                    <StepperTitle>{title}</StepperTitle>
                    <StepperDescription>{description}</StepperDescription>
                  </div>
                </StepperTrigger>
                {step < registrationSteps.length && (
                  <StepperSeparator className="-order-1 -translate-x-1/2 absolute inset-y-0 top-[calc(1.5rem+0.125rem)] left-4 m-0 group-data-[orientation=vertical]/stepper:h-[calc(100%-1.5rem-0.25rem)] group-data-[orientation=horizontal]/stepper:w-[calc(100%-1.5rem-0.25rem)] group-data-[orientation=horizontal]/stepper:flex-none" />
                )}
              </StepperItem>
            ))}
          </Stepper>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
