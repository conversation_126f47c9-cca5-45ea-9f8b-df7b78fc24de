'use client';

import { FormLoading } from '@/components/ui/form-loading';
import { useFormStore } from '@/lib/form-store';
import { useHydratedForm } from '@/lib/hooks/use-hydrated-form';
import { summarySchema } from '@/lib/validations/registration';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
} from '@repo/design-system/components/ui/form';
import { toast } from '@repo/design-system/components/ui/sonner';
import type { z } from 'zod';

export function SummaryStep() {
  const { formData, updateFormData, setCurrentStep } = useFormStore();

  // Use the custom hook for form hydration
  const { form, isHydrated } = useHydratedForm<z.infer<typeof summarySchema>>(
    summarySchema,
    (formData) => ({
      recaptcha: formData.recaptcha,
    }),
    {
      recaptcha: '',
    }
  );

  // Handle form submission
  const onSubmit = (data: z.infer<typeof summarySchema>) => {
    // Update the store with form data
    updateFormData(data);

    // TODO: In a real application, you would submit the form data to an API
    toast.success('Submitted successfully!');
  };

  // Handle back button click
  const handleBack = () => {
    // Save current form values to store even if incomplete
    updateFormData(form.getValues());

    // Navigate to the previous step
    setCurrentStep(2);
  };

  // Show loading state if not hydrated yet
  if (!isHydrated) {
    return <FormLoading />;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="rounded-md border p-4">
          <h3 className="mb-2 font-medium">ID Information</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="text-muted-foreground">ID Type:</div>
            <div>{formData.idType}</div>
            <div className="text-muted-foreground">Identification Number:</div>
            <div>{formData.identificationNumber}</div>
            <div className="text-muted-foreground">Tax Identification No:</div>
            <div>{formData.taxIdentificationNo}</div>
          </div>
        </div>

        <div className="rounded-md border p-4">
          <h3 className="mb-2 font-medium">Contact Details</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="text-muted-foreground">Name:</div>
            <div>{formData.personalCompanyName}</div>
            <div className="text-muted-foreground">Email:</div>
            <div>{formData.email}</div>
            <div className="text-muted-foreground">Phone:</div>
            <div>{formData.phone}</div>
          </div>
        </div>

        <div className="rounded-md border p-4">
          <h3 className="mb-2 font-medium">Business Details</h3>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="text-muted-foreground">MSIC Code:</div>
            <div>{formData.msicCode}</div>
            <div className="text-muted-foreground">Address:</div>
            <div>{formData.addressLine1}</div>
            <div className="text-muted-foreground">City:</div>
            <div>{formData.city}</div>
            <div className="text-muted-foreground">State:</div>
            <div>{formData.state}</div>
            <div className="text-muted-foreground">Country:</div>
            <div>{formData.country}</div>
            <div className="text-muted-foreground">Invoice No:</div>
            <div>{formData.invoiceNo}</div>
          </div>
        </div>

        <FormField
          control={form.control}
          name="recaptcha"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Verification</FormLabel>
              <FormControl>
                {/* TODO: In a real app, this would be a reCAPTCHA component */}
                <div className="flex h-12 items-center justify-center rounded border">
                  <button
                    type="button"
                    onClick={() => field.onChange('verified')}
                    className="text-blue-500 text-sm hover:underline"
                  >
                    Click to verify you're not a robot
                  </button>
                </div>
              </FormControl>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <div className="flex justify-between pt-4">
          <Button type="button" variant="outline" onClick={handleBack}>
            Back
          </Button>
          <Button type="submit">Submit</Button>
        </div>
      </form>
    </Form>
  );
}
