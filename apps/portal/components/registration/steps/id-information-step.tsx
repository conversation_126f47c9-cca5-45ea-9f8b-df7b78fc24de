'use client';

import { FormLoading } from '@/components/ui/form-loading';
import { useFormStore } from '@/lib/form-store';
import { useHydratedForm } from '@/lib/hooks/use-hydrated-form';
import { step1Schema } from '@/lib/validations/registration';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { log } from '@repo/observability/log';
import { useEffect, useState } from 'react';

import type { z } from 'zod';

export function IdInformationStep() {
  const { updateFormData, setCurrentStep, taxIdVerified, setTaxIdVerified } =
    useFormStore();

  const [isVerifying, setIsVerifying] = useState(false);

  // Use the custom hook for form hydration
  const { form, isHydrated } = useHydratedForm<z.infer<typeof step1Schema>>(
    step1Schema,
    (formData) => ({
      idType: formData.idType,
      identificationNumber: formData.identificationNumber,
      taxIdentificationNo: formData.taxIdentificationNo,
    }),
    {
      idType: 'BRN', // Default value before hydration
      identificationNumber: '',
      taxIdentificationNo: '',
    }
  );

  // Handle form submission
  const onSubmit = (data: z.infer<typeof step1Schema>) => {
    // Check if tax ID is verified
    if (!taxIdVerified) {
      form.setError('taxIdentificationNo', {
        type: 'manual',
        message:
          'Please verify your Tax Identification Number before proceeding',
      });
      return;
    }

    // Update the store with form data
    updateFormData(data);

    // Navigate to the next step
    setCurrentStep(1);
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    // Watch for any form field changes and reset tax ID verification
    if (!isHydrated) {
      return; // Don't trigger during initial hydration
    }

    const subscription = form.watch(() => {
      if (taxIdVerified) {
        setTaxIdVerified(false);
      }
    });

    return () => subscription.unsubscribe();
  }, [isHydrated, form]); // Only depend on form

  const verifyTaxId = async () => {
    const taxId = form.getValues('taxIdentificationNo');
    if (!taxId) {
      form.setError('taxIdentificationNo', {
        type: 'manual',
        message: 'Please enter a Tax Identification Number',
      });
      return;
    }

    setIsVerifying(true);

    try {
      form.clearErrors();

      const response = await fetch('/api/validation/tin', {
        method: 'POST',
        body: JSON.stringify({
          tin: taxId,
          registrationType: form.getValues('idType'),
          registrationNumber: form.getValues('identificationNumber'),
        }),
      });

      if (response.status !== 200) {
        form.setError('identificationNumber', {
          type: 'manual',
        });
        form.setError('taxIdentificationNo', {
          type: 'manual',
          message: 'Failed to validate TIN. Please try again.',
        });
        throw new Error('Failed to validate TIN');
      }

      setTaxIdVerified(true);
      toast.success('TIN verified successfully');
    } catch (error) {
      log.warn('Error verifying tax ID', { error });
      toast.error('Failed to verify TIN. Please try again.');
      setTaxIdVerified(false);
    } finally {
      setIsVerifying(false);
    }
  };

  // Show loading state if not hydrated yet
  if (!isHydrated) {
    return <FormLoading />;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="idType"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>ID Type</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value} // Use value instead of defaultValue
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select ID Type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="BRN">
                    Business Registration Number
                  </SelectItem>
                  <SelectItem value="NRIC">NRIC</SelectItem>
                  <SelectItem value="PASSPORT">Passport</SelectItem>
                  <SelectItem value="ARMY">ARMY</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <FormField
          control={form.control}
          name="identificationNumber"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Identification Number</FormLabel>
              <FormControl>
                <Input placeholder="Enter identification number" {...field} />
              </FormControl>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <FormField
          control={form.control}
          name="taxIdentificationNo"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Tax Identification Number</FormLabel>
              <div className="flex space-x-2">
                <FormControl>
                  <Input
                    placeholder="Enter tax identification number"
                    className="flex-1"
                    {...field}
                  />
                </FormControl>
                <Button
                  id="verify-tax-id"
                  type="button"
                  variant="outline"
                  onClick={verifyTaxId}
                  disabled={taxIdVerified || isVerifying}
                >
                  {(() => {
                    if (isVerifying) {
                      return 'Verifying...';
                    }
                    if (taxIdVerified) {
                      return 'Verified';
                    }
                    return 'Verify';
                  })()}
                </Button>
              </div>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <div className="flex justify-end pt-4">
          <Button type="submit" disabled={!taxIdVerified}>
            Next
          </Button>
        </div>
      </form>
    </Form>
  );
}
