'use client';

import { FormLoading } from '@/components/ui/form-loading';
import { useFormStore } from '@/lib/form-store';
import { useHydratedForm } from '@/lib/hooks/use-hydrated-form';
import { step2Schema } from '@/lib/validations/registration';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { PhoneInput } from '@repo/design-system/components/ui/phone-input';
import type { z } from 'zod';

export function ContactDetailsStep() {
  const { updateFormData, setCurrentStep } = useFormStore();

  // Use the custom hook for form hydration
  const { form, isHydrated } = useHydratedForm<z.infer<typeof step2Schema>>(
    step2Schema,
    (formData) => ({
      personalCompanyName: formData.personalCompanyName,
      email: formData.email,
      confirmEmail: formData.confirmEmail,
      phone: formData.phone,
    }),
    {
      personalCompanyName: '',
      email: '',
      confirmEmail: '',
      phone: '',
    }
  );

  // Handle form submission
  const onSubmit = (data: z.infer<typeof step2Schema>) => {
    // Update the store with form data
    updateFormData(data);

    // Navigate to the next step
    setCurrentStep(2);
  };

  // Handle back button click
  const handleBack = () => {
    // Save current form values to store even if incomplete
    updateFormData(form.getValues());

    // Navigate to the previous step
    setCurrentStep(0);
  };

  // Show loading state if not hydrated yet
  if (!isHydrated) {
    return <FormLoading />;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="personalCompanyName"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Personal / Company Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter name" {...field} />
              </FormControl>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="Enter email address"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmEmail"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Confirm Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="Confirm email address"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <GridFormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <PhoneInput
                  value={field.value}
                  onChange={(value) => {
                    field.onChange(value);
                  }}
                  defaultCountry="MY"
                  placeholder="012456789"
                />
              </FormControl>
              <FormMessage />
            </GridFormItem>
          )}
        />

        <div className="flex justify-between pt-4">
          <Button type="button" variant="outline" onClick={handleBack}>
            Back
          </Button>
          <Button type="submit">Next</Button>
        </div>
      </form>
    </Form>
  );
}
