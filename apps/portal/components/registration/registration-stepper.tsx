'use client';

import { useFormStore } from '@/lib/form-store';
import { registrationSteps } from '@/lib/steps';
import {
  Stepper,
  StepperIndicator,
  StepperItem,
  StepperSeparator,
  StepperTitle,
  StepperTrigger,
} from '@repo/design-system/components/ui/stepper';

export function RegistrationStepper() {
  const { currentStep, setCurrentStep } = useFormStore();

  // Handle step change from the stepper
  const handleStepChange = (step: number) => {
    // Convert 1-based step number back to 0-based index
    const targetStep = step - 1;

    // Only allow navigation to previous steps or the current step
    if (targetStep <= currentStep) {
      setCurrentStep(targetStep);
    }
  };

  return (
    <Stepper
      value={currentStep + 1}
      onValueChange={handleStepChange}
      className="my-8 flex md:hidden"
    >
      {registrationSteps.map(({ step, mobileTitle: title }) => (
        <StepperItem
          key={step}
          step={step}
          className="not-last:flex-1 max-md:items-start"
          // Disable steps that are ahead of the current step
          disabled={step - 1 > currentStep}
        >
          <StepperTrigger className="flex-1 rounded max-md:flex-col">
            <StepperIndicator />
            <div className="text-center md:text-left">
              <StepperTitle className="text-xs">{title}</StepperTitle>
            </div>
          </StepperTrigger>
          {step < registrationSteps.length && (
            <StepperSeparator className="max-md:mt-3.5 md:mx-4" />
          )}
        </StepperItem>
      ))}
    </Stepper>
  );
}
