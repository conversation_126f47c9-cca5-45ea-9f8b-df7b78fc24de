'use client';

import { useFormStore } from '@/lib/form-store';
import { registrationSteps } from '@/lib/steps';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import { toast } from '@repo/design-system/components/ui/sonner';
import { BusinessDetailsStep } from './steps/business-details-step';
import { ContactDetailsStep } from './steps/contact-details-step';
import { IdInformationStep } from './steps/id-information-step';
import { SummaryStep } from './steps/summary-step';

export function RegistrationForm() {
  const { currentStep, reset } = useFormStore();

  // Handle reset form
  const handleReset = () => {
    reset();
    toast.success('Form has been reset. You can start over.');
  };

  // Render the current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <IdInformationStep />;
      case 1:
        return <ContactDetailsStep />;
      case 2:
        return <BusinessDetailsStep />;
      case 3:
        return <SummaryStep />;
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{registrationSteps[currentStep].title}</CardTitle>
          <CardDescription>
            {registrationSteps[currentStep].description}
          </CardDescription>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              Start Over
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reset Form</DialogTitle>
              <DialogDescription>
                Are you sure you want to reset the form? All your data will be
                lost.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline">Cancel</Button>
              </DialogClose>
              <DialogClose asChild>
                <Button variant="destructive" onClick={handleReset}>
                  Reset
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>{renderStepContent()}</CardContent>
    </Card>
  );
}
