import { env } from '@/env';
import { extractSubdomain } from '@/lib/organization';
import { internationalizationMiddleware } from '@repo/internationalization/middleware';
import {
  noseconeMiddleware,
  noseconeOptions,
  noseconeOptionsWithToolbar,
} from '@repo/security/middleware';
import { NextRequest, NextResponse } from 'next/server';

export const config = {
  // matcher tells Next.js which routes to run the middleware on. This runs the
  // middleware on all routes except for static assets and Posthog ingest
  matcher: ['/((?!_next/static|_next/image|ingest|favicon.ico).*)'],
};

// Root domain for the application
// Extract the hostname from the NEXT_PUBLIC_WEB_URL for use as the root domain
const appUrl = new URL(env.NEXT_PUBLIC_WEB_URL);
const ROOT_DOMAIN = appUrl.host;

/**
 * Middleware function that handles:
 * 1. Organization/subdomain resolution
 * 2. Internationalization
 * 3. Security headers
 *
 * This is a public portal, so we don't need authentication
 */
export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const subdomain = extractSubdomain(request, ROOT_DOMAIN);

  // If we have a subdomain, we need to handle it specially
  if (subdomain) {
    // Check if the path already starts with a locale and subdomain pattern
    const pathParts = pathname.split('/').filter(Boolean);
    const isAlreadyRewritten =
      pathParts.length >= 2 && pathParts[1] === subdomain;

    // redirect with subdomain
    if (pathname.startsWith('/api/')) {
      return NextResponse.next({
        headers: {
          'X-Organization': subdomain,
        },
      });
    }

    if (!isAlreadyRewritten) {
      // Get the locale from the internationalization middleware
      // We'll create a mock request to see what locale would be chosen
      const mockRequest = new NextRequest(
        new URL('/en', request.url), // Use a dummy locale path
        { headers: request.headers }
      );

      const i18nResponse = internationalizationMiddleware(mockRequest);
      let locale = 'en'; // Default locale

      if (i18nResponse.url) {
        // Extract the locale from the i18n response URL
        const i18nUrl = new URL(i18nResponse.url);
        const i18nPathParts = i18nUrl.pathname.split('/');
        if (i18nPathParts.length > 1 && i18nPathParts[1]) {
          locale = i18nPathParts[1];
        }
      }

      // Rewrite to the subdomain page with the correct locale, preserving the original path
      const newPath = `/${locale}/${subdomain}${pathname}`;
      return NextResponse.rewrite(new URL(newPath, request.url));
    }
  }

  // For all other paths (main domain or already rewritten), handle internationalization first
  const i18nResponse = internationalizationMiddleware(request);
  if (i18nResponse) {
    return i18nResponse;
  }

  // Apply security headers
  const securityHeaders = env.FLAGS_SECRET
    ? noseconeMiddleware(noseconeOptionsWithToolbar)
    : noseconeMiddleware(noseconeOptions);

  return securityHeaders();
}
